import type { Address, Country, Customer, GeneratedTicket, SalesPeriod, ShortEmployee, Ticket } from '..';
import type { ChangeHistoryMap, Currency } from '../..';

export type TicketOrderType = 'import' | 'manual' | 'boxOffice' | 'onlineShop' | 'offlineShop' | 'promoter';
export type TicketOrderStatus = 'completed' | 'cancelled';

// Type for Create
// AUTO: ticketOrderId, vatList, currency[symbol], position[positionNumber, positionPrice, 
// salesPeriod.salesPeriodName], totalPrice[priceNet, vatValue], orderDate, changeHistory,
// customer.address.country.iso3166Name
// OPTIONAL-AUTO: position[name, description, singlePrice, positionType, generatedTickets], orderNumber
export type TicketOrderCreate = Pick<TicketOrder,
    'eventId'
    | 'orderType'
> & Partial<Pick<TicketOrder,
    'generalTermsAndConditionsAccepted'
    | 'customFields'
    | 'eventEmployee'
    | 'orderNumber'
    | 'status'
    | 'ticketImportId'
    | 'description'
>> & {
    positions: {
        [positionNumber: number]: Pick<TicketOrderPosition,
            'quantity'
        > & Partial<Pick<TicketOrderPosition,
            'name'
            | 'description'
            | 'positionType'
        >> & {
            salesPeriod?: Pick<SalesPeriod, 'salesPeriodId'>;
            ticket?: Pick<Ticket, 'ticketId'>;
            singlePrice?: Pick<VatPrice, 'priceGross'>;
        };
    };
    currency: Pick<Currency, 'iso4217Code'>;
    totalPrice: Pick<VatPrice, 'priceGross'>;
    customer: Omit<Customer, 'address'> & {
        address?: Omit<Address, 'country' | 'additionalInformation'> & {
            country: Pick<Country, 'iso3166Alpha2Code'>;
        };
    };
}

export type TicketOrderCreateManual = Omit<TicketOrderCreate, 'eventEmployee'> & {
    eventEmployee: {
        userSubject: string;
    };
}


// Full Type for readAccess
export type TicketOrder = {
    eventId: string;
    ticketOrderId: string;
    orderNumber: string; // Human readable
    orderType: TicketOrderType;
    status: TicketOrderStatus;
    positions: {
        [positionNumber: number]: TicketOrderPosition;
    };
    vatList: VatList;
    currency: Currency;
    totalPrice: Pick<VatPrice, 'priceGross' | 'priceNet' | 'vatValue'>;
    orderDate: string;
    changeHistory: ChangeHistoryMap;
    eventEmployee?: ShortEmployee; // Only if orderType != webshop
    generalTermsAndConditionsAccepted?: true; // Only if configured
    customFields: { // Always present, default emplty
        [customFieldId: string]: TicketOrderCustomField;
    }; // Only if configured
    customer: Customer;
    ticketImportId?: string;
    description: string;
}

export type TicketOrderCustomField = {
    customFieldId: string;
    fieldName: string;
    fieldValue: string;
}

export type TicketOrderPositionType = 'ticket';

export type TicketOrderPosition = TicketOrderPositionTicket & {
    name: string;
    description: string;
    positionNumber: number;
    singlePrice: VatPrice;
    positionPrice: VatPrice;
    quantity: number;
    positionType: TicketOrderPositionType;
    salesPeriod: Pick<SalesPeriod, 'salesPeriodId' | 'salesPeriodName'>;
    ticketOrderPositionKey: string;
}

export type TicketOrderPositionTicket = {
    ticket: Pick<Ticket, 'ticketId' | 'ticketName'>;
}

export type ShortGeneratedTicket = Pick<GeneratedTicket, 'code' | 'generatedTicketId' | 'generatedTicketName'>;

export type VatList = {
    [vatPercent: number]: VatPrice;
}
export type VatPrice = {
    vatValue: number;
    vatPercent: number;
    priceNet: number;
    priceGross: number;
}