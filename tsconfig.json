{
  "compilerOptions": {
    // "allowSyntheticDefaultImports": true,
    // "baseUrl": ".",
    // "declaration": false,
    // "esModuleInterop": true,
    // "experimentalDecorators": true,
    // "module": "es2015",
    // "module": "CommonJS",
    // "target": "ES2020",
    // "target": "es2015",
    // "strictPropertyInitialization": false,
    // "suppressImplicitAnyIndexErrors": true,
    // "noLib": false,
    // "sourceMap": true,
    "target": "esnext",
    "module": "esnext",
    // this enables stricter inference for data properties on `this`
    "strict": true,
    // "jsx": "preserve",
    // "jsx": "react",
    "moduleResolution": "node",
    "allowJs": true,
    "noImplicitAny": false,
    // "isolatedModules": true,
    // "importHelpers": true, // https://stackoverflow.com/questions/63724523/how-to-add-typescript-to-vue-3-and-vite-project
    // "isolatedModules": true,
    // "noEmit": true
    "baseUrl": ".", // This must be specified if "paths" is.
    "paths": { // Required because of vetur errors
      "@innevent/*": [
        "packages/*"
      ]
    },
    // "paths": {
    //   "*" : [""]
    // //   "@api/*": ["src/api/*"], // This mapping is relative to "baseUrl"
    // //   "@types-helper": ["src/types/helper"],
    // //   "@types-model": ["src/types/model"],
    // //   "@components/": ["src/components/*"],
    // //   "@util": ["src/util"]
    // },
    "outDir": "./built",
    // "esModuleInterop": true,
    // "types": [
    //   "./vue-shim.d.ts"
    // ]
    "forceConsistentCasingInFileNames": false // not sure if needed, added for .vue files
  },
  "compileOnSave": false,
  // "include": [
  // //   "./src/**/*",
  //   // "./**/*.vue",
  // //   "**/*"
  // ],
  "exclude": [
    "./src/js/*",
    "./built/**/*",
    "./node_modules"
  ],
  "include": [
    "./**/*.d.ts",
    "./**/*.ts",
    "./**/*.vue",
  ],
}