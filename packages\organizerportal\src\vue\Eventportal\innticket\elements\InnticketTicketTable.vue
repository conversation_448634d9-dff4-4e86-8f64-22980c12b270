<template>
  <div>
    <b-row>
      <b-col>
        <h2>Tickets</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto"
            variant="primary"
            size="sm"
            @click="btnCreateTicket()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Ticket erstellen
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      id="ticketTable"
      ref="ticketTable"
      hover
      :items="ticketMapRef.tickets"
      :fields="tableFields"
      :busy="isLoadingRef"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteTicket($event, data)"
          />
        </div>
      </template>
      <template #cell(personalizationRequired)="{value}">
        {{ value == true ? 'Erforderlich' : '<PERSON>cht erforderlich' }}
      </template>
      <template #cell(ticketGroups)="{value}">
        <b-badge
          v-for="(ticketGroup, key) in value"
          :key="key"
          variant="secondary"
          class="mr-1"
        >
          {{ getTicketGroupName(key) }}
        </b-badge>
      </template>
    </b-table>
    <TicketSidebar 
      ref="ticketSidebar"
      @ticketChanged="onTicketChange"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import TicketSidebar from './InnticketTicketSidebar.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton, Ticket } from '@innevent/webapp-types';
import { formatDate } from '@innevent/webapp-utils';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useTicketMapState } from '../../../../states/ticketMap';


export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		TicketSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const ticketTable = ref<BTable>();
		const ticketSidebar = ref<InstanceType<typeof TicketSidebar>>();
		const { ticketMapRef, isLoadingRef } = useTicketMapState();
		
		const tableFields: BvTableFieldArray =  [
			{ key: 'ticketName', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'numberOfActiveGeneratedTickets', label: 'Anzahl' },
			{ key: 'ticketCodePrefix', label: 'Präfix' },
			{ key: 'personalizationRequired', label: 'Personalisierung' },
			{ key: 'ticketGroups', label: 'Ticketgruppen' },
			{ key: 'buttons', label: '' }
		];

		async function btnDeleteTicket(btn: ResetButton, cellData: BvTableCellData<Ticket>) {
			try {
				await api.deleteTicket({
					key: {
						eventId: cellData.item.eventId,
						ticketId: cellData.item.ticketId
					}
				});
				ticketMapRef.value.tickets = ticketMapRef.value.tickets.filter(item => (item.ticketId !== cellData.item.ticketId));
				onTicketChange();
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(ticket: Ticket): void {
			const ticketObj: Ticket = JSON.parse(JSON.stringify(ticket));
			ticketSidebar.value?.openForEdit(ticketObj);
		}

		function onTicketChange() {
			ticketTable.value?.refresh();
		}

		function btnCreateTicket(): void {
			ticketSidebar.value?.openForCreate();
		}

		function getTicketGroupName(id): string {
			return ticketMapRef.value.ticketGroups.find(ticketGroup => ticketGroup.ticketGroupId == id)?.ticketGroupName ?? 'Not found';
		}
		

		return {
			tableFields,
			ticketSidebar,
			ticketTable,
			ticketMapRef,
			isLoadingRef,
			btnDeleteTicket,
			onTicketChange,
			tableRowClick,
			btnCreateTicket,
			formatDate,
			getTicketGroupName
		};
	}
});
</script>

<style>
</style>
