<template>
  <b-modal
    v-if="ecTerminalTransactionRef"
    v-model="modalOpenRef"
    title="Transaktion hinzufügen"
  >
    <div role="group">
      <label for="input1">Betrag:</label>
      <b-input-group
        id="input1"
        append="€"
      >
        <b-form-input
          v-model.number="ecTerminalTransactionRef.creditChange"
          type="number"
          @keyup.enter="onKeyupEnter()"
        />
      </b-input-group>
    </div>
    <div
      role="group"
      class="mt-3"
    >
      <label for="input2">Kommentar:</label>
      <b-form-input
        id="input2"
        v-model="ecTerminalTransactionRef.comment"
        :state="ecTerminalTransactionRef.comment.length > 2"
        aria-describedby="input2feedback"
        placeholder="Kommentar"
        trim
        @keyup.enter="onKeyupEnter()"
      />
      <b-form-invalid-feedback id="input2feedback">
        Es werden mindestens 3 Buchstaben zur Beschreibung erwartet
      </b-form-invalid-feedback>
    </div>
    <template #modal-footer="{ hide }">
      <LoadButton
        ref="createTransactionButtonRef"
        variant="primary"
        size="sm"
        icon="save"
        text="Transaktion erstellen"
        @click="btnCreateTransaction($event, hide)"
      />
    </template>
  </b-modal>
</template>

<script lang="ts">
import type { Ref } from '@vue/composition-api';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import LoadButton from '../../../buttons/LoadButton.vue';
import { currentEventRef, useEcTerminalState } from '../../../../states';
import type { ResetButton, ModalAction } from '@innevent/webapp-types';
import type { EcTerminal, EcTerminalTransaction } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { ecterminal } from '@innevent/webapp-api';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const instance = getCurrentInstance();
		const modalOpenRef = ref<boolean>(false);
		const createTransactionButtonRef = ref<InstanceType<typeof LoadButton>>();
		const currentEcTerminalRef = ref<EcTerminal>();
		let ecTerminalTransactionsRef = ref<EcTerminalTransaction[]>([]);
		const { loadEcTerminals } = useEcTerminalState();
		const ecTerminalTransactionRef = ref<Partial<EcTerminalTransaction>>({
			comment: '',
			creditChange: 0
		});
		
		function onKeyupEnter() {
			createTransactionButtonRef.value?.clickButton();
		}

		function openForCreate(ecTerminal: EcTerminal, transactionRef: Ref<EcTerminalTransaction[]>) {
			actionRef.value = 'create';
			ecTerminalTransactionRef.value!.comment = '';
			ecTerminalTransactionRef.value!.creditChange = 0;
			currentEcTerminalRef.value = ecTerminal;
			modalOpenRef.value = true;
			ecTerminalTransactionsRef = transactionRef;
		}
		
		async function btnCreateTransaction(btn: ResetButton) {
			try {
				const ecTerminalTransaction = await ecterminal.addTransaction({
					key: {
						ecTerminalId: currentEcTerminalRef.value!.ecTerminalId!,
						eventId: currentEcTerminalRef.value!.eventId!
					},
					data: {
						creditChange: ecTerminalTransactionRef.value!.creditChange!,
						comment: ecTerminalTransactionRef.value!.comment!
					}
				});
				ecTerminalTransactionsRef.value.push(ecTerminalTransaction);
				await loadEcTerminals({
					key: {
						eventId: currentEventRef.value!.eventId!
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				console.log(error);
				notifyError({ instance, error });
			}
			btn.reset();
			modalOpenRef.value = false;
		}
	

		return {
			onKeyupEnter,
			modalOpenRef,
			openForCreate,
			ecTerminalTransactionRef,
			btnCreateTransaction,
			createTransactionButtonRef
		};
	}
});
</script>
