<template>
  <div>
    <b-row>
      <b-col><h2>Einstellungen</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="Einstellungen speichern"
          @click="btnSaveSettings"
        />
      </b-col>
    </b-row>
    <b-container>
      <b-row
        v-for="(field, fieldKey) in inputFieldsRef"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col
          sm="6"
        >
          <label
            :for="`field-${fieldKey}`"
          >{{ field.name }}:</label>
        </b-col>
        <b-col
          sm="6"
        >
          <b-form-select
            v-if="field.type == 'dropdown'"
            v-model="field.value"
            :state="isValidOrganizer"
            :options="field.dropdownValues"
            text-field="organizationName"
            value-field="organizerId"
            style="width: 300px"
          />
          <b-form-checkbox
            v-else-if="field.type == 'switch'"
            v-model="field.value"
            name="check-button"
            switch
            size="lg"
          />
          <b-input-group
            v-else-if="field.type == 'decimal'"
            :append="field.currency"
            style="width: 300px"
          >
            <b-form-input
              :id="`field-${fieldKey}`"
              v-model="field.value"
              type="number"
            />
          </b-input-group>
          <b-form-input
            v-else
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            style="width: 300px"
          />
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted, ref } from '@vue/composition-api';
import LoadButton from '../../buttons/LoadButton.vue';
import { currentEventRef } from '../../../states/eventState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { ResetButton } from '@innevent/webapp-types';
import { useEventState } from '../../../states/eventState';
import type { EventSettingsInnOrderRoundOptions } from '@innevent/types';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const instance = getCurrentInstance();
		const { updateEvent } = useEventState();

		const topdownRoundValues = {
			'NO_ROUND':	'Nicht runden',
			'ROUND_DOWN_FULL_EURO':	'Auf vollen Euro abrunden',
			'ROUND_DOWN_FULL_5EURO':	'Auf 5er Multiplikat abrunden',
			'ROUND_DOWN_FULL_10EURO':	'Auf 10er Multiplikat abrunden'
		};

		onMounted(async ()  => {
			try {
				const settings = currentEventRef.value?.settings.innorder;
				Object.keys(inputFieldsRef.value).forEach(key => {
					inputFieldsRef.value[key].value = settings![key];
				});
			} catch (error: any) {
				notifyError({ instance, error });
			}
		});

		async function btnSaveSettings(btn: ResetButton) {
			try {
				let eventSettings = currentEventRef.value!.settings;
				eventSettings.innorder.identifierDepositValue =  parseInt(inputFieldsRef.value.identifierDepositValue.value);
				eventSettings.innorder.reActivateIdentifier =  inputFieldsRef.value.reActivateIdentifier.value;
				eventSettings.innorder.minimumTopUpValueTokenstation =  parseInt(inputFieldsRef.value.minimumTopUpValueTokenstation.value);
				eventSettings.innorder.maximumTopUpValueTokenstation =  parseInt(inputFieldsRef.value.maximumTopUpValueTokenstation.value);
				eventSettings.innorder.maximumTopDownValueTokenstation =  parseInt(inputFieldsRef.value.maximumTopDownValueTokenstation.value);
				eventSettings.innorder.topDownRoundTokenstation =
							inputFieldsRef.value.topDownRoundTokenstation.value as EventSettingsInnOrderRoundOptions;

				await updateEvent({
					settings: eventSettings
				});
				notifySuccess({ instance, message: 'Einstellungen gespeichert!' });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		const inputFieldsRef = ref({
			minimumTopUpValueTokenstation: {
				name: 'Minimaler Betrag Aufladung Tokenstation',
				type: 'decimal',
				currency: '€',
				value: ''
			},
			maximumTopDownValueTokenstation: {
				name: 'Maximaler Betrag Abbuchung Tokenstation',
				type: 'decimal',
				currency: '€',
				value: ''
			},
			identifierDepositValue: {
				name: 'Pfand NFC-Tag',
				type: 'decimal',
				currency: '€',
				value: ''
			},
			maximumTopUpValueTokenstation: {
				name: 'Maximaler Betrag Aufladung Tokenstation',
				type: 'decimal',
				currency: '€',
				value: ''
			},
			topDownRoundTokenstation: {
				name: 'Abbuchung Runden',
				type: 'dropdown',
				dropdownValues: topdownRoundValues,
				value: ''
			},
			reActivateIdentifier: {
				name: 'NFC-Tag wiederverwendbar?',
				type: 'switch',
				value: false
			}
		});

		return {
			btnSaveSettings,
			inputFieldsRef
		};
	}
});
</script>
<style scoped>

</style>
