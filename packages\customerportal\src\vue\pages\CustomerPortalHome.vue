<template>
  <b-row class="justify-content-center">
    <b-col
      sm="6"
      lg="4"
      xxl="3"
    >
      <InnCashlessCard />
    </b-col>
    <b-col
      sm="6"
      lg="4"
      xxl="3"
    >
      <InnCashlessCard2 />
    </b-col>
  </b-row>
</template>
<script>

// Frameworks

// Vue Components
import InnCashlessCard from './HomeCards/InnCashlessCard.vue';
import InnCashlessCard2 from './HomeCards/InnCashlessCard2.vue';
import InnCashlessCard3 from './HomeCards/InnCashlessCard3.vue';

export default {
	components: { InnCashlessCard, InnCashlessCard2, InnCashlessCard3 },
	props: [],
	data() { return {
		selectedEventId: null
	};},
	computed: {
		currentEvent() {
			if (this.$route.params.event) return this.events.find(e => e.id == this.$route.params.event);
			else return null;
		}
	},
	watch: {

	},
	created() {

	},
	methods: {

	}
};

</script>
<style scoped>
.card-img-top {
    width: 100%;
    height: 15vw;
    object-fit: cover;
}
</style>
