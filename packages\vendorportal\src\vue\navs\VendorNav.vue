<template>
  <b-nav
    vertical
    pills
  >
    <b-nav-item
      v-for="item of navItems"
      :key="item.link"
      exact
      exact-active-class="active"
      :to="{ name: item.link }"
    >
      <FontAwesomeIcon
        :icon="item.icon"
        fixed-width
      /><span class="ml-3">{{ item.name }}</span>
    </b-nav-item>
    <b-nav-item
      v-for="eventProp of selectedVendor.VendorEventProperties"
      :key="eventProp.Event.id"
      exact
      exact-active-class="active"
      :to="{ name: 'eDashboard', params: { event: eventProp.Event.id }}"
    >
      <FontAwesomeIcon
        class="ml-4"
        icon="angle-right"
        fixed-width
      /><span class="ml-2">{{ eventProp.Event.name }}</span>
    </b-nav-item>
  </b-nav>
</template>
<script>

export default {
	components: {

	},
	props: ['selectedVendor'],
	data() { return {
		navItems: [
			{ name: 'Stammdaten', link: 'vBasedata', icon: 'building' },
			{ name: 'Artikelverwaltung', link: 'vArticles', icon: 'th-list' },
			{ name: '<PERSON><PERSON><PERSON><PERSON>', link: 'vEmployees', icon: 'users' },
			// { name: 'Kassen', link: 'vCashboxes', icon: 'cash-register' },
			// { name: 'EC-Terminals', link: 'vECTerminals', icon: 'credit-card' },
			{ name: 'Events', link: 'vEvents', icon: 'calendar-alt' }
		]
	};}
};
</script>
