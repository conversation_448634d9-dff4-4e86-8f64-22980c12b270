<template>
  <b-container class="mt-5 mb-5">
    <b-card
      v-if="invalidInvitation && !showResultAlert"
      bg-variant="danger"
      text-variant="white"
      header="Request Invalid"
      class="text-center"
    >
      <b-card-text><PERSON><PERSON> Link scheint fehlerhaft oder nicht mehr gültig zu sein.</b-card-text>
    </b-card>
    <b-card
      v-if="!invalidInvitation && !showResultAlert && invitation"
      title="Einladung zur Zusammenarbeit"
      sub-title="INN//Order"
    >
      <b-card-text>
        Hallo{{ cognitoUser && cognitoUser.attributes.given_name ? " " + cognitoUser.attributes.given_name:"" }},<br>du wurdest eingeladen, um zusammen Inn//Order nutzen zu können.
      </b-card-text>
      <b-card-text v-if="invitation && invitation.message">
        Folgende Nachricht wurde dir hinterlassen:<br>{{ invitation.message }}
      </b-card-text>
      <b-card-text>
        Bitte bestätige die Zusammenarbeit:
      </b-card-text>
      <b-container>
        <b-row>
          <b-col>
            <b-button
              block
              variant="primary"
              @click="answerInvitation('ACCEPTED')"
            >
              Einladung akzeptieren
            </b-button>
          </b-col>
          <b-col>
            <b-button
              block
              variant="outline-danger"
              @click="answerInvitation('DECLINED')"
            >
              Einladung ablehnen
            </b-button>
          </b-col>
        </b-row>
      </b-container>
    </b-card>
    <b-card
      v-if="showResultAlert"
      class="text-center"
    >
      <b-alert
        show
        :variant="variantResultAlert"
      >
        {{ textResultAlert }}
      </b-alert>
    </b-card>
  </b-container>
</template>
<script>

import { Auth, API } from 'aws-amplify';

export default {
	props: ['cognitoUser'],
	data() { return {
		invitation: {},
		invalidInvitation: false,
		showResultAlert: false,
		textResultAlert: '',
		variantResultAlert: 'success'
	};},
	computed: {

	},
	created() {
		this.loadInvitation();
	},
	methods: {
		async loadInvitation() {
			try {
				this.invitation = await API.get('innorderAnonym', '/VendorStaffInvitation/' + this.$route.params.invitationId);
				if (this.invitation.status !== 'CREATED') throw 'Einladung ungültig';
			} catch (error) {
				this.invalidInvitation = true;
			}
		},
		async answerInvitation(status) {
			let item = {
				status: status
			};
      
			try {
				await API.put('innorder', '/VendorStaffInvitation/' + this.$route.params.invitationId, { body: item });

				this.showResultAlert = true;
				this.textResultAlert = 'Deine Antwort wurde erfolgreich übertragen. Danke!';
			} catch (error) {
				this.showResultAlert = true;
				this.variantResultAlert = 'danger';
				this.textResultAlert = 'Es ist ein Fehler aufgetreten, bitte versuchen Sie es später erneut';
			}
		},
		logout() {
			Auth.signOut();
			location.reload();
		},
		loggedIn() {
			location.reload();
		}
	}
};
</script>
<style scoped>

</style>
