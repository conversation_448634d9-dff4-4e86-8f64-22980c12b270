import type { PermissionInnEvent } from '@innevent/types';
import type { ComputedRef } from '@vue/composition-api';
import { computed } from '@vue/composition-api';
import { useEmployeeState } from './employeeState';
import { permissionState as log } from '../loglevel';

log.debug('INIT permissionState');

const { currentEventEmployee, currentOrganizerEmployee }  = useEmployeeState();

const permittedEventPermissions: ComputedRef<PermissionInnEvent[]> = computed(() => {
	log.debug('COMPUTED permittedEventPermissions START');
	if (!currentEventEmployee.value) {
		log.debug('COMPUTED permittedOrganizerPermissions END', []);
		return [];
	}
	const permittedEventPermissions = Object.keys(currentEventEmployee.value.permissions) as PermissionInnEvent[];
	log.debug('COMPUTED permittedEventPermissions END', permittedEventPermissions);
	return permittedEventPermissions;
});

const permittedOrganizerPermissions: ComputedRef<PermissionInnEvent[]> = computed(() => {
	log.debug('COMPUTED permittedOrganizerPermissions START');
	if (!currentOrganizerEmployee.value) {
		log.debug('COMPUTED permittedOrganizerPermissions END', []);
		return [];
	}
	const permittedOrganizerPermissions = Object.keys(currentOrganizerEmployee.value.permissions) as PermissionInnEvent[];
	log.debug('COMPUTED permittedOrganizerPermissions END', permittedOrganizerPermissions);
	return permittedOrganizerPermissions;
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function usePermissionState() {
	log.debug('usePermissionState()');

	return {
		permittedEventPermissions,
		permittedOrganizerPermissions
	};
}