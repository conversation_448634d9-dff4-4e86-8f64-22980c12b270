import type { TicketOrderMap } from '@innevent/webapp-types';
import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { useEmployeeState } from '.';

const isLoadingRef = ref<boolean>(false);
const ticketOrderMapRef = ref<TicketOrderMap | undefined>();
const { eventEmployeesRef } = useEmployeeState();

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useTicketOrderMap() {
	return {
		ticketOrderMapRef,
		isLoadingRef
	};
}

export async function refreshTicketOrderMap(eventId: string): Promise<void> {
	isLoadingRef.value = true;
	ticketOrderMapRef.value = {
		ticketOrders: await api.getTicketOrders({
			key: { eventId },
			queryParameter: {
				itemLimit: 25
			}
		}),
		tickets: await api.getTickets({
			key: { eventId }
		}),
		salesPeriods: await api.getSalesPeriods({
			key: { eventId }
		}),
		eventEmployees: eventEmployeesRef.value
	};
	isLoadingRef.value = false;
}