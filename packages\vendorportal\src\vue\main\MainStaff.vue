<template>
  <div>
    <b-row>
      <b-col><h2><PERSON><PERSON><PERSON><PERSON></h2></b-col>
      <b-col>
        <b-form-input
          v-model="queryEmployee"
          type="search"
          placeholder="Suche..."
          @update="refreshEmployeeList()"
        />
      </b-col>
    </b-row>
    <VendorEmployeeSidebar
      v-if="sidebarVisible"
      v-model="sidebarVisible"
      :selected-vendor-id="selectedVendor.id"
      :vendor-employee="vendorEmployeeSidebar"
      @close="closeSidebar"
    />
    <b-table
      id="assignedEmployeeTable"
      ref="tabelVendorUsers"
      hover
      small
      :items="getVendorEmployee"
      :fields="tableFieldsEmployee"
      :current-page="paginationEmpl.currentPage"
      :per-page="paginationEmpl.perPage"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="editStaffData"
    >
      >
      <template #cell(index)="data">
        {{ data.index + 1 }}
      </template>
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(buttons)="data">
        <DeleteButton
          v-if="!hasOwnerPermission(data.item) && data.item.isTechnicalUser == false"
          size="sm"
          class=""
          icon="trash"
          text="Entfernen"
          variant="danger"
          @click="deleteFromList($event, data.item)"
        />
        <SaveButton
          v-if="data.item.isChanged"
          :data="data"
          size="sm"
          @click="saveVendorEmployee"
        />
      </template>
    </b-table>
    <b-pagination
      v-model="paginationEmpl.currentPage"
      :per-page="paginationEmpl.perPage"
      :total-rows="paginationEmpl.rows"
      align="center"
    />
    <TechnicalUserTable
      ref="technicalUserTable"
      :referenceObjectId="selectedVendor.id"
      reference-object-type="vendor"
      @technicalUserChanged="onTechnicalUserChanged()"
    />
    <div style="height: 50px" />
    <div>
      <b-row>
        <b-col><h2>Mitarbeitereinladungen</h2></b-col>
        <b-col>
          <b-form-input
            v-model="queryEmployeeInvite"
            type="search"
            placeholder="Suche..."
            @update="reloadEmployeeInvite()"
          />
        </b-col>
      </b-row>
    </div>
    <label class="typo__label">Filter nach Status</label>
    <Multiselect
      v-model="filterSelection"
      :options="filterOptions"
      :multiple="true"
      placeholder="Auswählen"
      label="name"
      track-by="name"
      :allow-empty="false"
      :preselect-first="true"
      @select="reloadEmployeeInvite"
      @remove="reloadEmployeeInvite"
    />
    <b-table
      id="invitedEmployeeTable"
      ref="tabelVendorUsersInvite"
      hover
      small
      :items="getVendorStaffInvitations"
      :fields="tableFieldsInvitations"
      :current-page="pagInvitations.currentPage"
      :per-page="pagInvitations.perPage"
      class="mt-3 table-clickable"
      head-variant="light"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(createdAt)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(status)="data">
        <b-badge
          v-if="data.value == 'ACCEPTED'"
          variant="success"
        >
          Akzeptiert
        </b-badge>
        <b-badge
          v-else-if="data.value == 'CREATED'"
          variant="secondary"
        >
          Versendet
        </b-badge>
        <b-badge
          v-else-if="data.value == 'DECLINED'"
          variant="danger"
        >
          Abgelehnt
        </b-badge>
      </template>
      <template #cell(buttons)="data">
        <DeleteButton
          size="sm"
          class=""
          icon="trash"
          text="Entfernen"
          variant="danger"
          @click="deleteInvitationFromList($event, data)"
        />
      </template>
    </b-table>
    <b-pagination
      v-model="pagInvitations.currentPage"
      :per-page="pagInvitations.perPage"
      :total-rows="pagInvitations.rows"
      align="center"
    />
    <div class="d-flex">
      <b-button
        v-b-modal.inviteModal
        class="ml-auto"
        variant="primary"
        size="sm"
      >
        <FontAwesomeIcon icon="plus-square" /> Mitarbeiter einladen
      </b-button>
    </div>
    <b-modal
      id="inviteModal"
      title="Mitarbeiter einladen"
      centered
    >
      <div role="group">
        <label for="inviteEmail">E-Mail Adresse:</label>
        <b-form-input
          id="inviteEmail"
          v-model="inviteEmail"
          :state="inviteEmailState"
          aria-describedby="inviteEmail-help inviteEmail-feedback"
          placeholder="<EMAIL>"
        />
        <b-form-invalid-feedback id="inviteEmail-feedback">
          Die Email Adresse ist ungültig
        </b-form-invalid-feedback>
        <b-form-text id="inviteEmail-help">
          Bitte die Emailadresse der einzuladenden Person eingeben
        </b-form-text>
      </div>
      <div
        role="group"
        class="mt-3"
      >
        <label for="inviteText">Nachricht (optional):</label>
        <b-form-textarea
          id="inviteText"
          v-model="inviteText"
          placeholder="Gib eine kurze Nachricht ein..."
          rows="3"
          max-rows="6"
        />
      </div>
      <template #modal-footer>
        <div class="d-flex">
          <LoadButton
            class="ml-auto"
            text="Einladen"
            icon="plus-square"
            :disabled="!inviteEmailState"
            @click="sendInvitation"
          />
        </div>
      </template>
    </b-modal>
  </div>
</template>
<script lang="ts">
import { API } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';
import SaveButton from '../buttons/SaveButton.vue';
import DeleteButton from '../buttons/DeleteButton.vue';
import Multiselect from 'vue-multiselect';
import VendorEmployeeSidebar from './elements/VendorEmployeeSidebar.vue';
import TechnicalUserTable from '@innevent/webapp-components/user/TechnicalUserTable.vue';
import { defineComponent, ref } from '@vue/composition-api';
import { formatDate } from '@innevent/webapp-utils';

import type { BTable } from 'bootstrap-vue';

export default defineComponent({
	components: {
		LoadButton,
		DeleteButton,
		Multiselect,
		SaveButton,
		VendorEmployeeSidebar,
		TechnicalUserTable
	},
	props: [
		'selectedVendor',
		'events',
		'eventProperties'
	],
	setup() {
		const tabelVendorUsers = ref<BTable>();
		function onTechnicalUserChanged() {
			tabelVendorUsers.value?.refresh();
		}

		return {
			formatDate,
			onTechnicalUserChanged,
			tabelVendorUsers
		};
	},
	data() { return {
		sidebarVisible: false,
		vendorEmployeeSidebar: 1,
		paginationEmpl: {
			currentPage: 1,
			perPage: 30,
			rows: 0
		},
		pagInvitations: {
			currentPage: 1,
			perPage: 20,
			rows: 0
		},
		queryEmployee: '',
		queryEmployeeInvite: '',

		userList: null,
		tableFieldsEmployee: [
			{ key: 'index', label: 'Nr', sortable: true },
			{ key: 'firstname', label: 'Vorname', sortable: true },
			{ key: 'lastname', label: 'Nachname', sortable: true },
			{ key: 'position', label: 'Position', sortable: true },
			{ key: 'isTechnicalUser', label: 'Technischer User', sortable: true, formatter: value => value ? 'Ja' : '' },
			{ key: 'buttons', label: '' }
		],
		tableFieldsInvitations: [
			{ key: 'mail', label: 'E-Mail', sortable: true },
			{ key: 'status', label: 'Status', sortable: true },
			{ key: 'message', label: 'Nachricht' },
			{ key: 'createdAt', label: 'Erstellt am', sortable: true },
			{ key: 'buttons', label: '' }
		],
		inviteEmail: '',
		inviteText: '',
		filterOptions: [
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		],
		filterSelection: [
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		]
	};},
	computed: {
		inviteEmailState() {
			return this.isEmailValid(this.inviteEmail);
		}
	},
	methods: {
		editStaffData(item) {
			this.vendorEmployeeSidebar = item;
			this.sidebarVisible = true;
		},
		closeSidebar() {
			this.sidebarVisible = false;
		},
		refreshEmployeeList() {
			this.$refs.tabelVendorUsers.refresh();
		},
		reloadEmployeeInvite() {
			this.$refs.tabelVendorUsersInvite.refresh();
		},
		dataChange(data) {
			this.$set(this.userList[data.index], 'isChanged', true);
		},
		async getVendorEmployee(ctx) {
			let params = new URLSearchParams();
			params.append('include', '["PermVendorUser"]');
			params.append('filterVendor', this.selectedVendor.id);
			params.append('currentPage', (ctx.currentPage - 1));
			params.append('pageSize', ctx.perPage);
			if (this.queryEmployee) {
				params.append('query', this.queryEmployee);
			}
			return await API.get('innorder', '/VendorEmployee?' + params.toString()).then(response  => {
				this.paginationEmpl.rows = response.count;
				return response.rows;
			}).catch(() => {
				this.$bvToast.toast('Beim Laden der MitarbeiterDaten ist ein Fehler aufgetreten.', {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		async getVendorStaffInvitations(ctx) {
			let params = new URLSearchParams();
			params.append('include', '["PermVendorUser"]');
			params.append('filterVendor', this.selectedVendor.id);
			params.append('currentPage', (ctx.currentPage - 1));
			params.append('pageSize', ctx.perPage);
			if (this.queryEmployeeInvite) {
				params.append('query', this.queryEmployeeInvite);
			}
			if (this.filterSelection) {
				params.append('filterStatus', this.filterSelection.map(filter => filter.key));
			}
			return await API.get('innorder', '/VendorStaffInvitation?' + params).then(response  => {
				this.pagInvitations.rows = response.count;
				return response.rows;
			}).catch(() => {
				this.$bvToast.toast('Beim Laden der MitarbeiterDaten ist ein Fehler aufgetreten.', {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		sendInvitation(btn) {
			let item = {
				mail: this.inviteEmail,
				status: 'CREATED',
				message: this.inviteText,
				// eslint-disable-next-line @typescript-eslint/naming-convention
				VendorId: this.selectedVendor.id
			};
			API.post('innorder', '/VendorStaffInvitation', { body: item }).then(() => {
				this.$bvToast.toast('Die Einladung wurde erfolgreich versendet.', {
					title: 'Versenden Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
				this.$bvModal.hide('inviteModal');
				this.reloadEmployeeInvite();
			}).catch(() => {
				this.$bvToast.toast('Beim Senden der Einladung ist ein Fehler aufgetreten...', {
					title: 'Versenden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		deleteInvitationFromList(btn, data) {
			API.del('innorder', '/VendorStaffInvitation/' + data.item.id).then(()  => {
				this.$bvToast.toast('Die Einladung wurde erfolgreich gelöscht.', {
					title: 'Löschen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				this.reloadEmployeeInvite();
				btn.reset();
			}).catch(() => {
				this.$bvToast.toast('Beim Löschen der Einladung ist ein Fehler aufgetreten.', {
					title: 'Löschen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		isEmailValid(email) {
			const mailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
			if (!mailRegex.test(email)) { return false; }
			return true;
		},
		hasOwnerPermission(dataItem) {
			if (!dataItem.PermVendorUsers) return true;
			return dataItem.PermVendorUsers.some(permVendorUser => permVendorUser.permission == 'perm:innorder.vendor.owner');
		},
		async deleteFromList(btn, dataItem) {
			console.log(dataItem);
			await API.del('innorder', '/VendorEmployee/' + dataItem.id).then(()  => {
				this.$bvToast.toast('Die Berechtigung wurde erfolgreich gelöscht.', {
					title: 'Löschen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
			}).catch(() => {
				this.$bvToast.toast('Beim Löschen der Berechtigung ist ein Fehler aufgetreten.', {
					title: 'Löschen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
			btn.reset();
			this.refreshEmployeeList();
			this.$refs.technicalUserTable.$refs.technicalUserTable.refresh();
		}
	}
});
</script>
