<template>
  <b-container fluid>
    <TicketOrderTable />
  </b-container>
</template>
<script lang="ts">
import TicketOrderTable from './elements/InnticketTicketOrderTable.vue';
import { onMounted, defineComponent } from '@vue/composition-api';
import { refreshTicketOrderMap } from '../../../states/ticketOrder';
import { currentEventRef } from '../../../states/eventState';

export default defineComponent({
	components: {
		TicketOrderTable
	},
	setup() {
		onMounted(async () => {
			await refreshTicketOrderMap(currentEventRef.value!.eventId!);
		});
		
		return {  };
	}
	
});
</script>
