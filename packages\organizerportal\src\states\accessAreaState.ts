import type { AccessArea } from '@innevent/types';
import { accessArea as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { accessAreaState as log } from '../loglevel';
import Vue from 'vue';
import { currentEventRef } from './eventState';


log.debug('INIT accessAreaState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const accessAreasRef = ref<AccessArea[]>([]);

watch(accessAreasRef, () => {
	log.debug('WATCH accessAreasRef', accessAreasRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useAccessAreaState() {
	log.debug('useAccessAreaState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		accessAreasRef
	};
}

export async function loadAccessAreas(): Promise<void> {
	log.debug('loadAccessAreas()', currentEventRef.value);
	isLoadingRef.value = true;
	try {
		accessAreasRef.value = await api.listAccessAreas({ key: { eventId: currentEventRef.value!.eventId! } });
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type CreateAccessAreaStateOption = {
	key: Pick<AccessArea, 'eventId'>;
	data: Pick<AccessArea, 'accessAreaName' | 'description' | 'permittedTickets'>;
}
export async function createAccessArea(options: CreateAccessAreaStateOption): Promise<void> {
	log.debug('createAccessArea()', options);
	isLoadingRef.value = true;
	try {
		const accessAreas = await api.createAccessArea(options);
		accessAreasRef.value.push(accessAreas);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type UpdateAccessAreaStateOption = {
	key: Pick<AccessArea, 'eventId' | 'accessAreaId'>;
	data: Pick<AccessArea, 'accessAreaName' | 'description' | 'permittedTickets'>;
}
export async function updateAccessArea(options: UpdateAccessAreaStateOption): Promise<void> {
	log.debug('updateAccessArea()', options);
	isLoadingRef.value = true;
	try {
		const updatedAccessArea = await api.updateAccessArea(options);
		const index = accessAreasRef.value.findIndex((oldAccessArea) =>oldAccessArea.accessAreaId == updatedAccessArea.accessAreaId);
		if (index == -1) {
			log.debug('updateAccessArea()', 'Could not find AccessArea');
			return;
		}
		Vue.set(accessAreasRef.value, index, updatedAccessArea);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type DeleteAccessAreaStateOption = {
	key: Pick<AccessArea, 'eventId' | 'accessAreaId'>; 
}
export async function deleteAccessArea(options: DeleteAccessAreaStateOption): Promise<void> {
	log.debug('deleteAccessArea()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteAccessArea(options);
		accessAreasRef.value = accessAreasRef.value.filter((accessArea) => accessArea.accessAreaId !== options.key.accessAreaId);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}