<template>
  <b-row class="justify-content-center mt-5">
    <div class="innLoginForm">
      <div class="text-center mb-4">
        <img class="mb-4" src="../img/logo-transparent.png" alt="" width="150" height="150">
        <h1 class="h4 mb-3 font-weight-normal">{{ title }}</h1>
      </div>
      <div class="d-flex justify-content-center">
        <b-spinner v-if="loading" variant="primary" type="grow" style="width: 100px; height: 100px; margin: 50px;"></b-spinner>
      </div>
      <div>
        <b-alert v-if="errorMessage" show variant="danger">{{ errorMessage }}</b-alert>
        <b-alert v-if="successMessage" show variant="success">{{ successMessage }}</b-alert>
      </div>
      <b-button block variant="outline-primary" v-if="successMessage" to="/login">Anmelden</b-button>
    </div>
  </b-row>
</template>
<script>


import { Auth } from 'aws-amplify'
var URL = require('url-parse')

export default {
  data() { return{
    url: null,
    errorMessage: null,
    successMessage: null,
    title: 'Registrierung bestätigen',
    loading: true
  }},
  async created(){
    this.url = new URL(window.location.href, true)
    this.confirmSignUp()
  },
  methods: {
    async confirmSignUp(){
      var q = this.url.query
      if(!q || !q.confirmation_code || !q.user_name){
        this.errorMessage = 'URL Query Parameters invalid'
        this.loading = false
        return
      }
      try {
        await Auth.confirmSignUp(q.user_name, q.confirmation_code)
        this.success()
      } catch (error) {
        if(error.message.includes('status is CONFIRMED')) this.successMessage = 'Der Account wurde bereits bestätigt.'
        else if(error.message.includes('Attempt limit exceeded')) this.errorMessage = 'Zu viele Versuche, bitte versuche es später erneut.'
        else {
          console.log(error)
          this.errorMessage = error.message
        }
      }
      this.loading = false
    },
    async success(){
      this.successMessage = "Account und E-Mail Adresse wurden erfolgreich verifiziert"
      // if(this.urlParams.redirect_uri){
      //   var url = buildUrl(this.urlParams.redirect_uri, {})
      //   window.location.replace(url)
      // }
    }
  }
}
</script>
