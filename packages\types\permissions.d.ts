export type PermissionCore = 'perm:core.event.owner' | 'perm:core.event.admin' | 'perm:core.event.edit' | 'perm:core.event.show';
export type PermissionOrganizer = 'perm:core.organizer.owner' | 'perm:core.organizer.admin' | 'perm:core.organizer.edit' | 'perm:core.organizer.show';
export type PermissionInnticket = 'perm:innticket.event.show' | 'perm:innticket.event.edit';
export type PermissionInnAccess = 'perm:innaccess.event.show' | 'perm:innaccess.event.edit';
export type PermissionInnOrder =
    'perm:innorder.vendor.owner' |
    'perm:innorder.vendor.admin' |
    'perm:innorder.vendor.edit' |
    'perm:innorder.vendor.show' |
    'perm:innorder.tokenstation.use' |
    'perm:innorder.tokenstation.admin' |
    'perm:innorder.salesarea.use' |
    'perm:innorder.salesarea.admin';
export type PermissionRedemptionGroup = 'perm:innticket.redemptiongroup.use' | 'perm:innticket.redemptiongroup.admin';
export type PermissionInnEvent = PermissionCore | PermissionInnticket | PermissionRedemptionGroup | 
    PermissionInnOrder | PermissionOrganizer | PermissionInnAccess;
