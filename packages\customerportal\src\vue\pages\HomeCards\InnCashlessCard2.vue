<template>
  <b-card
    title="Guthaben und Transaktionen anzeigen"
  >
    <b-card-text>
      Are you at an event and want to see the balance on your token wristband?
      Go to TokenInfo and follow the instructions!
    </b-card-text>
    <b-card-text>
      Du bist auf einem Event und möchtest das Guthaben deiner deines Armbandes einsehen?
      Gehe zur TokenInfo und folge den Anweisungen!
    </b-card-text>
    <b-button
      to="/tokeninfo"
      variant="primary"
      block
    >
      Token-Info
    </b-button>
  </b-card>
</template>
<script>

// Frameworks

// Vue Components
// import Navigation from './Eventportal/Navigation.vue'

export default {
	components: { },
	props: [],
	data() { return {
		selectedEventId: null
	};},
	computed: {
		currentEvent() {
			if (this.$route.params.event) return this.events.find(e => e.id == this.$route.params.event);
			else return null;
		}
	},
	watch: {

	},
	created() {

	},
	methods: {

	}
};

</script>
<style scoped>

</style>
