<template>
  <div>
    <Header
      :containerClass="containerClass"
    ></Header>
    <b-container fluid :class="containerClass" class="pt-3">
      <div v-if="loading" class="d-flex justify-content-center mt-5">
        <b-spinner style="width: 5rem; height: 5rem;" variant="primary" class="float-right"></b-spinner>
      </div>
      <router-view/>
    </b-container>
  </div>
</template>
<script>

// Frameworks
import { Auth, API } from 'aws-amplify'

// Vue Components
import Header from './Header.vue'
import LoadingSpinner from './elements/LoadingSpinner.vue'

export default{
  props: ['cognitoUser'],
  components: { Header, LoadingSpinner },
  data() { return{
    loading: false,
    containerClass: 'max-w1600'
  }},
  created(){

  },
  watch: {

  },
  computed: {

  },
  methods:{

  }
}

</script>
