/* eslint-disable @typescript-eslint/naming-convention */
import type { Event, Vendor } from '.';

export type User = {
    id?: string;
}

export type Employee = {
    id?: string;
    firstname: string;
    lastname: string;
    position?: string;
    UserId: string;
    User?: User;
    isTechnicalUser: boolean;
}

export type EventEmployee = Employee & {
    EventId: string;
    Event?: Event;
}

export type VendorEmployee = Employee & {
    VendorId: string;
    Vendor?: Vendor;
}