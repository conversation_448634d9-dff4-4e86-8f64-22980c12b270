{"name": "@innevent/organizerportal", "version": "1.0.0", "scripts": {"dev": "webpack-dev-server", "build": "webpack --mode=production", "organizer": "webpack-dev-server", "lint": "eslint --ext .vue,.js,.ts src/", "lintfix": "eslint --ext .vue,.js,.ts src/ --fix"}, "private": true, "engines": {"npm": "please-use-yarn", "yarn": ">= 1.22.0"}, "author": "<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.10.0", "@babel/plugin-transform-typescript": "^7.13.0", "@babel/preset-env": "7.10.0", "@babel/preset-typescript": "^7.13.0", "@babel/runtime": "^7.13.10", "@fortawesome/fontawesome-common-types": "^6.1.1", "@fortawesome/fontawesome-free": "^6.1.1", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fortawesome/vue-fontawesome": "^2.0.6", "@vue/compiler-sfc": "3.0.9", "@vue/composition-api": "^1.0.0-rc.6", "autoprefixer": "^9.8.6", "aws-amplify": "^4.2.10", "babel-loader": "8.1.0", "babel-plugin-transform-regenerator": "^6.26.0", "babel-polyfill": "6.26.0", "bootstrap": "^4.6.0", "bootstrap-vue": "^2.21.2", "crypto-browserify": "^3.12.0", "css-loader": "3.6.0", "decimal.js": "^10.2.0", "dotenv-webpack": "^7.0.2", "file-loader": "^5.0.2", "html-webpack-plugin": "5.3.1", "http-status": "^1.4.1", "jquery": "^3.5.1", "luxon": "^1.25.0", "moment": "^2.29.0", "object-filter": "^1.0.2", "popper.js": "^1.16.0", "portal-vue": "^2.1.6", "postcss-loader": "^3.0.0", "sass": "1.32.0", "sass-loader": "^8.0.2", "ts-loader": "^8.1.0", "tsconfig-paths-webpack-plugin": "^3.5.1", "typescript": "^4.2.3", "validator": "^13.1.17", "vue": "2.6.12", "vue-color": "^2.7.1", "vue-ctk-date-time-picker": "^2.5.0", "vue-datetime": "^1.0.0-beta.14", "vue-loader": "15.9.8", "vue-multiselect": "^2.1.6", "vue-router": "3.5.1", "vue-style-loader": "^4.1.2", "vue-template-compiler": "2.6.12", "webpack": "^5.28.0", "webpack-cli": "^4.5.0", "webpack-dev-server": "^3.11.2", "webpackbar": "^5.0.0-3", "weekstart": "^1.0.1"}, "dependencies": {"@innevent/types": "1.2.1", "chart.js": "^2.9.4", "dotenv-webpack": "^7.0.2", "git-revision-webpack-plugin": "^3.0.6", "loglevel": "^1.7.1", "loglevel-plugin-prefix": "^0.8.4", "precss": "^4.0.0", "vue-chartjs": "^3.5.1"}}