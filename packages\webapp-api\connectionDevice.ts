import { apiInnEvent } from './instances';
import type { ConnectionDevice } from '@innevent/types';

type ModelType = ConnectionDevice;

export type GetOrganizerEmployeeOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getConnectionDevices(options: GetOrganizerEmployeeOptions): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/ConnectionDevice', { 
		params: { 
			eventId: options.key.eventId 
		} 
	});
	return response.data;
}