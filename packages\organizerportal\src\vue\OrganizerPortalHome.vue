<template>
  <b-container
    fluid
    class="mt-4"
  >
    <b-row>
      <b-col>
        <div class="d-flex justify-content-between align-items-center">
          <h2>Eventübersicht</h2>
          <b-button
            v-b-modal.modalCreateEvent
            variant="primary"
          >
            Event Erstellen
          </b-button>
        </div>
        <b-table
          striped
          hover
          :busy="isLoadingEventsRef"
          :items="permittedEventsRef"
          :fields="eventTableFields"
          class="table-clickable"
          @row-clicked="rowClickEvent"
        >
          <template #table-busy>
            <TableBusyLoader />
          </template>
        </b-table>
      </b-col>
      <b-col>
        <div class="d-flex justify-content-between align-items-center">
          <h2>Veranstalterübersicht</h2>
          <b-button
            v-b-modal.modalCreateOrganizer
            variant="primary"
          >
            Veranstalter Erstellen
          </b-button>
        </div>
        <b-table
          striped
          hover
          :busy="isLoadingOrganizersRef"
          :items="permittedOrganizersRef"
          :fields="organizerTableFields"
          class="table-clickable"
          @row-clicked="rowClickOrganizer"
        >
          <template #table-busy>
            <TableBusyLoader />
          </template>
        </b-table>
      </b-col>
    </b-row>
  </b-container>
</template>
<script lang="ts">
import { defineComponent } from '@vue/composition-api';
import { useOrganizerState } from '../states/organizerState';
import { useEventState } from '../states/eventState';
import type { Event, Organizer } from '@innevent/types';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';

export default defineComponent({
	components: {
		TableBusyLoader
	},
	setup(props, context) {
		const { permittedOrganizersRef, isLoadingRef: isLoadingOrganizersRef } = useOrganizerState();
		const { permittedEventsRef, isLoadingRef: isLoadingEventsRef } = useEventState();
		const { $router } = context.root;

		function formatDate(dateString: string) {
			return dateString ? new Date(dateString).toLocaleString() : '';
		}

		const eventTableFields = [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'timeFrom', label: 'Von', sortable: true, formatter: formatDate },
			{ key: 'timeTo', label: 'Bis', sortable: true, formatter: formatDate },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'organizerId', label: 'Veranstalter', sortable: true, formatter: (cellValue) => {
				const organizer = permittedOrganizersRef.value.find(organizer => organizer.organizerId == cellValue);
				return organizer?.organizationName ?? '<organizationName>';
			} }
		];
		const organizerTableFields = [
			{ key: 'organizationName', label: 'Name', sortable: true }
		];

		function rowClickEvent(item: Event) {
			$router.push({ name: 'eDashboard', params: { event: item.eventId as string } });
		}
		function rowClickOrganizer(item: Organizer) {
			$router.push({ name: 'oDashboard', params: { organizer: item.organizerId } });
		}

		return {
			permittedOrganizersRef,
			permittedEventsRef,
			eventTableFields,
			organizerTableFields,
			rowClickEvent,
			rowClickOrganizer,
			isLoadingOrganizersRef,
			isLoadingEventsRef
		};
	}
});
</script>
