<template>
  <div>
    <b-sidebar
      v-if="currentCashboxRef"
      v-model="sidebarOpenRef"
      sidebar-class="top-fixed-header"
      body-class="p-3"
      z-index="1"
      :title="currentCashboxRef ? currentCashboxRef.name : ''"
      width="1000px"
      backdrop
      right
    >
      <h3>Allgemein</h3>
      <label for="cashboxInputName">Name</label>
      <b-form-input
        id="cashboxInputName"
        v-model="currentCashboxRef.name"
        @keyup.enter="onKeyupEnter()"
      />
      <label
        for="cashboxInputDescription"
        class="mt-2"
      >Beschreibung</label>
      <b-form-input
        id="cashboxInputDescription"
        v-model="currentCashboxRef.description"
        @keyup.enter="onKeyupEnter()"
      />
      <LoadButton
        ref="saveButtonRef"
        size="sm"
        icon="save"
        class="mt-3"
        @click="btnSaveCashbox($event)"
      />
      <b-row>
        <b-col>
          <h4 class="mt-3">
            Zugewiesene Mitarbeiter
          </h4>
          <p v-if="cashboxEmployeesRef.length == 0">
            Derzeit sind keine Mitarbeiter zugeordnet.
          </p>
          <b-list-group v-else>
            <b-list-group-item
              v-for="employee of cashboxEmployeesRef"
              :key="employee.userSubject"
              class="d-flex"
            >
              {{ employee.firstName + " " + employee.lastName }}
              <LoadButton
                text=""
                icon="arrow-circle-right"
                size="sm"
                class="ml-auto"
                @click="btnRmoveCashboxEmployeeAssign($event, employee)"
              />
            </b-list-group-item>
          </b-list-group>
        </b-col>
        <b-col>
          <h4 class="mt-3">
            Verfügbare Mitarbeiter
          </h4>
          <b-list-group>
            <b-list-group-item
              v-for="employee of unassignedEmployees"
              :key="employee.userSubject"
            >
              <LoadButton
                text=""
                icon="arrow-circle-left"
                size="sm"
                @click="btnAddCashboxEmployeeAssign($event, employee)"
              />
              {{ employee.firstName + " " + employee.lastName }}
            </b-list-group-item>
          </b-list-group>
        </b-col>
      </b-row>
      <b-row class="mt-5">
        <b-col><h3>Transaktionen</h3></b-col>
        <b-col class="d-flex">
          <LoadButton
            class="ml-auto"
            style="height: fit-content"
            size="sm"
            text="Transaktion erstellen"
            icon="plus-square"
            @click="createCashboxTransactionModal"
          />
        </b-col>
      </b-row>
      <b-table
        ref="cashboxTransactionTableRef"
        :items="cashboxTransactionsRef"
        :fields="tableFields"
        :busy="isLoadingCashboxTransactionsRef"
        sort-by="createdOn"
        :sort-desc="true"
        class="mt-3"
        striped
        hover
        head-variant="light"
        :per-page="paginationCashboxTransactions.perPage"
        :current-page="paginationCashboxTransactions.currentPage"
      >
        <template #table-busy>
          <TableBusyLoader />
        </template>
        <template #cell(employee)="data">
          {{ data.value.firstName + " " + data.value.lastName }}
        </template>
        <template #cell(createdOn)="data">
          {{ formatDate(data.value) }}
        </template>
        <template #cell(creditBefore)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
        <template #cell(creditChange)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
        <template #cell(creditAfter)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
      </b-table>
      <b-pagination
        v-model="paginationCashboxTransactions.currentPage"
        :per-page="paginationCashboxTransactions.perPage"
        :total-rows="paginationCashboxTransactions.totalRows"
        align="center"
      />
    </b-sidebar>
    <CashboxTransactionModal
      ref="cashboxTransactionModalRef"
    />
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref } from '@vue/composition-api';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useCashboxState } from '../../../../states';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import LoadButton from '../../../buttons/LoadButton.vue';
import type { ModalAction, ResetButton } from '@innevent/webapp-types';
import type { Cashbox, CashboxTransaction, ShortEmployee } from '@innevent/types';
import { useEmployeeState, loadEventEmployeesOfEvent } from '../../../../states/employeeState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { currentEventRef } from '../../../../states/eventState';
import CashboxTransactionModal from './CashboxTransactionModal.vue';
import { cashbox as api } from '@innevent/webapp-api';
import { formatDate } from '@innevent/webapp-utils';

export default defineComponent({
	components: {
		TableBusyLoader,
		LoadButton,
		CashboxTransactionModal
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpenRef = ref<boolean>(false);
		const cashboxTransactionModalRef = ref<InstanceType<typeof CashboxTransactionModal>>();
		const instance = getCurrentInstance();
		const cashboxTransactionsRef = ref<CashboxTransaction[]>([]);
		const cashboxEmployeesRef = ref<ShortEmployee[]>([]);
		const isLoadingCashboxTransactionsRef = ref<boolean>(true);

		const { updateCashbox } = useCashboxState();
		const { employeesOfEventRef } = useEmployeeState();
		const cashboxTransactionTableRef = ref<BTable>();
		const currentCashboxRef = ref<Cashbox>();
		const saveButtonRef = ref<InstanceType<typeof LoadButton>>();

		const tableFields: BvTableFieldArray =  [
			{ key: 'createdOn', label: 'Zeitpunkt', sortable: true },
			{ key: 'creditBefore', label: 'Betrag vorher' },
			{ key: 'creditChange', label: 'Betrag', sortable: true },
			{ key: 'creditAfter', label: 'Betrag nachher' },
			{ key: 'comment', label: 'Kommentar' },
			{ key: 'employee', label: 'Mitarbeiter' }

		];

		async function loadCashboxTransactions() {
			try {
				const transactions = await api.listTransactions({ key: {
					eventId: currentCashboxRef.value!.eventId!,
					cashboxId: currentCashboxRef.value!.cashboxId
				} });
				cashboxTransactionsRef.value = transactions.items;
				isLoadingCashboxTransactionsRef.value = false;
			} catch (error: any) {
				notifyError({ instance, error });
			}
		}

		async function loadCashboxEmployees() {
			try {
				cashboxEmployeesRef.value = await api.listEmployees({
					key: {
						cashboxId: currentCashboxRef.value!.cashboxId,
						eventId: currentCashboxRef.value!.eventId!
					}
				});
			} catch (error) {
				notifyError({ instance, error });
			}
		}

		const paginationCashboxTransactions = ref({
			totalRows: cashboxTransactionsRef.value.length,
			perPage: 5,
			currentPage: 1
		});


		const unassignedEmployees = computed(()=> {
			return employeesOfEventRef.value.filter((employee) => {
				return !cashboxEmployeesRef.value.some((cashboxEmployee) => cashboxEmployee.userSubject == employee.userSubject);
			});
		});
		
		async function openForEdit(cashbox: Cashbox) {
			actionRef.value = 'edit';
			currentCashboxRef.value = cashbox;
			sidebarOpenRef.value = true;

			await loadCashboxTransactions();
			await loadCashboxEmployees();
			await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
			paginationCashboxTransactions.value.totalRows = cashboxTransactionsRef.value.length;
		}

		function onKeyupEnter() {
			saveButtonRef.value?.clickButton();
		}

		function createCashboxTransactionModal(btn: ResetButton) {
			const cashboxObj = JSON.parse(JSON.stringify(currentCashboxRef.value));
			cashboxTransactionModalRef.value?.openForCreate(cashboxObj, cashboxTransactionsRef);
			btn.reset();
		}

		async function btnSaveCashbox(btn: ResetButton) {
			try {
				const cashbox = currentCashboxRef.value!;
				await updateCashbox({
					key: {
						cashboxId: cashbox.cashboxId,
						eventId: cashbox.eventId!
					},
					data: {
						name: cashbox.name,
						description: cashbox.description
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnRmoveCashboxEmployeeAssign(btn: ResetButton, employee: ShortEmployee) {
			try {
				const cashbox = currentCashboxRef.value!;

				await api.removeEmployee({
					key: {
						cashboxId: cashbox.cashboxId,
						eventId: cashbox.eventId!
					},
					data: {
						[employee.userSubject]: true
					}
				});
				cashboxEmployeesRef.value = cashboxEmployeesRef.value.filter((employeeFilter) => 
					employee.userSubject !== employeeFilter.userSubject);

				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnAddCashboxEmployeeAssign(btn: ResetButton, employee: ShortEmployee) {
			try {
				const cashbox = currentCashboxRef.value!;

				await api.addEmployee({
					key: {
						cashboxId: cashbox.cashboxId,
						eventId: cashbox.eventId!
					},
					data: {
						[employee.userSubject]: true
					}
				});

				const addedEmployee = employeesOfEventRef.value.find((employeeFind) => employeeFind.userSubject == employee.userSubject);
				if (addedEmployee) cashboxEmployeesRef.value.push(addedEmployee);

				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}
		
		return {
			cashboxTransactionTableRef,
			openForEdit,
			sidebarOpenRef,
			currentCashboxRef,
			btnSaveCashbox,
			onKeyupEnter,
			saveButtonRef,
			cashboxTransactionsRef,
			cashboxEmployeesRef,
			btnRmoveCashboxEmployeeAssign,
			btnAddCashboxEmployeeAssign,
			unassignedEmployees,
			tableFields,
			cashboxTransactionModalRef,
			createCashboxTransactionModal,
			paginationCashboxTransactions,
			formatDate,
			isLoadingCashboxTransactionsRef
		};
	}
});
</script>

<style>
</style>
