import * as log from 'loglevel';
import * as logPrefixer from 'loglevel-plugin-prefix';

logPrefixer.reg(log);
logPrefixer.apply(log, {
	format(level, loggerName, timestamp) {
		return `[${timestamp}][${loggerName}][${level}]`;
	}
});

log.setDefaultLevel('DEBUG');

export const eventState = log.getLogger('state-event');
export const organizerState = log.getLogger('state-organizer');
export const employeeState = log.getLogger('state-employee');
export const permissionState = log.getLogger('state-permission');
export const userState = log.getLogger('state-user');
export const router = log.getLogger('router');
export const app = log.getLogger('app');
export const eventPortalGeneral = log.getLogger('eventportal-general');
export const innTicketImport = log.getLogger('innticket-import');
export const cashboxState = log.getLogger('state-cashbox');
export const ecTerminalState = log.getLogger('state-ecterminal');
export const eventEmployeeInvitationState = log.getLogger('state-eventEmployeeInvitation');
export const tokenstationState = log.getLogger('state-tokenstation');
export const depositGroupState = log.getLogger('state-depositGroup');
export const vendorInvitationState = log.getLogger('state-vendorInvitation');
export const vendorEventPropertyState = log.getLogger('state-vendorEventProperty');
export const accessAreaState = log.getLogger('state-accessAreaState');
