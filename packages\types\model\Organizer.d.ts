import type { PermissionOrganizer } from '..';

export type Organizer = {
    organizerId: string;
    organizationType: OrganizationType;
    organizationName: string;
}

export type OrganizationType = 'PERSON' | 'GBR' | 'GMBH' | 'AG' | 'CLUB' | 'UG' | 'KG' | 'OHG' | 'OTHER';

export type OrganizerEmployee = {
    organizerId: string;
    firstName: string;
    lastName: string;
    position: string;
    userSubject: string;
    permissions: {
        [ permission in PermissionOrganizer ]?: true;
    };
}