import type { EmailChangeRequest } from '@innevent/types';
import { apiInnEvent } from './instances';

type ModelType = EmailChangeRequest;
type PrimaryKey = 'emailChangeRequestId';


export type EmailChangeRequestCreateOptions = {
	data: Pick<ModelType, 'newMail'>;
}
export async function createEmailChangeRequest(options: EmailChangeRequestCreateOptions): Promise<void> {
	await (await apiInnEvent()).post('/EmailChangeRequest', options.data);
}

export type EmailChangeRequestConfirmOptions = {
	key: Pick<ModelType, 'emailChangeRequestId'>;
}
export async function confirmEmailChangeRequest(options: EmailChangeRequestConfirmOptions): Promise<void> {
	await (await apiInnEvent()).get(`/EmailChangeRequest/${options.key.emailChangeRequestId}`);
}