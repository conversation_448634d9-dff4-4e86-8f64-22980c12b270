import type { EcTerminalTransaction, EcTerminal, ShortEmployee, KeyObject, 
	InnEventQueryResult, InnEventQueryOptions, PartialPick } from '@innevent/types';
import { apiInnEvent } from './instances';
import type { ReferenceObject } from '@innevent/webapp-types';

type ModelType = EcTerminal;
type PrimaryKey = 'ecTerminalId';

export type EcTerminalListOptions =  {
    key: ReferenceObject;
};
export async function listEcTerminals(options: EcTerminalListOptions): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/EcTerminal', { 
		params: options.key
	});
	return response.data;
}

export type EcTerminalCreateOptions = {
	key: ReferenceObject;
	data: PartialPick<EcTerminal, 'name' | 'description'>;
};
export async function createEcTerminal(options: EcTerminalCreateOptions): Promise<Required<ModelType>> {
	const response = await (await apiInnEvent()).post('/EcTerminal', options.data, { 
		params: { 
			...options.key,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type EcTerminalDeleteOptions =  {
    key: Pick<ModelType, PrimaryKey> & ReferenceObject;
};
export async function deleteEcTerminal(options: EcTerminalDeleteOptions): Promise<void> {
	await (await apiInnEvent()).delete(`/EcTerminal/${options.key.ecTerminalId}`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId
		} 
	});
}

export type EcTerminalUpdateOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: PartialPick<EcTerminal, 'name' | 'description'>;
}
export async function updateEcTerminal(options: EcTerminalUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/EcTerminal/${options.key.ecTerminalId}`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type EcTerminalListTransactionsOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	queryParameter?: InnEventQueryOptions<ModelType>;
}
export async function listTransactions(options: EcTerminalListTransactionsOptions): Promise<InnEventQueryResult<EcTerminalTransaction>> {
	const response = await (await apiInnEvent()).get(`/EcTerminal/${options.key.ecTerminalId}/Transaction`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			...options.queryParameter,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type EcTerminalAddTransactionOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: Pick<EcTerminalTransaction, 'comment' | 'creditChange'>;
}
export async function addTransaction(options: EcTerminalAddTransactionOptions): Promise<EcTerminalTransaction> {
	const response = await (await apiInnEvent()).post(`/EcTerminal/${options.key.ecTerminalId}/Transaction`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}


export type EcTerminalListEmployeesOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
}
export async function listEmployees(options: EcTerminalListEmployeesOptions): Promise<ShortEmployee[]> {
	const response = await (await apiInnEvent()).get(`/EcTerminal/${options.key.ecTerminalId}/Employee`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type EcTerminalAddEmployeeOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: KeyObject;
}
export async function addEmployee(options: EcTerminalAddEmployeeOptions): Promise<ShortEmployee> {
	const response = await (await apiInnEvent()).post(`/EcTerminal/${options.key.ecTerminalId}/Employee`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type EcTerminalRemoveEmployeeOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: KeyObject;
}
export async function removeEmployee(options: EcTerminalRemoveEmployeeOptions): Promise<ShortEmployee> {
	const response = await (await apiInnEvent()).delete(`/EcTerminal/${options.key.ecTerminalId}/Employee`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		},
		data: options.data
	});
	console.log('response', response);
	return response.data;
}