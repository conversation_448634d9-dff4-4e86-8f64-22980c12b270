<template>
  <b-row class="justify-content-center mt-5">
    <div>
      <div class="text-center mb-4">
        <img class="mb-4" src="../img/logo-transparent.png" alt="" width="150" height="150">
        <h1 class="h4 mb-3 font-weight-normal">{{ title }}</h1>
      </div>
      <b-alert show variant="primary">Unsere Passwortrichtlinien erfordern, dass ein neues Passwort gesetzt werden muss. <br />Bitte gib ein neues sicheres Passwort ein.</b-alert>
      <b-form-group
        id="input-group-password-new"
        :label="fieldNameNewPassword"
        label-for="input-password-new">
        <b-form-input
          squared
          ref="inputPasswordNew"
          id="input-password-new"
          v-model="newPassword"
          type="password"
          :state="validateNewPassword"
          placeholder="Neues Passwort eingeben"
          @keyup.enter="newPasswordSubmit()"
        ></b-form-input>
        <template :state="validateNewPassword">
          <b-form-invalid-feedback v-for="err in passwordErrors" :key="err">
            {{ err }}
          </b-form-invalid-feedback>
        </template>
      </b-form-group>
      <b-button
        block
        :disabled="(!validateNewPassword) || btnLoading"
        variant="primary"
        squared
        @click="newPasswordSubmit()">
        <b-spinner small v-if="btnLoading"></b-spinner>
        {{ btnText }}
      </b-button>
      <b-alert
        class="mt-3"
        v-if="successMessage"
        show
        variant="success">
        {{ successMessage }}
      </b-alert>
      <b-button block variant="outline-primary" v-if="successMessage" to="/login">Anmelden</b-button>
      <b-alert
        class="mt-3"
        v-if="errorMessage"
        show
        variant="danger">
        {{ errorMessage }}
      </b-alert>
      <b-alert
        class="mt-3"
        v-if="expiredError"
        show
        variant="danger">
        Der Link ist leider abgelaufen und nicht mehr gültig.
        <b-link @click="resendPasswordForgotRequest()">Neuen Link anfordern</b-link>
      </b-alert>
    </div>
  </b-row>
</template>
<script>

import PasswordValidator from 'password-validator'
import { Auth } from 'aws-amplify'
var URL = require('url-parse')

export default {
  data() { return{
    url: null,
    errorMessage: null,
    successMessage: null,
    newPassword: '',
    fieldNameNewPassword: 'Neues Passwort',
    passwordErrors: [],
    btnLoading: false,
    btnText: 'Passwort ändern',
    title: 'Passwort zurücksetzen',
    expiredError: false
  }},
  async created(){
    this.url = new URL(window.location.href, true)
    var q = this.url.query
    if(!q || !q.confirmation_code || !q.user_name){
      this.errorMessage = 'URL Query Parameters invalid'
    }
  },
  computed: {
    validateNewPassword(){
      var schema = new PasswordValidator()
      schema
      .is().min(8)
      .is().max(50)
      .has().uppercase()
      .has().lowercase()
      .has().digits()
      .has().not().spaces()

      var errors = schema.validate(this.newPassword, { list: true })
      this.passwordErrors = errors.map(error => { switch(error){
          case 'min': return 'mind. 8 Zeichen'; break;
          case 'uppercase': return 'mind. 1 Großbuchstabe'; break;
          case 'lowercase': return 'mind. 1 Kleinbuchstabe'; break;
          case 'digits': return 'mind. eine Zahl'; break;
          case 'spaces': return 'keine Leerzeichen'; break;
          case 'oneOf': return 'darf nicht Vorname, Nachname oder Email enthalten'; break;
      }})
      if(errors.length > 0) return false
      return true
    },
  },
  methods: {
    async resendPasswordForgotRequest(){
      try {
          await Auth.forgotPassword(this.url.query.user_name);
          console.log('code resent succesfully');
          this.errorMessage = 'Die Email wurde erfolgreich versendet. Bitte prüfe dein Postfach.'
      } catch (err) {
          console.log('error resending code: ', err);
          this.errorMessage = 'Die Email konnte leider nicht versendet werden.'
      }
    },
    async newPasswordSubmit(){
      if(!this.validateNewPassword) return
      var q = this.url.query
      try {
        await Auth.forgotPasswordSubmit(q.user_name, q.confirmation_code, this.newPassword)
        this.success()
      } catch (error) {
        if(error.code == 'ExpiredCodeException' || error.code == 'CodeMismatchException') this.expiredError = true
        else if(error.code == 'LimitExceededException') this.errorMessage = 'Zu viele Versuche, bitte versuche es später erneut.'
        else{
          console.log(error)
          this.errorMessage = 'Unbekannter Fehler'
        }
      }
    },
    async success(){
      this.successMessage = "Passwort wurde erfolgreich geändert"
      // if(this.urlParams.redirect_uri){
      //   var url = buildUrl(this.urlParams.redirect_uri, {})
      //   window.location.replace(url)
      // }
    }
  }
}
</script>
<style scoped>

</style>
