import Vue from 'vue';

import VueRouter from 'vue-router';
Vue.use(VueRouter);

// Vue Pages
import PageNotFound from './src/vue/PageNotFound.vue';
import MainBaseData from './src/vue/main/MainBaseData.vue';
import MainArticles from './src/vue/main/MainArticles.vue';
import MainEvents from './src/vue/main/MainEvents.vue';
import MainStaff from './src/vue/main/MainStaff.vue';

import VendorInvitationPage from './src/vue/forms/VendorInvitationPage.vue';

import VendorNav from './src/vue/navs/VendorNav.vue';
import VendorEventNav from './src/vue/navs/VendorEventNav.vue';

import EventArticlePage from './src/vue/eventPages/EventArticlePage.vue';
import SalesAreaPage from './src/vue/eventPages/SalesAreaPage.vue';
import InnorderCashboxPage from './src/vue/eventPages/InnorderCashboxPage.vue';
import InnorderECTerminalPage from './src/vue/eventPages/InnorderECTerminalPage.vue';
import OrderPage from './src/vue/eventPages/OrderPage.vue';
import VendorEventDashboard from './src/vue/eventPages/VendorEventDashboard.vue';


import VendorPortalHome from './src/vue/main/VendorPortalHome.vue';
import VendorStaffInvitation from './src/vue/forms/VendorStaffInvitation.vue';

// Vue Router
export const router = new VueRouter({
	mode: 'history',
	routes: [
		{ name: 'portalHome', path: '/', component: VendorPortalHome, alias: '/vendor' },
		{ name: 'vendorInvitation', path: '/vendor-invitation/:invitationId', component: VendorInvitationPage },
		{ name: 'vendorStaffInvitation', path: '/vendor-staff-invitation/:invitationId', component: VendorStaffInvitation },
		{ name: 'vBasedata', path: '/vendor/:vendor/basedata', alias: '/vendor/:vendor/', components: { nav: VendorNav, default: MainBaseData } },
		{ name: 'vArticles', path: '/vendor/:vendor/articles', components: { nav: VendorNav, default: MainArticles } },
		{ name: 'vEmployees', path: '/vendor/:vendor/employees', components: { nav: VendorNav, default: MainStaff } },
		{ name: 'vCashboxes', path: '/vendor/:vendor/cashboxes', components: { nav: VendorNav, default: InnorderCashboxPage } },
		{ name: 'vECTerminals', path: '/vendor/:vendor/ecterminals', components: { nav: VendorNav, default: InnorderECTerminalPage } },
		{ name: 'vEvents', path: '/vendor/:vendor/events', components: { nav: VendorNav, default: MainEvents } },
		{ name: 'eDashboard', path: '/vendor/:vendor/event/:event/dashboard', components: { nav: VendorEventNav, default: VendorEventDashboard } },
		{ name: 'eArticles', path: '/vendor/:vendor/event/:event/articles', components: { nav: VendorEventNav, default: EventArticlePage } },
		{ name: 'eSalesAreas', path: '/vendor/:vendor/event/:event/salesareas', components: { nav: VendorEventNav, default: SalesAreaPage } },
		{ name: 'eOrders', path: '/vendor/:vendor/event/:event/orders', components: { nav: VendorEventNav, default: OrderPage } },
		{ path: '*', component: PageNotFound }
	]
});