<template>
  <b-button
    :size="size"
    :variant="variant"
    :disabled="disabled"
    :block="block"
    @click.stop="clickDelete"
  >
    <b-spinner
      v-if="state=='loading'"
      small
    /><FontAwesomeIcon
      v-if="state!='loading'"
      icon="trash"
    /> {{ btnText }}
  </b-button>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export default {
	components: { FontAwesomeIcon },
	props: {
		data: Object,
		block: { default: false },
		size: { type: String, default: '' },
		text: { type: String, default: 'Löschen' }
	},
	data() { return {
		state: 'initial',
		btnText: 'Löschen'
	};},
	computed:{
		variant() {
			if (this.state == 'initial') {
				return 'outline-danger';
			} else if (this.state == 'confirm' || this.state == 'loading') {
				return 'danger';
			}
		},
		disabled() {
			if (this.state == 'loading') {
				return true;
			} else {
				return false;
			}
		}
	},
	created() {
		this.btnText = this.text;
	},
	methods: {
		async clickDelete() {
			if (this.state == 'initial') {
				this.state = 'confirm';
				this.btnText = 'Bestätigen';
				await sleep(3000);
				if (this.state != 'loading') {
					this.state = 'initial',
					this.btnText = this.text;
				}
			} else if (this.state == 'confirm') {
				this.btnText = 'Loading...';
				this.state = 'loading';
				this.$emit('click', this);
			}
		},
		reset() {
			this.state = 'initial',
			this.btnText = this.text;
		}
	}
};
</script>
