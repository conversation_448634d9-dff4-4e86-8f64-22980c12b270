<template>
  <b-button
    squared
    :size="size"
    variant="success"
    :disabled="disabled"
    :block="block"
    @click.stop="clickCreate"
  >
    <b-spinner
      v-if="state=='loading'"
      small
    />
    <FontAwesomeIcon
      v-if="state!='loading'"
      icon="save"
    /> {{ btnText }}
  </b-button>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export default {
	components: { FontAwesomeIcon },
	props: {
		data: Object,
		block: { default: false },
		size: { type: String, default: '' },
		text: { type: String, default: 'Erstellen' }
	},
	data() { return {
		state: 'initial',
		btnText: 'Speichern'
	};},
	computed:{
		disabled() {
			if (this.state == 'loading') {
				return true;
			} else {
				return false;
			}
		}
	},
	created() {
		this.btnText = this.text;
	},
	methods: {
		async clickCreate() {
			if (this.state == 'initial') {
				this.btnText = 'Loading...';
				this.state = 'loading';
				this.$emit('click', this);
			}
		},
		reset() {
			this.state = 'initial',
			this.btnText = this.text;
		}
	}
};
</script>
