<template>
  <div>
    <OrderDetailsSidebar
      v-if="sidebarVisible"
      v-model="sidebarVisible"
      :order="selectedOrder"
    />
    <h2>Bestellungen</h2>
    <b-table
      ref="tableOrders"
      hover
      :items="orders"
      :fields="fields"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="openSidebar"
    />
    <b-pagination
      v-model="pagOrders.currentPage"
      :per-page="pagOrders.perPage"
      :total-rows="pagOrders.rows"
      align="center"
      @change="loadOrders"
    />
  </div>
</template>
<script>
import { API } from 'aws-amplify';
let moment = require('moment');
import OrderDetailsSidebar from './elements/OrderDetailsSidebar.vue';

export default {
	components: { OrderDetailsSidebar },
	props: ['selectedVendor', 'events', 'selectedEvent'],
	data() { return {
		sidebarVisible: false,
		selectedOrder: null,
		orders: [],
		pagOrders: {
			currentPage: 1,
			perPage: 5,
			rows: 0
		},
		fields: [
			{ sortable: true, key: 'createdAt', label: 'Uhrzeit/Datum', formatter: val => this.formatDate(val) },
			{ sortable: true, key: 'costNet', label: 'Betrag Netto', formatter: val => this.formatCurrency(val) },
			{ sortable: true, key: 'costVat', label: 'Steuern', formatter: val => this.formatCurrency(val) },
			{ sortable: true, key: 'costGross', label: 'Betrag Brutto', formatter: val => this.formatCurrency(val) },
			{ sortable: true, key: 'SalesArea', label: 'Verkaufszone', formatter: val => val.name }
		]
	};},
	watch: {
		selectedEvent() {
			this.$refs.tableOrders.refresh();
		}
	},
	created() {
		this.loadOrders(1);
	},
	methods: {
		openSidebar(order) {
			this.selectedOrder = order;
			this.sidebarVisible = true;
		},
		formatDate(date) {
			return moment(date).locale('de').format('DD. MMMM YYYY - HH:mm');
		},
		formatCurrency(value) {
			return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(value);
		},
		async loadOrders(pageNumber) {
			try {
				let params = new URLSearchParams();
				params.append('filterVendor', this.selectedVendor.id);
				params.append('filterEvent', this.selectedEvent.id);
				params.append('include', '["OrderPosition", "VendorEmployee"]');
				params.append('currentPage', (pageNumber-1));
				params.append('pageSize', this.pagOrders.perPage);
			
				const response = await API.get('innorder', '/Order?' + params);
				this.pagOrders.rows = response.count;
				this.orders = response.rows;
			} catch (error) {
				this.$bvToast.toast('Beim Laden der Bestellungen ist ein Fehler aufgetreten.', {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			}
		}
	}
};
</script>
