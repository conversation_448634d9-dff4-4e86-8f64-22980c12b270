export const availableEventPermissions = [
	{
		value: false,
		urn: 'perm:core.event.show',
		badge: 'EventShow',
		name: 'Event Lesen',
		description: 'Lesezugriff auf alle Module'
	},
	{
		value: false,
		urn: 'perm:core.event.edit',
		badge: 'EventEdit',
		name: 'Event Schreiben',
		description: 'Schreibzugriff auf alle Module'
	},
	{
		value: false,
		urn: 'perm:core.event.admin',
		badge: 'EventAdmin',
		name: 'Event Admin',
		description: 'Administrator eines Events'
	},
	{
		value: false,
		urn: 'perm:core.event.owner',
		badge: 'EventOwner',
		name: 'Event Eigentümer',
		description: 'Besitzer eines Events'
	}
];
export const availableOrderPermissions = [
	{
		value: false,
		urn: 'perm:innorder.event.show',
		badge: 'OrderShow',
		name: 'Order Lesen',
		description: 'Lesezugriff auf alles im Ordermodul'
	},
	{
		value: false,
		urn: 'perm:innorder.event.edit',
		badge: 'OrderEdit',
		name: 'Order Schreiben',
		description: 'Schreibzugriff auf alles im Ordermodul'
	}
];
export const availableTokenstationPermissions = [
	{
		value: false,
		urn: 'perm:innorder.tokenstation.use',
		badge: 'TokenstationUse',
		name: 'Tokenstation Use',
		description: 'Mitarbeiter kann alle Tokenstations auswählen'
	},
	{
		value: false,
		urn: 'perm:innorder.tokenstation.admin',
		badge: 'TokenstationAdmin',
		name: 'Tokenstation Admin',
		description: 'Mitarbeiter ist Administrator bei allen Tokenstations'
	}

];
export const availableSalesAreaPermissions = [
	{
		value: false,
		urn: 'perm:innorder.salesarea.use',
		badge: 'SalesAreaUse',
		name: 'SalesArea Use',
		description: 'Mitarbeiter kann alle Salesareas auswählen'
	},
	{
		value: false,
		urn: 'perm:innorder.salesarea.admin',
		badge: 'SalesAreaAdmin',
		name: 'SalesArea Admin',
		description: 'Mitarbeiter ist Administrator bei allen Salesareas'
	}

];
export const availableTicketPermissions = [
	{
		value: false,
		urn: 'perm:innticket.event.show',
		badge: 'TicketShow',
		name: 'Ticket Lesen',
		description: 'Lesezugriff auf alles im Ticketmodul'
	},
	{
		value: false,
		urn: 'perm:innticket.event.edit',
		badge: 'TicketEdit',
		name: 'Ticket Schreiben',
		description: 'Schreibzugriff auf alles im Ticketmodul'
	}
];
export const availableRedemptionGroupPermissions = [
	{
		value: false,
		urn: 'perm:innticket.redemptiongroup.use',
		badge: 'RedemptiongroupUse',
		name: 'Redemptiongroup Use',
		description: 'Mitarbeiter kann alle Entwertungsgruppen auswählen'
	},
	{
		value: false,
		urn: 'perm:innticket.redemptiongroup.admin',
		badge: 'RedemptiongroupAdmin',
		name: 'Redemptiongroup Admin',
		description: 'Mitarbeiter ist Administrator aller Entwertungsgruppen'
	}
];
export const availableAccessPermissions = [
	{
		value: false,
		urn: 'perm:innaccess.event.show',
		badge: 'Access Show',
		name: 'Access Lesen',
		description: 'Lesezugriff auf alles im Accessmodul'
	},
	{
		value: false,
		urn: 'perm:innaccess.event.edit',
		badge: 'Access Edit',
		name: 'Access Schreiben',
		description: 'Schreibzugriff auf alles im Ticketmodul'
	}
];
export const availableAccessStationPermissions = [
	{
		value: false,
		urn: 'perm:innaccess.accessstation.use',
		badge: 'AccessStation Use',
		name: 'AccessStation Use',
		description: 'Mitarbeiter kann alle AccessStations auswählen'
	},
	{
		value: false,
		urn: 'perm:innaccess.accessstation.admin',
		badge: 'AccessStation Admin',
		name: 'AccessStation Admin',
		description: 'Mitarbeiter ist Administrator aller AccessStation'
	}
];