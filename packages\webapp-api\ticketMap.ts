import type { TicketMap } from '@innevent/webapp-types';
import { apiInnTicket } from './instances';

type ModelType = TicketMap;


export type TicketMapGetOptions =  {
    key: { eventId: string ; };
};
export async function getTicketMap(options: TicketMapGetOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).get('/TicketMap', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}