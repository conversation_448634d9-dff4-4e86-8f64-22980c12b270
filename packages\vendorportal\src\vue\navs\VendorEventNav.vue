<template>
  <b-nav
    vertical
    pills
  >
    <b-nav-text :to="'vendor/' + selectedVendor.id">
      <b-button-group class="d-flex">
        <b-button
          variant="outline-primary"
          class="w-100"
          :to="{ name: 'vBasedata' }"
        >
          <FontAwesomeIcon
            icon="arrow-circle-left"
            fixed-width
          /><span class="ml-3">Zurück</span>
        </b-button>
        <b-dropdown
          v-if="filteredEventProps > 1"
          variant="outline-primary"
          right
          text="Event wechseln"
          class="w-100"
        >
          <b-dropdown-item
            v-for="ep of filteredEventProps"
            :key="ep.Event.id"
            :to="{ params: { event: ep.Event.id } }"
          >
            {{ ep.Event.name }}
          </b-dropdown-item>
        </b-dropdown>
      </b-button-group>
    </b-nav-text>
    <b-nav-text>
      <h3>{{ currentEventName }}</h3>
    </b-nav-text>
    <div class="dropdown-divider" />
    <b-nav-item
      v-for="item of navItems"
      :key="item.link"
      exact
      exact-active-class="active"
      :to="{ name: item.link }"
    >
      <FontAwesomeIcon
        :icon="item.icon"
        fixed-width
      /><span class="ml-3">{{ item.name }}</span>
    </b-nav-item>
  </b-nav>
</template>
<script>
export default {
	components: {},
	props: ['selectedVendor', 'selectedEvent'],
	data() {
		return {
			navItems: [
				{ name: 'Eventdetails', link: 'eDashboard', icon: 'calendar-alt' },
				{ name: 'Artikelverwaltung', link: 'eArticles', icon: 'th-list' },
				{ name: 'Verkaufsbereiche', link: 'eSalesAreas', icon: 'store' },
				{ name: 'Bestellungen', link: 'eOrders', icon: 'shopping-cart' }
			]
		};
	},
	computed: {
		filteredEventProps() {
			return this.selectedVendor.VendorEventProperties.filter(
				(ep) => ep.Event.id != this.selectedEvent.id
			);
		},
		currentEventName() {
			if (this.selectedEvent) return this.selectedEvent.name;
			else return 'Event Not Found';
		}
	},
	created() {},
	methods: {}
};
</script>
