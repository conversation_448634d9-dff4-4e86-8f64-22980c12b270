<script>
import { Line } from 'vue-chartjs';

export default {
	extends: Line,
	props: {
		chartdata: {
			type: Object,
			default: null
		}
	},
	data: () => ({
		options: {
			responsive: true,
			maintainAspectRatio: false,
			scales: {
				xAxes: [{
					type: 'time',
					distribution: 'series'
				}],
				yAxes: [{
					ticks: {
						min: 0,
						stepSize: 1,
						reverse: false,
						beginAtZero: true
					}
				}]
				
			}
		}
	}),
	mounted() {
		this.renderChart(this.chartdata, this.options);
	}
};
</script>


