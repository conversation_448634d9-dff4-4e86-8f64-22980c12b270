<template>
  <div>
    <b-button
      :id="btnId"
      pill
      style="border: none"
      :style="btnSytles"
    >
      <FontAwesomeIcon
        :icon="['fas', 'palette']"
        size="lg"
        :color="iconColor"
      />
    </b-button>
    <b-popover
      :target="btnId"
      :triggers="triggers"
      :placement="placement"
    >
      <Chrome
        :value="colorHSV"
        @input="updateColor"
      />
    </b-popover>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { Chrome } from 'vue-color';
import tinycolor from 'tinycolor2';
const uuid = require('uuid/v4');

export default {
	components: { FontAwesomeIcon, Chrome },
	props: {
		triggers: { type: String, default: 'click' },
		placement: { type: String, default: 'top' },
		fallbackColor: { type: String },
		value: { type: String }
	},
	data() { return {
		btnId: uuid(),
		color: null
	};},
	computed:{
		colorHSV() {
			return this.color.toHsv();
		},
		btnSytles() {
			return 'background-color: ' + this.color.toHexString();
		},
		iconColor() {
			if (this.color.isLight()) {
				return '#6c757d';
			} else {
				return 'white';
			}
		}
	},
	created() {
		let color = tinycolor(this.value);
		let fallbackColor = tinycolor(this.fallbackColor);
		let defaultColor = tinycolor('#6c757d');
		if (color.isValid()) {
			this.color = color;
		} else if (fallbackColor.isValid()) {
			this.color = fallbackColor;
		} else {
			this.color = defaultColor;
		}
	},
	methods: {
		updateColor(selectedColor) {
			let color = tinycolor(selectedColor.hsv);
			if (color.isValid()) {
				this.color = color;
				this.$emit('input', color.toHexString());
				this.$emit('change');
			}
		}
	}
};
</script>
