<template>
  <b-button
    :size="size"
    :variant="variant"
    :disabled="disabled || loading"
    :block="block"
    @click="emit('click')"
  >
    <b-spinner
      v-if="loading"
      small
    />
    <FontAwesomeIcon
      v-if="!loading"
      :icon="icon"
    />
    {{ loading ? 'Loading...' : text }}
  </b-button>
</template>
<script>
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/naming-convention */
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { defineComponent } from '@vue/composition-api';

export default defineComponent({
	components: { FontAwesomeIcon },
	props: {
		block: { type: Boolean, default: false },
		size: { type: String, default: '' },
		text: { type: String, default: 'Speichern' },
		icon: { type: String, default: 'save' },
		variant: { type: String, default: 'primary' },
		disabled: { type: Boolean, default: false },
		loading: { type: Boolean, default: false }
	},
	emits: ['click'],
	setup(props, { emit }) {
		return {
			emit
		};
	}
});
</script>
