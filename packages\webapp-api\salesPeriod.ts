import { apiInnTicket } from './instances';
import type { SalesPeriod } from '@innevent/webapp-types';

type ModelType = SalesPeriod;
type PrimaryKey = 'salesPeriodId';


export type SalesPeriodCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'salesPeriodName' | 'description' | 'timeFrom' | 'timeTo'>;
}
export async function createSalesPeriod(options: SalesPeriodCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/SalesPeriod', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW' 
		} 
	});
	return response.data;
}

export type SalesPeriodDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteSalesPeriod(options: SalesPeriodDeleteOptions): Promise<void> {
	await (await apiInnTicket()).delete(`/SalesPeriod/${options.key.salesPeriodId}`, { 
		params: { eventId: options.key.eventId } 
	});
}


export type SalesPeriodUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'salesPeriodName' | 'description' | 'timeFrom' | 'timeTo'>>;
};

export async function updateSalesPeriod(options: SalesPeriodUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/SalesPeriod/${options.key.salesPeriodId}`, 
		{
			salesPeriodName: options.data.salesPeriodName,
			description: options.data.description,
			timeFrom: options.data.timeFrom,
			timeTo: options.data.timeTo
		}, { 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}


export type SalesPeriodGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getSalesPeriods(options: SalesPeriodGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnTicket()).get('/SalesPeriod', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}