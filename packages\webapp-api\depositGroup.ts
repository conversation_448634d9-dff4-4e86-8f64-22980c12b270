import { apiInnOrder } from './instances';
import type { DepositGroup } from '@innevent/webapp-types';

type ModelType = DepositGroup;
type PrimaryKey = 'id';


export type DepositGroupCreateOptions = {
	data: Pick<ModelType, 'name' | 'active' | 'costGross' | 'vat' | 'EventId'>;
}

export async function createDepositGroup(options: DepositGroupCreateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).post('/DepositGroup', options.data);
	const newDepositGroup = {
		id: response.data,
		...options.data
	} as DepositGroup;
	return newDepositGroup;
}

export type DepositGroupDeleteOptions = {
	key: Pick<ModelType, PrimaryKey>;
}
export async function deleteDepositGroup(options: DepositGroupDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/DepositGroup/${options.key.id}`);
}

export type DepositGroupUpdateOptions = {
	key: Pick<ModelType, PrimaryKey>;
	data: Partial<Pick<ModelType, 'name' | 'active' | 'costGross' | 'vat'>>;
};

export async function updateDepositGroup(options: DepositGroupUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).put(`/DepositGroup/${options.key.id}`, {
		name: options.data.name,
		active: options.data.active,
		costGross: options.data.costGross,
		vat: options.data.vat
	});
	return response.data;
}


export type DepositGroupGetOptions =  {
    key: Pick<ModelType, 'EventId'>;
};
export async function getDepositGroups(options: DepositGroupGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnOrder()).get(`/DepositGroup?filterEvent=${options.key.EventId}`);
	return response.data;
}
