import { vendorInvitation as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { vendorInvitationState as log } from '../loglevel';
import type { VendorInvitation } from '@innevent/webapp-types';

log.debug('INIT vendorInvitationState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const vendorInvitationsRef = ref<VendorInvitation[]>([]);

watch(vendorInvitationsRef, () => {
	log.debug('WATCH vendorInvitationState', vendorInvitationsRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useVendorInvitationState() {
	log.debug('useVendorInvitationState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		vendorInvitationsRef,
		loadVendorInvitations,
		createVendorInvitation,
		deleteVendorInvitation
	};
}

// type LoadVendorInvitationsStateOption = {
//     key: Pick<VendorInvitation, 'EventId'>;
// }
async function loadVendorInvitations(eventId: string): Promise<void> {
	log.debug('loadVendorInvitations()', eventId);
	isLoadingRef.value = true;
	try {
		vendorInvitationsRef.value = await api.getVendorInvitations({
			key: {
				EventId: eventId
			}
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type CreateVendorInvitationStateOption = {
	data: Pick<VendorInvitation, 'mail' | 'status' | 'EventId'> & Partial<Pick<VendorInvitation, 'message'>>;
}
async function createVendorInvitation(options: CreateVendorInvitationStateOption): Promise<void> {
	log.debug('createVendorInvitation()', options);
	isLoadingRef.value = true;
	try {
		const newVendorInvitation = await api.createVendorInvitation(options);
		vendorInvitationsRef.value.push(newVendorInvitation);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type DeleteVendorInvitationStateOption = {
	key: Pick<VendorInvitation, 'id'>;
}
async function deleteVendorInvitation(options: DeleteVendorInvitationStateOption): Promise<void> {
	log.debug('deleteVendorInvitation()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteVendorInvitation(options);
		vendorInvitationsRef.value = vendorInvitationsRef.value.filter((vendorInvitation) => vendorInvitation.id !== options.key.id);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}