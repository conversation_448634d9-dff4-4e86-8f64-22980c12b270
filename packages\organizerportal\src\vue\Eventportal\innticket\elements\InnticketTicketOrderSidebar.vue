<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    :title="actionRef == 'create' ? 'Neue Bestellung' : 'Bestellung'"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h4>Allgemein</h4>
      <b-card bg-variant="light">
        <b-form-group label="Email-Adresse ">
          <b-form-input
            v-model="customer.emailAddress"
            type="email"
            :state="validStreet"
            :readonly="actionRef == 'view'"
          />
        </b-form-group>
      </b-card>
      <b-card
        class="mt-4"
        bg-variant="light"
      >
        <p>Persönliche Angaben</p>
        <b-form-select
          v-model="customer.customerType"
          :options="organizationTypes"
          :disabled="actionRef == 'view'"
        />
        <b-form-input
          v-if="customer.customerType == 'organization'"
          v-model="customer.organizationName"
          class="mt-2"
          placeholder="Name des Unternehmens"
          :state="validStreet"
          :readonly="actionRef == 'view'"
        />
        <b-row
          class="mt-2"
        >
          <b-col sm="6">
            <b-form-input
              v-model="customer.firstName"
              placeholder="Vorname"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
          <b-col sm="6">
            <b-form-input
              v-model="customer.lastName"
              placeholder="Nachname"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
        </b-row>
        <b-row
          class="mt-2"
        >
          <b-col sm="6">
            <b-form-input
              v-model="customer.birthdate"
              placeholder="Geburtsdatum"
              type="date"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
          <b-col sm="6">
            <b-form-select
              v-model="customer.gender"
              :options="optionsGender"
              :disabled="actionRef == 'view'"
            />
          </b-col>
        </b-row>
        <b-form-input
          v-model="customer.phoneNumber"
          type="number"
          class="mt-2"
          placeholder="Handynummer"
          :readonly="actionRef == 'view'"
        />
      </b-card>
      <b-form-checkbox
        v-model="addressRequired"
        class="mt-4"
        name="check-button"
        switch
        :disabled="actionRef == 'view'"
      >
        Adresse erforderlich
      </b-form-checkbox>
      <b-card
        v-if="addressRequired"
        bg-variant="light"
      >
        <p>Adresse</p>
        <b-row
          class="mt-2"
        >
          <b-col sm="6">
            <b-form-input
              v-model="address.firstName"
              placeholder="Vorname"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
          <b-col sm="6">
            <b-form-input
              v-model="address.lastName"
              placeholder="Nachname"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
        </b-row>
        <b-row
          class="mt-2"
        >
          <b-col sm="10">
            <b-form-input
              v-model="address.street"
              placeholder="Straße"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
          <b-col sm="2">
            <b-form-input
              v-model="address.houseNumber"
              placeholder="Nr"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
        </b-row>
        <b-row
          class="mt-2"
        >
          <b-col sm="4">
            <b-form-input
              v-model="address.zipCode"
              placeholder="Postleitzahl"
              type="number"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
          <b-col sm="8">
            <b-form-input
              v-model="address.city"
              placeholder="Stadt"
              :state="validStreet"
              :readonly="actionRef == 'view'"
            />
          </b-col>
        </b-row>
      </b-card>
      <b-container class="mt-4">
        <b-row>
          <b-col><h4>Ticketauswahl</h4></b-col>
          <b-col>
            <b-dropdown
              v-if="actionRef !== 'view'"
              squared
              class="float-right"
              size="sm"
              variant="outline-secondary"
              :disabled="unusedTickets.length == 0"
            >
              <template #button-content>
                <FontAwesomeIcon :icon="['fas', 'plus-square']" /> Ticket hinzufügen
              </template>
              <b-dropdown-item
                v-for="ticket in unusedTickets"
                :key="ticket.ticketId"
                @click="addOrderPosition(ticket)"
              >
                {{ ticket.ticketName }}
              </b-dropdown-item>
            </b-dropdown>
          </b-col>
        </b-row>
      </b-container>
      <b-table
        id="ticketTable"
        ref="ticketTable"
        hover
        :items="Object.values(orderPositions)"
        :fields="tableFields"
        class="mt-3 table-clickable"
        head-variant="light"
      >
        <template #cell(ticket)="{item}">
          {{ item.name }}
        </template>
        <template #cell(salesperiod)="{item}">
          <b-form-select
            v-model="item.salesPeriod.salesPeriodId"
            :options="getSalesPeriodsFromTicket(item.ticketId)"
            value-field="salesPeriodId"
            text-field="salesPeriodName"
            :disabled="actionRef == 'view'"
            @input="selectSalesPeriod($event, item)"
          />
        </template>
        <template #cell(priceGross)="{item}">
          <b-input-group
            append="€"
            style="width: 100px"
          >
            <b-form-input
              v-model="item.singlePrice.priceGross"
              type="number"
              :readonly="actionRef == 'view'"
              @change="calculateAndRemove"
            />
          </b-input-group>
        </template>
        <template #cell(quantity)="{item}">
          <b-form-input
            v-model="item.quantity"
            type="number"
            :readonly="actionRef == 'view'"
            @change="calculateAndRemove"
          />
        </template>
        <template #cell(sumPriceGross)="{item}">
          <b-input-group
            append="€"
            style="width: 100px"
          >
            <b-form-input
              :value="item.positionPrice.priceGross"
              readonly
            />
          </b-input-group>
        </template>
        <template #table-busy>
          <TableBusyLoader />
        </template>
        <template #cell(buttons)="data">
          <DeleteButton
            v-if="actionRef !== 'view'"
            size="sm"
            icon="trash"
            text=""
            variant="danger"
            @click="removeOrderPosition($event, data)"
          />
        </template>
        <template #custom-foot>
          <b-tr>
            <b-th
              colspan="6"
            >
              <p class="float-right mr-5">
                Gesamtbetrag: {{ totalPriceGross.priceGross }} €
              </p>
            </b-th>
          </b-tr>
        </template>
      </b-table>
    </div>
    <LoadButton
      v-if="actionRef !== 'view'"
      ref="createTicketOrderButton"
      variant="primary"
      text="Bestellung aufgeben"
      @click="createTicketOrder"
    />
  </b-sidebar>
</template>
<script lang="ts">
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import { defineComponent, ref, getCurrentInstance, computed, onMounted } from '@vue/composition-api';
import type { BvTableCellData, ModalAction, ResetButton, Ticket } from '@innevent/webapp-types';
import { useTicketOrderMap } from '../../../../states/ticketOrder';
import type { TicketOrderPosition, TicketOrderCreate, SalesPeriod, VatPrice, TicketOrder } from '@innevent/types';
import type { BvTableFieldArray } from 'bootstrap-vue';
import * as api from '@innevent/webapp-api';
import { getCognitoUserAttributes, removeEmptyProperties, notifyError, notifySuccess } from '@innevent/webapp-utils';
import { currentEventRef } from '../../../../states/eventState';

type NewOrderPosition = Partial<Pick<TicketOrderPosition, 'quantity' | 'ticketId' | 'name' | 'description' | 'positionType' | 'generatedTickets'>> & {
        salesPeriod: Pick<SalesPeriod, 'salesPeriodId'>;
        singlePrice: Pick<VatPrice, 'priceGross'>;
        positionPrice: Pick<VatPrice, 'priceGross'>;
    };

type NewOrderPositions = {
   [positionNumber: number]: NewOrderPosition;
}

export default defineComponent({
	components: {
		LoadButton,
		DeleteButton
	},
	emits: ['ticketOrderChanged'],
	setup() {
		const actionRef = ref<ModalAction>('create');
		const { ticketOrderMapRef: ticketOrderMap } = useTicketOrderMap();
		const sidebarOpen = ref<boolean>(false);
		const addressRequired = ref<boolean>(false);
		const instance = getCurrentInstance();
		const createTicketOrderButton = ref<InstanceType<typeof LoadButton>>();
		let cognitoUserAttributes: any = {};
	
		const organizationTypes = [{ value: 'person', text: 'Person' }, { value: 'organization', text: 'Firma' }];
		const optionsGender = [{ value: 'male', text: 'männlich' }, { value: 'female', text: 'weiblich' }, { value: 'other', text: 'divers' }];
		const tableFields: BvTableFieldArray =  [
			{ key: 'ticket', label: 'Ticket', sortable: true },
			{ key: 'salesperiod', label: 'Verkaufsphase' },
			{ key: 'priceGross', label: 'Preis' },
			{ key: 'quantity', label: 'Anzahl' },
			{ key: 'sumPriceGross', label: 'Gesamt' },
			{ key: 'buttons', label: '' }
		];
		let totalPriceGross: Pick<VatPrice, 'priceGross'> = {
			priceGross: 0
		};

		const emptyAddress: TicketOrderCreate['customer']['address'] = {
			firstName: '', 
			lastName: '',
			street: '', 
			houseNumber: '',
			zipCode: '',
			city: '',
			country: {
				iso3166Alpha2Code: 'DE'
			}
		};

		const emptyCustomer: TicketOrderCreate['customer'] = {
			customerType: 'person',
			emailAddress: '',
			firstName: '',
			lastName: '',
			gender: 'male',
			birthdate: '',
			organizationName: '',
			phoneNumber: undefined
		};

		const address = ref<TicketOrderCreate['customer']['address']>(Object.assign({}, emptyAddress));
		const customer = ref<TicketOrderCreate['customer']>(Object.assign({}, emptyCustomer));
		const orderPositions = ref<NewOrderPositions>({});
		
		onMounted(getActualUser);

		async function getActualUser() {
			cognitoUserAttributes = await getCognitoUserAttributes();
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;

			address.value = Object.assign({}, emptyAddress);
			customer.value = Object.assign({}, emptyCustomer);
			orderPositions.value = {};
			addressRequired.value = false;
			totalPriceGross.priceGross = 0;
		}

		function openForView(ticketOrder: TicketOrder) {
			actionRef.value = 'view';
			sidebarOpen.value = true;

			customer.value = ticketOrder.customer;
			if (ticketOrder.customer.address) {
				address.value = ticketOrder.customer.address;
				addressRequired.value = true;

			}
			orderPositions.value = ticketOrder.positions;
			totalPriceGross.priceGross = ticketOrder.totalPrice.priceGross;
		}

		async function onKeyupEnter() {
			createTicketOrderButton.value?.clickButton();
		}

		async function createTicketOrder(btn: ResetButton) {
			if (customer.value.customerType == 'person') {
				delete customer.value.organizationName;
			}
			if (!addressRequired.value) {
				delete customer.value.address;
			}

			const orderPositionCopy: NewOrderPositions = JSON.parse(JSON.stringify(orderPositions.value));
			const orderPositionsObj = Object.values(orderPositionCopy).reduce(
				(orderPositions, orderPosition, index)=> {				
					orderPositions[index +1] = orderPosition;
					//@ts-ignore
					delete orderPositions[index +1].positionPrice;
					return orderPositions;
				}, {});


			const ticketOrder: TicketOrderCreate = {
				eventId: currentEventRef.value!.eventId!,
				orderType: 'manual',
				currency: {
					iso4217Code: 'EUR'
				},
				totalPrice: totalPriceGross,
				eventEmployee: {
					userSubject: cognitoUserAttributes.sub,
					firstName: cognitoUserAttributes.given_name,
					lastName: cognitoUserAttributes.family_name,
					position: 'Position Platzhalter'
				},
				customer: customer.value,
				positions: orderPositionsObj
			};

			removeEmptyProperties(ticketOrder.customer);

			try {
				const newTicketOrder = await api.createTicketOrder({
					key: {
						eventId: currentEventRef.value!.eventId!
					},
					data: ticketOrder
				});

				ticketOrderMap.value!.ticketOrders.items.push(newTicketOrder);
				notifySuccess({ instance, message: 'Bestellung erfolgreich erstellt' });
				sidebarOpen.value = false;
			} catch (error: any) {
				notifyError({ instance, message: 'Fehler aufgetreten', error });
			}
			btn.reset();
		}

		function getSalesPeriodsFromTicket(ticketId: string) {
			const ticket = ticketOrderMap.value?.tickets.find(ticket => ticket.ticketId == ticketId) ?? undefined;

			if (ticket) {
				const salesPeriodIds = Object.keys(ticket.ticketSaleProperties);
				return ticketOrderMap.value?.salesPeriods.filter(salesPeriod => salesPeriodIds.includes(salesPeriod.salesPeriodId));
			}
			return {};
		}

		const unusedTickets = computed(() => {
			if (!ticketOrderMap.value) return [];
			const orderPositionValues = Object.values(orderPositions.value);
			const usedTicketIds = orderPositionValues.map(orderPosition => orderPosition.ticketId);
			return ticketOrderMap.value.tickets.filter(ticket => !usedTicketIds.includes(ticket.ticketId));
		});

		function selectSalesPeriod(salesPeriodId: string, orderPosition: NewOrderPosition) {
			const ticket = ticketOrderMap.value?.tickets.find(ticket => ticket.ticketId == orderPosition.ticketId) as Ticket;

			orderPosition.singlePrice = {
				priceGross: ticket.ticketSaleProperties[salesPeriodId].price.priceGross
			};
			const price = orderPosition.singlePrice.priceGross as number;
			const quantity = orderPosition.quantity as number;

			orderPosition.positionPrice.priceGross = (price * quantity);

			calculateAndRemove();
		}

		function removeOrderPosition(btn: ResetButton, cellData: BvTableCellData<TicketOrderPosition>) {
			const orderPositionKey = Object.entries(orderPositions.value).find(([key, value]) => value.ticketId == cellData.item.ticketId)?.[0];
			if (orderPositionKey) {
				delete orderPositions.value[orderPositionKey];
				calculateAndRemove();
			}
			btn.reset();
		}
		
		function addOrderPosition(ticket: Ticket) {
			const firstSalesPeriodId = Object.keys(ticket.ticketSaleProperties)[0];
			const ticketOrderPosition: NewOrderPosition = {
				ticketId: ticket.ticketId,
				name: ticket.ticketName,
				description: ticket.description || undefined,
				positionType: 'ticket',
				quantity: 1,
				salesPeriod: {
					salesPeriodId: firstSalesPeriodId
				},
				singlePrice: {
					priceGross: ticket.ticketSaleProperties[firstSalesPeriodId].price.priceGross
				},
				positionPrice: {
					priceGross: ticket.ticketSaleProperties[firstSalesPeriodId].price.priceGross
				}
			};
			orderPositions.value[Object.keys(orderPositions.value).length +1] = ticketOrderPosition;

			calculateAndRemove();
		}


		function calculateAndRemove() {
			let totalPrice = 0;
			
			//Calculate
			Object.values(orderPositions.value).forEach((orderPosition, index) => {
				if (orderPosition.singlePrice && !orderPosition.singlePrice.priceGross) orderPosition.singlePrice.priceGross = 0;
				if (!orderPosition.quantity) orderPosition.quantity == 0;

				const price = orderPosition.singlePrice?.priceGross as number;
				const quantity = orderPosition.quantity as number;

				orderPosition.positionPrice.priceGross = (price * quantity);
				totalPrice += (price * quantity);
			});
			totalPriceGross.priceGross = totalPrice;

			//Remove where quantity = 0
			orderPositions.value = Object.values(orderPositions.value).reduce((orderPositions, orderPosition, index)=> {
				if (orderPosition.quantity != 0) {
					orderPositions[index + 1] = orderPosition;
				}
				return orderPositions;
			}, {});
		}


		return {
			actionRef,
			sidebarOpen,
			createTicketOrder,
			createTicketOrderButton,
			onKeyupEnter,
			openForCreate,
			openForView,
			tableFields,
			organizationTypes,
			customer,
			address,
			optionsGender,
			orderPositions,
			unusedTickets,
			addOrderPosition,
			selectSalesPeriod,
			ticketOrderMap,
			getSalesPeriodsFromTicket,
			calculateAndRemove,
			totalPriceGross,
			addressRequired,
			removeOrderPosition
		};
	}
});
</script>
<style>
</style>