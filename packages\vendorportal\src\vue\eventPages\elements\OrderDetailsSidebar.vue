<template>
  <b-sidebar
    v-model="sidebarVisible"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Details zur Bestellung"
    width="1000px"
    backdrop
    right
  >
    <div class="px-3 py-2">
      <div class="mb-4">
        <h2>Allgemeines</h2>
        <div>
          <h5> - Zeitpunkt der Bestellung: {{ formatDate(order.timestamp) }}</h5>
          <h5> - Bruttobetrag: {{ order.costGross }} €</h5>
          <h5> - SalesArea: {{ order.SalesArea.name }}</h5>
          <h5> - Verkäufer: {{ order.VendorEmployee.firstname + " " + order.VendorEmployee.lastname }}</h5>
        </div>
        <h2 class="mt-4">
          Einzelpositionen
        </h2>
        <b-card
          v-if="getArticleOrderPositions.length"
          title="Artikel"
          class="mb-4"
        >
          <b-row>
            <b-table
              ref="selectableTable"
              :items="getArticleOrderPositions"
              :fields="orderFields"
              head-variant="light"
            >
              <template #cell(costGross)="data">
                {{ data.item.costGross }} €
              </template>
              <template #cell(vat)="data">
                {{ data.item.vat }} %
              </template>
              <template #cell(costGrossSum)="data">
                {{ data.item.costGrossSum }} €
              </template>
            </b-table>
          </b-row>
        </b-card>

        <b-card
          v-if="getDepositOrderPositions.length"
          title="Zurückgegebener Pfand"
          class="mb-4"
        >
          <b-row>
            <b-table
              ref="selectableTable"
              :items="getDepositOrderPositions"
              :fields="orderFields"
              head-variant="light"
            >
              <template #cell(costGross)="data">
                {{ data.item.costGross }} €
              </template>
              <template #cell(vat)="data">
                {{ data.item.vat }} %
              </template>
              <template #cell(costGrossSum)="data">
                {{ data.item.costGrossSum }} €
              </template>
            </b-table>
          </b-row>
        </b-card>
      </div>
      <div style="height: 66px">
        <!-- Platzhalter, da Slider nach unten verschoben -->
      </div>
    </div>
  </b-sidebar>
</template>
<script>

import LoadButton from '../../buttons/LoadButton.vue';
import SaveButton from '../../buttons/SaveButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
import Multiselect from 'vue-multiselect';
let moment = require('moment');
const uuid = require('uuid/v4');


export default {
	components: { LoadButton, DeleteButton, Multiselect, SaveButton },
	props: ['value', 'order'],
	data() { return {
		orderFields: [
			{
				key: 'name',
				label: 'Name'
			}, {
				key: 'amount',
				label: 'Anzahl'
			}, {
				key: 'costGross',
				sortable: false,
				label: 'Preis brutto'
			}, {
				key: 'vat',
				label: 'Mwst'
			}, {
				key: 'costGrossSum',
				label: 'Gesamt brutto'
			}]
	};},
	computed:{
		sidebarVisible: {
			get() {
				return this.value;
			},
			set(val) {
				console.log(val);
				this.$emit('input', val);
			}
		},
		getArticleOrderPositions() {
			return this.order.OrderPositions.filter((op) => ['ARTICLE', 'DEPOSIT'].includes(op.type));
		},
		getDepositOrderPositions() {
			return this.order.OrderPositions.filter((op) => op.type == 'DEPOSITRETURN');
		}

	},
	created() {
	},
	methods:{
		formatDate(date) {
			return moment(date).locale('de').format('DD.MM.YYYY  HH:mm:ss');
		}
	}
};
</script>

<style>
.top-fixed-header {
  padding-top: 66px;
}
</style>
