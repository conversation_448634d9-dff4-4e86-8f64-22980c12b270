<template>
  <b-card
    title="Event suchen"
    img-src="https://images.unsplash.com/photo-1429514513361-8fa32282fd5f?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1267&q=80"
    img-top
    tag="article"
  >
    <b-card-text>
      Add your chip/wristband for top up & payout online!
    </b-card-text>
    <b-card-text>
      You got a chip already? You can add your chip here and top it up online. We accept various payment methods.
    </b-card-text>
    <b-button href="#" variant="primary">Go somewhere</b-button>
  </b-card>
</template>
<script>

// Frameworks
import { Auth, API } from 'aws-amplify'

// Vue Components
// import Navigation from './Eventportal/Navigation.vue'

export default{
  props: [],
  components: { },
  data() { return{
    selectedEventId: null
  }},
  created(){

  },
  watch: {

  },
  computed:{
    currentEvent(){
      if(this.$route.params.event) return this.events.find(e => e.id == this.$route.params.event)
      else return null
    }
  },
  methods:{

  }
}

</script>
<style scoped>

</style>
