import { depositGroup as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { depositGroupState as log } from '../loglevel';
import Vue from 'vue';
import type { DepositGroup } from '@innevent/webapp-types';

log.debug('INIT depositGroupState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const depositGroupsRef = ref<DepositGroup[]>([]);

watch(depositGroupsRef, () => {
	log.debug('WATCH depositGroupsRef', depositGroupsRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useTokenstationState() {
	log.debug('useTokenstationState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		depositGroupsRef,
		loadDepositGroups,
		createDepositGroup,
		updateDepositGroup,
		deleteDepositGroup
	};
}

// type LoadDepositGroupsStateOption = {
//     key: Pick<DepositGroup, 'EventId'>;
// }
async function loadDepositGroups(eventId: string): Promise<void> {
	log.debug('loadDepositGroups()', eventId);
	isLoadingRef.value = true;
	try {
		depositGroupsRef.value = await api.getDepositGroups({
			key: {
				EventId: eventId
			}
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type CreateDepositGroupStateOption = {
	data: Pick<DepositGroup, 'name' | 'active' | 'costGross' | 'vat' | 'EventId'>;
}
async function createDepositGroup(options: CreateDepositGroupStateOption): Promise<void> {
	log.debug('createDepositGroup()', options);
	isLoadingRef.value = true;
	try {
		const newDepositGroup = await api.createDepositGroup(options);
		depositGroupsRef.value.push(newDepositGroup);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type UpdateDepositGroupStateOption = {
	key: Pick<DepositGroup, 'id'>;
	data: Partial<Pick<DepositGroup, 'name' | 'active' | 'costGross' | 'vat'>>;
}
async function updateDepositGroup(options: UpdateDepositGroupStateOption): Promise<void> {
	log.debug('updateDepositGroup()', options);
	isLoadingRef.value = true;
	try {
		const updatedDepositGroup = await api.updateDepositGroup(options);
		const index = depositGroupsRef.value.findIndex((oldDepositGroup) =>oldDepositGroup.id == updatedDepositGroup.id);
		if (index == -1) {
			log.debug('updateDepositGroup()', 'Could not find DepositGroup');
			return;
		}
		Vue.set(depositGroupsRef.value, index, updatedDepositGroup);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type DeleteDepositGroupStateOption = {
	key: Pick<DepositGroup, 'id'>;
}
async function deleteDepositGroup(options: DeleteDepositGroupStateOption): Promise<void> {
	log.debug('deleteDepositGroup()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteDepositGroup(options);
		depositGroupsRef.value = depositGroupsRef.value.filter((depositGroup) => depositGroup.id !== options.key.id);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}