<template>
  <div>
    <button v-if="state == 'created'" type="button" class="btn btn-primary float-right" @click="save">
      S<PERSON>ichern
    </button>
    <button v-if="state == 'loading'" type="button" class="btn btn-primary float-right" disabled>
      <span class="spinner-border spinner-border-sm"></span>
      Loading...
    </button>
    <button v-if="state == 'success'" type="button" class="btn btn-success float-right" @click="save">
      <FontAwesomeIcon icon='check'></FontAwesomeIcon>
      Erfolgreich
    </button>
    <button v-if="state == 'error'" type="button" class="btn btn-danger float-right" @click="save">
      <FontAwesomeIcon icon='exclamation-triangle'></FontAwesomeIcon>
      Es ist ein Fehler aufgetreten! ({{ error }})
    </button>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export default {
  data(){ return{
    error: null,
    state: 'created'
  }},
  components: { FontAwesomeIcon },
  methods:{
    save(){
      this.state = 'loading'
      this.$emit('save', this)
    },
    async setSuccess(){
      this.state = 'success'
      await sleep(2000)
      this.state = 'created'
    },
    async setError(error){
      this.error = error
      this.state = 'error'
      await sleep(5000)
      this.state = 'created'
      this.error = null
    }
  }
}
</script>
