import type { TicketGroup } from '@innevent/webapp-types';
import { apiInnTicket } from './instances';

type ModelType = TicketGroup;
type PrimaryKey = 'ticketGroupId';


export type TicketGroupCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'ticketGroupName'>;
}
export async function createTicketGroup(options: TicketGroupCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/TicketGroup', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type TicketGroupDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteTicketGroup(options: TicketGroupDeleteOptions): Promise<void> {
	await (await apiInnTicket()).delete(`/TicketGroup/${options.key.ticketGroupId}`, {
		params: { eventId: options.key.eventId } 
	});
}


export type TicketGroupUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'ticketGroupName'>>;
};

export async function updateTicketGroup(options: TicketGroupUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/TicketGroup/${options.key.ticketGroupId}`, 
		{ ticketGroupName: options.data.ticketGroupName }, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;

}


export type TicketGroupGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getTicketGroups(options: TicketGroupGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnTicket()).get('/TicketGroup', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}