import { apiInnOrder } from './instances';
import type { OldPermission, Tokenstation } from '@innevent/webapp-types';

type ModelType = Tokenstation;
type PrimaryKey = 'id';


export type TokenstationCreateOptions = {
	data: Pick<ModelType, 'EventId' | 'name'> & Partial<Pick<ModelType, 'description'>>;
}

export async function createTokenstation(options: TokenstationCreateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).post('/Tokenstation', options.data);
	const newTokenstation = {
		id: response.data,
		...options.data
	} as Tokenstation;
	
	return newTokenstation;
}

export type TokenstationDeleteOptions = {
	key: Pick<ModelType, PrimaryKey>;
}
export async function deleteTokenstation(options: TokenstationDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/Tokenstation/${options.key.id}`);
}

export type TokenstationUpdateOptions = {
	key: Pick<ModelType, PrimaryKey>;
	data: Partial<Pick<ModelType, 'name' | 'description'>>;
};

export async function updateTokenstation(options: TokenstationUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).put(`/Tokenstation/${options.key.id}`, {
		name: options.data.name,
		description: options.data.description
	});
	return response.data;
}


export type TokenstationGetOptions =  {
    key: Pick<ModelType, 'EventId'>;
};
export async function getTokenstations(options: TokenstationGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnOrder()).get(`/Tokenstation?filterEvent=${options.key.EventId}`);
	return response.data;
}

export async function getPermTokenstations(tokenstationId: string): Promise<OldPermission[] | never> {
	const response = await (await apiInnOrder()).get(`/Tokenstation/${tokenstationId}/Permission`);
	return response.data;
}

export type PermTokenstationCreateOptions = {
	data: Partial<Pick<OldPermission, 'permission' | 'userSubject' | 'TokenstationId' | 'eventId'>>;
};
export async function createPermTokenstation(options: PermTokenstationCreateOptions): Promise<OldPermission> {
	const response = await (await apiInnOrder()).post(`/Tokenstation/${options.data.TokenstationId}/Permission`, options.data);
	const newPermission = {
		id: response.data,
		...options.data
	} as OldPermission;
	
	return newPermission;
}

export type PermTokenstationDeleteOptions = {
	key: Pick<OldPermission, 'id'>;
}
export async function deletePermTokenstation(options: PermTokenstationDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/Tokenstation/Permission/${options.key.id}`);
}