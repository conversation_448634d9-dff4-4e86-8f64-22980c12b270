import * as log from 'loglevel';
import * as logPrefixer from 'loglevel-plugin-prefix';

logPrefixer.reg(log);
logPrefixer.apply(log, {
	format(level, loggerName, timestamp) {
		return `[${timestamp}][${loggerName}][${level}]`;
	}
});

log.setDefaultLevel('DEBUG');

export const router = log.getLogger('router');
export const app = log.getLogger('app');
export const vendorState = log.getLogger('state-vendor');
export const userState = log.getLogger('state-user');