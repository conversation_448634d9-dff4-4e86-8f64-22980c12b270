import type { QueryCommandInput } from '@aws-sdk/lib-dynamodb';

export type ModelKeyBoolean<ModelType> = { [attributeKey in keyof Partial<ModelType>]: boolean }

export type KeyObject = {
    [key: string]: true;
}

export type Currency = {
    iso4217Code: 'EUR';
    symbol: '€';
}

export type Price = {
    vatPercent: number;
    priceGross: number;
    currency?: Currency;
}

export type ChangeHistoryItemType = 'create' | 'update' | 'add' | 'remove';
export type ChangeHistoryMap = {
    [createdOn: string]: ChangeHistoryItem;
};
export type ChangeHistoryItem = {
    changeHistoryItemId: string;
    type: ChangeHistoryItemType;
    createdOn: string;
    changedAttributes: {
        [key: string]: {
            key: string;
            newValue: unknown;
        };
    };
    message: string;
}

export type SortDirection = 'ascending' | 'descending';
export type InnEventQueryResult<ModelType> = {
	items: ModelType[];
	startKey: string;
	lastEvaluatedKey: string;
	itemLimit: number;
	sortField: string;
	sortDirection: SortDirection;
}

export type InnEventQueryOptions<ModelType> = {
    partitionKeyValue: string;
	exclude?: ModelKeyBoolean<ModelType>;
    startKey?: string;
    itemLimit?: number;
    sortField?: string;
	sortDirection?: SortDirection;
}

export type QueryCommandInputAttributes =
    Pick<QueryCommandInput, 
        'KeyConditionExpression' |
        'Limit' | 
        'ExpressionAttributeValues' | 
        'ScanIndexForward'> &
    Partial<Pick<QueryCommandInput,
        'ExclusiveStartKey' |
        'IndexName'>>


export type PartialPick<T, K extends keyof T> = { [P in K]?: T[P]; };
export type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;