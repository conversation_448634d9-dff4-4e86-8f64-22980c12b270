export type Tokenstation = {
    id: string;
	name: string;
	description: string;
	EventId: string;
}

export type OldPermission = {
	id: string;
	eventId: string;
	userSubject: string;
	permission: string; 
	TokenstationId: string;
}

export type DepositGroup = {
	id?: string;
	name: string;
	active: boolean;
	costGross: string;
	vat: number;
	EventId: string;
}

export type VendorInvitation = {
	id?: string;
	mail: string;
	status: string;
	message: string;
	VendorId?: number;
	EventId: string;
}

export type VendorEventProperty = {
	id?: string;
	VendorId: number;
	EventId: string;
	Vendor: Vendor;
}

export type Vendor = {
	id?: string;
	name: string;
	organizationName: string;
	organizationType: string;
	street: string;
	housenumber: string;
	postalcode: string;
	city: string;
}