import type { Optional } from '@innevent/types';
import type { ComputedRef } from '@vue/composition-api';
import { computed, ref } from '@vue/composition-api';
import { vendor as api } from '@innevent/webapp-api';
import { router } from '../../router';
//import { loadEventEmployee, loadEventEmployeesOfEvent } from '.';
import { vendorState as log } from '../loglevel';
import type { Vendor } from '@innevent/webapp-types';

log.debug('INIT eventState');

//const { currentUserRef } = useUserState();

const isLoadingRef = ref<boolean>(false);
const permittedVendorsRef = ref<Vendor[]>([]);
const currentVendorIdRef = ref<string | undefined>(undefined);

// watch([currentEventIdRef, currentUserRef], ([eventId, cognitoUser]) => {
// 	log.debug('WATCH currentEventIdRef, currentUserRef', eventId, cognitoUser);
// 	if (eventId && cognitoUser) {
// 		loadEventEmployee(eventId as string, (cognitoUser as CustomCognitoUser).attributes.sub);
// 		loadEventEmployeesOfEvent(eventId as string);
// 	}
// });

export const currentVendorRef: ComputedRef<Vendor | undefined> = computed(() => {
	log.debug('COMPUTED currentVendorRef START');
	const currentVendor = permittedVendorsRef.value.find(vendor => vendor.id === currentVendorIdRef.value);
	log.debug('COMPUTED currentVendorRef END', currentVendor);
	return currentVendor;
});

async function reloadPermittedVendors() {
	log.debug('reloadPermittedVendors()');
	isLoadingRef.value = true;
	try {
		permittedVendorsRef.value = await api.getPermittetVendors();
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function createVendor(newVendor: Optional<Vendor, 'id'>): Promise<Vendor> {
	log.debug('createVendor()', newVendor);
	isLoadingRef.value = true;
	try {
		const createdVendor = await api.createVendor({
			data: newVendor
		});
		isLoadingRef.value = false;
		permittedVendorsRef.value.push(createdVendor);
		router.push({
			name: 'vBasedata',
			params: {
				vendor: createdVendor.id!
			}
		});
		return createdVendor;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function deleteVendor(vendorId: string): Promise<void> {
	log.debug('deleteVendor()', vendorId);
	isLoadingRef.value = true;
	try {
		await api.deleteVendor({
			key: { id: vendorId }
		});
		permittedVendorsRef.value.filter(vendor => vendor.id !== vendorId);
		if (currentVendorRef.value?.id === vendorId) {
			router.push({
				name: 'portalHome'
			});
		}
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function updateVendor(updateAttributes: Vendor): Promise<void> {
	log.debug('updateVendor()', updateAttributes);
	isLoadingRef.value = true;
	try {
		const updatedVendor = await api.updateVendor({
			key: { id: updateAttributes.id },
			data: {
				name: updateAttributes.name,
				organizationName: updateAttributes.organizationName,
				organizationType: updateAttributes.organizationType,
				city: updateAttributes.city,
				housenumber: updateAttributes.housenumber,
				postalcode: updateAttributes.postalcode,
				street: updateAttributes.street
			}
		});
		permittedVendorsRef.value = permittedVendorsRef.value.map(vendor => {
			return vendor.id === updateAttributes.id ? updatedVendor : vendor;
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useEventState() {
	log.debug('useEventState()');

	return {
		isLoadingRef,
		currentVendorRef,
		permittedVendorsRef,
		reloadPermittedVendors,
		createVendor,
		deleteVendor,
		updateVendor
	};
}