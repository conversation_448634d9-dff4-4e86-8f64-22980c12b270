<template>
  <b-row class="justify-content-center mt-5">
    <div class="innLoginForm">
      <div class="text-center mb-4">
        <img
          class="mb-4"
          src="../img/logo-transparent.png"
          alt=""
          width="150"
          height="150"
        >
        <h1 class="h4 mb-3 font-weight-normal">
          {{ title }}
        </h1>
      </div>
      <div class="d-flex justify-content-center">
        <b-spinner
          v-if="loading"
          variant="primary"
          type="grow"
          style="width: 100px; height: 100px; margin: 50px;"
        />
      </div>
      <div>
        <b-alert
          v-if="errorMessage"
          show
          variant="danger"
        >
          {{ errorMessage }}
        </b-alert>
        <b-alert
          v-if="successMessage"
          show
          variant="success"
        >
          {{ successMessage }}
        </b-alert>
      </div>
      <b-button
        v-if="successMessage || errorMessage"
        block
        variant="outline-primary"
        to="/login"
      >
        Anmelden
      </b-button>
    </div>
  </b-row>
</template>
<script>


import { Auth } from 'aws-amplify';
import * as api from '@innevent/webapp-api';
let URL = require('url-parse');

export default {
	data() { return {
		url: null,
		errorMessage: null,
		successMessage: null,
		title: 'E-Mail Adresse bestätigen',
		loading: true
	};},
	async created() {
		this.url = new URL(window.location.href, true);
		this.confirmEmail();
	},
	methods: {
		async confirmEmail() {
			let q = this.url.query;
			if (!q || !q.requestId) {
				this.errorMessage = 'URL Parameters invalid';
				this.loading = false;
				return;
			}
			try {
				await api.confirmEmailChangeRequest({
					key: {
						emailChangeRequestId: q.requestId
					}
				});
				this.success();
			} catch (error) {
				if (error.response.status == 404) this.errorMessage = 'Request abgelaufen oder ungültig';
				else this.errorMessage = 'Unbekannter Fehler';
				console.log(error.response);
			}
			this.loading = false;
		},
		async success() {
			await Auth.signOut();
			this.successMessage = 'Die E-Mail Adresse wurde erfolgreich verifiziert und geändert. Bitte neu anmelden';
			// if(this.urlParams.redirect_uri){
			//   var url = buildUrl(this.urlParams.redirect_uri, {})
			//   window.location.replace(url)
			// }
		}
	}
};
</script>
