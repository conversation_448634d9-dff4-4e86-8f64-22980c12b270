<template>
  <div>
    <h2>InnOrder Dashboard</h2>
    <LoadingSpinner v-if="!tokendata" />
    <div v-else>
      <h4>NFC-Tag Infos</h4>
      <div class="mt-3">
        <b-card-group deck>
          <b-card
            bg-variant="light"
            header="Anzahl Tags im Umlauf"
            class="text-center"
          >
            <b-card-text>{{ tokendata.nfcInfo.countNfcTagsUse }}</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Anzahl Tags Zurückgegeben"
            class="text-center"
          >
            <b-card-text>{{ tokendata.nfcInfo.countNfcTagsReturned }}</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Anzahl Tags defekt"
            class="text-center"
          >
            <b-card-text>{{ tokendata.nfcInfo.countNfcTagsDefect }}</b-card-text>
          </b-card>
        </b-card-group>
      </div>
      <div class="mt-3">
        <b-card-group deck>
          <b-card
            bg-variant="light"
            header="Guthaben aller Tags"
            class="text-center"
          >
            <b-card-text>{{ tokendata.nfcInfo.nfcTagsSaldo[0].sum }} €</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Durchschnittliches Guthaben"
            class="text-center"
          >
            <b-card-text>{{ tokendata.nfcInfo.nfcTagsSaldo[0].avg }} €</b-card-text>
          </b-card>
        </b-card-group>
      </div>
      <h4 class="mt-4">
        Aufladungen
      </h4>
      <div class="mt-3">
        <b-card-group deck>
          <b-card
            bg-variant="light"
            header="Anzahl Aufladungen"
            class="text-center"
          >
            <b-card-text>{{ tokendata.topUp.countAndSaldoTopUps.count }}</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Betrag aller Aufladungen"
            class="text-center"
          >
            <b-card-text>{{ tokendata.topUp.countAndSaldoTopUps.sum }} €</b-card-text>
          </b-card>
        </b-card-group>
      </div>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><PieChart :chartdata="chartDataTopUpCount" /></b-col>
            <b-col><PieChart :chartdata="chartDataTopUpSaldo" /></b-col>
          </b-row>
          <b-row>
            <b-col><LineChart :chartdata="chartCountTopUpsPerHour" /></b-col>
            <b-col><LineChartCurrency :chartdata="chartSalesTopUpsPerHour" /></b-col>
          </b-row>
        </b-container>
      </div>

      <h4 class="mt-4">
        Abbuchungen
      </h4>
      <div class="mt-3">
        <b-card-group deck>
          <b-card
            bg-variant="light"
            header="Anzahl Abbuchungen"
            class="text-center"
          >
            <b-card-text>{{ tokendata.topDown.countAndSaldoTopDowns.count }}</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Betrag aller Abbuchungen"
            class="text-center"
          >
            <b-card-text>{{ Math.abs(tokendata.topDown.countAndSaldoTopDowns.sum ) }} €</b-card-text>
          </b-card>
        </b-card-group>
      </div>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><LineChart :chartdata="chartCountTopDownsPerHour" /></b-col>
            <b-col>
              <LineChartCurrency
                :chartdata="chartSalesTopDownsPerHour"
                :reverse="true"
              />
            </b-col>
          </b-row>
        </b-container>
      </div>
    </div>
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import { defineComponent } from '@vue/composition-api';
import LineChart from './elements/dashboardcharts/LineChart.vue';
import LineChartCurrency from './elements/dashboardcharts/LineChartCurrency.vue';
import HorizontalBarChart from './elements/dashboardcharts/HorizontalBarChart.vue';
import { currentEventRef } from '../../../states/eventState';
import PieChart from './elements/dashboardcharts/PieChart.vue';
import LoadingSpinner from '../../elements/LoadingSpinner.vue';

export default defineComponent({
	components: { HorizontalBarChart, LineChart, PieChart, LineChartCurrency, LoadingSpinner },
	setup() {
		return {
			currentEventRef
		};
	},
	data() { return {
		tokendata: null
	};},
	computed: {
		chartDataTopUpCount() {
			let labels = this.tokendata.topUp.countAndSaldoTopUpsByPaymentMethod.map((data) => data.paymentMethod);
			let countData = this.tokendata.topUp.countAndSaldoTopUpsByPaymentMethod.map((data) => data.count);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl',
						backgroundColor: [
							'rgba(255, 99, 132, 0.2)',
							'rgba(54, 162, 235, 0.2)',
							'rgba(255, 206, 86, 0.2)' ],
						data: countData
					}
				]
			};
		},
		chartDataTopUpSaldo() {
			let labels = this.tokendata.topUp.countAndSaldoTopUpsByPaymentMethod.map((data) => data.paymentMethod);
			let sumData = this.tokendata.topUp.countAndSaldoTopUpsByPaymentMethod.map((data) => data.sum);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Umsatz',
						backgroundColor: [
							'rgba(255, 99, 132, 0.2)',
							'rgba(54, 162, 235, 0.2)',
							'rgba(255, 206, 86, 0.2)' ],
						data: sumData
					}
				]
			};
		},
		chartCountTopUpsPerHour() {
			let labels = this.tokendata.topUp.countTopUpsPerHour.map((data) => new Date(data.datetime));
			let countData = this.tokendata.topUp.countTopUpsPerHour.map((data) => data.count);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl Topups pro Stunde',
						backgroundColor: 'rgba(255, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartSalesTopUpsPerHour() {
			let labels = this.tokendata.topUp.salesTopUpsPerHour.map((data) => new Date(data.datetime));
			let countData = this.tokendata.topUp.salesTopUpsPerHour.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Betrag Topups pro Stunde',
						backgroundColor: 'rgba(20, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartCountTopDownsPerHour() {
			let labels = this.tokendata.topDown.countTopDownsPerHour.map((data) => new Date(data.datetime));
			let countData = this.tokendata.topDown.countTopDownsPerHour.map((data) => data.count);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl TopDowns pro Stunde',
						backgroundColor: 'rgba(20, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartSalesTopDownsPerHour() {
			let labels = this.tokendata.topDown.salesTopDownsPerHour.map((data) => new Date(data.datetime));
			let countData = this.tokendata.topDown.salesTopDownsPerHour.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Betrag TopDowns pro Stunde',
						backgroundColor: 'rgba(20, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartDataTop10EventCasboxes() {
			let labels = this.tokendata.cashbox.top10EventCasboxes.map((data) => data.name);
			let countData = this.tokendata.cashbox.top10EventCasboxes.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Top 10 Event Cashboxes',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					}
				]
			};
		},
		chartDataTop10VendorCasboxes() {
			let labels = this.tokendata.cashbox.top10VendorCasboxes.map((data) => data.name);
			let countData = this.tokendata.cashbox.top10VendorCasboxes.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Top 10 Vendor Cashboxes',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					}
				]
			};
		},
		chartDataTop10EventECTerminals() {
			let labels = this.tokendata.ecterminal.top10EventECTerminals.map((data) => data.name);
			let countData = this.tokendata.ecterminal.top10EventECTerminals.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Top 10 Event EC-Terminals',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					}
				]
			};
		},
		chartDataTop10VendorECTerminals() {
			let labels = this.tokendata.ecterminal.top10VendorECTerminals.map((data) => data.name);
			let countData = this.tokendata.ecterminal.top10VendorECTerminals.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Top 10 Vendor EC-Terminals',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					}
				]
			};
		}
	},
	created() {
		this.getTokenstationData();
	},
	methods: {
		getTokenstationData() {
			this.tokendata = null;
			API.get('innorder', '/Dashboard/OrganizerOrderData?EventId=' + currentEventRef.value.eventId)
				.then((response) => {
					this.tokendata = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der Tokenstation Daten ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		}
	}
});
</script>
<style scoped>

</style>