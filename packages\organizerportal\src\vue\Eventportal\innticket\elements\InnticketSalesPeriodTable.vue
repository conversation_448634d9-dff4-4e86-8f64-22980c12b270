<template>
  <div>
    <b-row>
      <b-col>
        <h2>Verkaufsphasen</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto"
            variant="primary"
            size="sm"
            @click="btnCreateSalesPeriod()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Verkaufsphase erstellen
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      v-if="ticketMapRef"
      id="salesPeriodTable"
      ref="salesPeriodTable"
      hover
      small
      :busy="isLoadingRef"
      :items="ticketMapRef.salesPeriods"
      :fields="tableFields"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(timeFrom)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(timeTo)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text=""
            variant="danger"
            @click="btnDeleteSalesPeriod($event, data)"
          />
        </div>
      </template>
    </b-table>
    <SalesPeriodSidebar 
      ref="salesPeriodSidebar"
      :eventId="eventId"
      @salesPeriodChanged="onSalesPeriodChange"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import SalesPeriodSidebar from './InnticketSalesPeriodSidebar.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton, SalesPeriod } from '@innevent/webapp-types';
import * as api from '@innevent/webapp-api';
import { formatDate } from '@innevent/webapp-utils';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useTicketMapState } from '../../../../states/ticketMap';

export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		SalesPeriodSidebar
	},

	setup() {
		const instance = getCurrentInstance();
		const { ticketMapRef, isLoadingRef } = useTicketMapState();
		const salesPeriodTable = ref<BTable>();
		const salesPeriodSidebar = ref<InstanceType<typeof SalesPeriodSidebar>>();

		const tableFields: BvTableFieldArray =  [
			{ key: 'salesPeriodName', label: 'Name', sortable: true },
			{ key: 'timeFrom', label: 'Startdatum', sortable: true },
			{ key: 'timeTo', label: 'Enddatum' },
			{ key: 'buttons', label: '' }
		];

		async function btnDeleteSalesPeriod(btn: ResetButton, cellData: BvTableCellData<SalesPeriod>) {
			try {
				await api.deleteSalesPeriod({
					key: {
						eventId: cellData.item.eventId,
						salesPeriodId: cellData.item.salesPeriodId
					}
				});
				ticketMapRef.value.salesPeriods = ticketMapRef.value.salesPeriods.filter(item => 
					(item.salesPeriodId !== cellData.item.salesPeriodId));
				ticketMapRef.value.tickets.forEach((ticket)=> {
					const ticketSalesProperties = Object.keys(ticket.ticketSaleProperties);
					if (ticketSalesProperties.includes(cellData.item.salesPeriodId)) {
						delete ticket.ticketSaleProperties[cellData.item.salesPeriodId];
					}
				});

				onSalesPeriodChange();
				notifySuccess({ instance });
			} catch (error) {
				console.log(error);
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(salesPeriod: SalesPeriod): void {
			salesPeriodSidebar.value?.openForEdit(salesPeriod);
		}

		function onSalesPeriodChange() {
			salesPeriodTable.value?.refresh();
		}

		function btnCreateSalesPeriod(): void {
			salesPeriodSidebar.value?.openForCreate();
		}
		

		return {
			tableFields,
			salesPeriodSidebar,
			salesPeriodTable,
			btnDeleteSalesPeriod,
			onSalesPeriodChange,
			tableRowClick,
			btnCreateSalesPeriod,
			formatDate,
			ticketMapRef,
			isLoadingRef
		};
	}
});
</script>

<style>
</style>
