<template>
  <div>
    <Header
      :portal-name="portalName"
      :current-portal-object-id="currentPortalObjectId"
      :portal-objects="portalObjects"
      :cognito-user="cognitoUser"
      @logout="logout"
      @navigate="navigate"
    />
    <LoadingSpinner v-if="currentPortalObjectId == null" />
    <div
      v-else
      class="container-fluid"
    >
      <div class="row">
        <Navigation
          class="col-12 col-md-3 col-xl-2 border-right"
          :current-portal-object-id="currentPortalObjectId"
          :current-nav="currentNav"
          :main-content="mainContent"
          :selected-event="selectedEvent"
          :event-properties="eventProperties"
          :events="events"
          @showMainNav="showMainNav"
          @navigate="navigate"
        />
        <Main
          class="col-12 col-md-9 col-xl-10 offset-md-3 offset-xl-2"
          :current-portal-object-id="currentPortalObjectId"
          :current-nav="currentNav"
          :main-content="mainContent"
          :portal-objects="portalObjects"
          :event-properties="eventProperties"
          :events="events"
          :selected-event="selectedEvent"
          @navigate="navigate"
        />
      </div>
    </div>
  </div>
</template>
<script>

// Frameworks
import { Auth } from 'aws-amplify';

// Vue Components
import Header from './Header.vue';
import Main from './Main.vue';
import LoadingSpinner from './LoadingSpinner.vue';
import Navigation from './Navigation.vue';

export default {
	components: { Header, Main, LoadingSpinner, Navigation },
	props: ['cognitoUser'],
	data() { return {
		portalName: 'INN//SYSTEMS Organizer Portal'
	};},
	watch:{
		currentPortalObjectId: function(val) {
			if (val) {
				this.loadEventPropertys();
			}
		}
	},
	created: async function() {

	},
	methods:{
		showMainNav() {
			this.currentNav = 'main';
		},
		showSubNav() {
			this.currentNav = 'sub';
		},
		navigate(site, ev = null) {
			this.mainContent = site;
			if (site.substr(0, 3) == 'sub') {
				this.showSubNav();
			} else {
				this.showMainNav();
			}
			if (ev) {
				this.selectedEvent = ev;
			}
		},
		async logout() {
			try {
				await Auth.signOut();
				window.location.href = 'http://localhost:8080?logout=true';
				console.log('logged out');
			} catch (error) {
				console.log(error);
			}
		}
	}
};

</script>
