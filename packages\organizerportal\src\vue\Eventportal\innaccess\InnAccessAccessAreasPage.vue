<template>
  <b-container fluid>
    <AccessAreaTable />
  </b-container>
</template>
<script lang="ts">
import AccessAreaTable from './elements/InnAccessAccessAreaTable.vue';
import { onMounted, defineComponent } from '@vue/composition-api';
import { loadAccessAreas } from '../../../states/accessAreaState';

export default defineComponent({
	components: {
		AccessAreaTable
	},
	setup() {
		onMounted(async () => {
			await loadAccessAreas();
		});
		
		return {  };
	}
	
});
</script>
