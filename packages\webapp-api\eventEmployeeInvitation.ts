import { apiInnEvent } from './instances';
import type { EventEmployeeInvitation } from '@innevent/types';

type ModelType = EventEmployeeInvitation;
type PrimaryKey = 'eventEmployeeInvitationId';

export type EventEmployeeInvitationCreateOptions = {
	data: Pick<ModelType, 'eventId' | 'email' | 'message'>;
}
export async function createEventEmployeeInvitation(options: EventEmployeeInvitationCreateOptions): Promise<Required<ModelType>> {
	const response = await (await apiInnEvent()).post('/EventEmployeeInvitation', options.data, { 
		params: { 
			eventId: options.data.eventId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}


export type GetEventEmployeeInvitationOptions =  {
    key: Pick<ModelType, 'eventId' | 'eventEmployeeInvitationId'>;
};
export async function getEventEmployeeInvitation(options: GetEventEmployeeInvitationOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).get(`/EventEmployeeInvitation/${options.key.eventEmployeeInvitationId}`, { 
		params: { 
			eventId: options.key.eventId 
		} 
	});
	return response.data;
}

export type GetEventEmployeeInvitationsOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getEventEmployeeInvitations(options: GetEventEmployeeInvitationsOptions): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/EventEmployeeInvitation', { 
		params: {
			eventId: options.key.eventId
		} 
	});
	return response.data;
}

export type EventEmployeeInvitationDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteEventEmployeeInvitation(options: EventEmployeeInvitationDeleteOptions): Promise<void> {
	await (await apiInnEvent()).delete(`/EventEmployeeInvitation/${options.key.eventEmployeeInvitationId}`, { 
		params: { 
			eventId: options.key.eventId
		} 
	});
}

export type ResendEventEmployeeInvitationOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}

export async function resendInvitation(options: ResendEventEmployeeInvitationOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/EventEmployeeInvitation/${options.key.eventEmployeeInvitationId}/ResendEmail`, {}, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}

export type AnswerEventEmployeeInvitationOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}

export async function acceptInvitation(options: AnswerEventEmployeeInvitationOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/EventEmployeeInvitation/${options.key.eventEmployeeInvitationId}/Accept`, {}, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}

export async function declineInvitation(options: AnswerEventEmployeeInvitationOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/EventEmployeeInvitation/${options.key.eventEmployeeInvitationId}/Decline`, {}, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}