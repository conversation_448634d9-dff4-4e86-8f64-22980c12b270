<template>
  <div>
    <Header
      :events="events"
      :organizers="permittedOrganizersRef"
      :container-class="containerClass"
    />
    <router-view
      :class="containerClass"
      :events="permittedEventsRef"
      :organizers="organizers"
    />
    <ModalCreateEvent :organizers="organizers" />
    <ModalCreateOrganizer />
  </div>
</template>
<script lang="ts">

// Frameworks
import Header from './Header.vue';
import ModalCreateEvent from './elements/ModalCreateEvent.vue';
import ModalCreateOrganizer from './elements/ModalCreateOrganizer.vue';
import { useOrganizerState } from '../states/organizerState';
import { useEventState } from '../states/eventState';
import { useUserState } from '../states/userState';
import { defineComponent } from '@vue/composition-api';
import { app as log } from '../loglevel';

export default defineComponent({
	components: { Header, ModalCreateOrganizer, ModalCreateEvent },
	setup() {
		log.debug('App.vue setup()');
		const { reloadPermittedOrganizers, permittedOrganizersRef } = useOrganizerState();
		const { reloadPermittedEvents, permittedEventsRef } = useEventState();
		const { reloadUser } = useUserState();

		log.debug('reloadPermittedOrganizers()');
		reloadPermittedOrganizers();
		log.debug('reloadPermittedEvents()');
		reloadPermittedEvents();
		log.debug('reloadUser()');
		reloadUser();


		const containerClass = 'max-w2000';
		
		return {
			permittedEventsRef,
			permittedOrganizersRef,
			containerClass
		};
	}
});

</script>
