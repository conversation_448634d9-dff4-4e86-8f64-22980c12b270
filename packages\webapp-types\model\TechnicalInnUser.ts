/* eslint-disable @typescript-eslint/naming-convention */

export type TechnicalInnUser = {
    email: string;
    firstName: string;
    lastName: string;
    technicalPassword: string;
    technicalRefId: string;
    technicalRefObject: string;
    userSubject: string;
    eventEmployees?: EventEmployee[];
    vendorEmployees?: VendorEmployee[];
}

export type TechnicalUserReferenceObjectType = 'event' | 'vendor';

export type Employee = {
    id?: string;
    UserId: string;
}

export type EventEmployee = Employee & {
    EventId: string;
}

export type VendorEmployee = Employee & {
    VendorId: string;
}