<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Ticketgruppen"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h2>Allgemeines</h2>
      <b-row
        v-for="(field, fieldKey) of inputFields"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col sm="3">
          <label :for="`field-${fieldKey}`">{{ field.name }}:</label>
        </b-col>
        <b-col
          v-if="field.type == 'text'"
          sm="9"
        >
          <b-form-input
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            @keyup.enter="onKeyupEnter()"
          />
        </b-col>
      </b-row>
    </div>
    <LoadButton
      ref="saveButton"
      variant="primary"
      @click="clickOnSave"
    />
  </b-sidebar>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import * as api from '@innevent/webapp-api';
import type { ModalAction, ResetButton, TicketGroup } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useTicketMapState } from '../../../../states/ticketMap';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';
import { currentEventRef } from '../../../../states/eventState';


export default defineComponent({
	components: {
		LoadButton
	},
	emits: ['ticketGroupChanged'],
	setup(props, { emit }) {
		const actionRef = ref<ModalAction>('create');
		const { ticketMapRef, isLoadingRef } = useTicketMapState();
		const sidebarOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentTicketGroup = ref<TicketGroup>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();

		async function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function clickOnSave(btn: ResetButton) {
			await saveTicketGroup();
			btn.reset();
		}

		async function saveTicketGroup(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					const ticketGroup = await api.createTicketGroup({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							ticketGroupName: inputFields.value.ticketGroupName.value
						}
					});
					ticketMapRef.value.ticketGroups.push(ticketGroup);
					emit('ticketGroupChanged');
				} else {
					if (!currentTicketGroup.value) throw 'NoValue';
					const ticketGroup = await api.updateTicketGroup({
						key: {
							eventId: currentTicketGroup.value?.eventId,
							ticketGroupId: currentTicketGroup.value?.ticketGroupId
						},
						data: {
							ticketGroupName: inputFields.value.ticketGroupName.value
						}
					});
					const index = ticketMapRef.value.ticketGroups.findIndex(element => element.ticketGroupId == ticketGroup.ticketGroupId);
					ticketMapRef.value.ticketGroups.splice(index, 1, ticketGroup);
					emit('ticketGroupChanged');
				}
				notifySuccess({ instance });
				sidebarOpen.value = false;
			} catch (error) {
				if (error.isAxiosError) {
					const axiosError: AxiosError = error;
					const response: InnEventError = axiosError.response?.data;

					if (response.isInnEventError) {
						if (response.errorCode == 'ValidationError') {
							notifyError({ instance, title: 'Validierungsfehler', message: 'Die Felder wurden nicht richtig gefüllt' });
							return;
						}	
					} 
				}
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;
			inputFields.value.ticketGroupName.value = '';
		}

		function openForEdit(ticketGroup: TicketGroup) {
			actionRef.value = 'edit';
			sidebarOpen.value = true;
			currentTicketGroup.value = ticketGroup;
			inputFields.value.ticketGroupName.value = ticketGroup.ticketGroupName;
		}

		const inputFields = ref({
			ticketGroupName: {
				name: 'Name',
				type: 'text',
				value: ''
			}
		});


		return {
			actionRef,
			sidebarOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			openForCreate,
			openForEdit,
			saveTicketGroup
		};
	}
});
</script>
<style>
</style>