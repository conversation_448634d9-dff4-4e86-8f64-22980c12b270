<template>
  <b-container class="mt-5 mb-5">
    <template v-if="state == 'loading'">
      <b-skeleton width="85%" />
      <b-skeleton width="55%" />
      <b-skeleton width="70%" />
    </template>
    <b-card
      v-if="state == 'invalid'"
      bg-variant="danger"
      text-variant="white"
      header="Request Invalid"
      class="text-center"
    >
      <b-card-text>Dieser Link scheint fehlerhaft oder nicht mehr gültig zu sein.</b-card-text>
    </b-card>
    <b-card
      v-if="state == 'validInvite'"
      title="Verkäufer Einladung zur Zusammenarbeit"
      sub-title="INN//Order"
    >
      <b-card-text>
        Hallo{{ cognitoUser && cognitoUser.attributes.given_name ? " " + cognitoUser.attributes.given_name:"" }},<br>
        du wurdest eingeladen, bei der Veranstaltung {{ invitation.Event.name }} deine Produkte zu verkaufen.<br>
      </b-card-text>
      <b-card-text v-if="invitation && invitation.message">
        Folgende Nachricht wurde dir hinterlassen:<br>{{ invitation.message }}
      </b-card-text>
      <b-card-text>
        Bitte wähle einen bestehenden Verkäufer Account für dieses Event aus, oder erstelle einen neuen.
      </b-card-text>
      <b-list-group
        v-if="vendors.length > 0"
        class="mb-3"
      >
        <b-list-group-item
          v-for="vendor of vendors"
          :key="vendor.id"
          class="d-flex justify-content-between"
        >
          {{ vendor.name }}
          <LoadButton
            variant="primary"
            text="Verbinden"
            icon="link"
            size="sm"
            @click="answerInvitation($event, 'ACCEPTED', vendor)"
          />
        </b-list-group-item>
      </b-list-group>
      <b-card-text v-else>
        Kein Vendor verfügbar, bitte erstelle einen.
      </b-card-text>
      <div class="d-flex justify-content-between">
        <LoadButton
          variant="outline-danger"
          text="Einladung löschen"
          icon="trash"
          size="sm"
          @click="answerInvitation($event, 'DECLINED')"
        />
        <LoadButton
          v-b-modal.modalCreateVendor
          class="ml-auto"
          variant="primary"
          text="Vendor erstellen"
          icon="plus-square"
          size="sm"
          @click="btn => btn.reset()"
        />
      </div>
    </b-card>
    <b-card
      v-if="showResultAlert"
      class="text-center"
    >
      <b-alert
        show
        :variant="variantResultAlert"
      >
        {{ textResultAlert }}
      </b-alert>
      <b-link :to="{ name: 'eDashboard', params: { event: invitation.EventId, vendor: invitation.VendorId }}">
        Weiter zum Vendorportal
      </b-link>
    </b-card>
  </b-container>
</template>
<script>

import { Auth, API } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';


export default {
	components: { LoadButton },
	props: ['vendors'],
	data() { return {
		state: 'loading',
		invitation: null,
		showResultAlert: false,
		textResultAlert: '',
		variantResultAlert: 'success',
		cognitoUser: null
	};},
	computed: {

	},
	async created() {
		this.cognitoUser = await Auth.currentAuthenticatedUser();
		this.loadInvitation();
	},
	methods:{
		loadInvitation() {
			API.get('innorder', '/VendorInvitation/' + this.$route.params.invitationId).then(response => {
				this.invitation = response;
				this.state = 'validInvite';
			}).catch(error => {
				this.state = 'invalid';
			});
		},
		answerInvitation(btn, status, vendor) {
			this.invitation.status = status;
			this.invitation.VendorId = vendor.id;
			API.put('innorder', '/VendorInvitation/' + this.invitation.id, { body: this.invitation }).then(response => {
				this.variantResultAlert = 'success';
				this.showResultAlert = true;
				this.textResultAlert = 'Deine Antwort wurde erfolgreich an den Veranstalter übermittelt.';
				this.state = 'successAnswer';
				btn.reset();
			}).catch(error => {
				this.showResultAlert = true;
				this.variantResultAlert = 'danger';
				this.textResultAlert = 'Es ist ein Fehler aufgetreten, bitte versuchen Sie es später erneut';
				btn.reset();
			});
		}
	}
};
</script>
<style scoped>

</style>
