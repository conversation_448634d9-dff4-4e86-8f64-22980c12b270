<template>
  <b-container fluid>
    <ImportTable />
  </b-container>
</template>
<script lang="ts">
import ImportTable from './elements/InnticketImportTable.vue';
import { onMounted } from '@vue/composition-api';
import { defineComponent } from '@vue/composition-api';
import { refreshTicketImportMap } from '../../../states/ticketImport';

export default defineComponent({
	components: {
		ImportTable
	},
	setup() {
		onMounted(async () => {
			await refreshTicketImportMap();
		});
		
		return {};
	}
	
});
</script>
