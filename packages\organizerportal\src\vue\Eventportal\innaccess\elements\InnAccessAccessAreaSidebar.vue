<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="<PERSON><PERSON>rittsbereich"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h2>Allgemeines</h2>
      <b-row
        v-for="(field, fieldKey) of textFields"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col
          sm="3"
        >
          <label
            :for="`field-${fieldKey}`"
          >{{ field.name }}:</label>
        </b-col>
        <b-col
          sm="9"
        >
          <b-form-input
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            @keyup.enter="onKeyupEnter()"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-permittedTickets">Gültige Tickets:</label>
        </b-col>
        <b-col sm="9">
          <Multiselect
            v-model="validTickets"
            :options="ticketMapRef.tickets"
            :multiple="true"
            placeholder="Ticket auswählen"
            label="ticketName"
            track-by="ticketName"
            :allow-empty="true"
            :preselect-first="true"
            :close-on-select="false"
            @select="addTicket"
            @remove="removeTicket"
          />
        </b-col>
      </b-row>
    </div>
    <LoadButton
      ref="saveButton"
      variant="primary"
      @click="clickOnSave"
    />
  </b-sidebar>
</template>
<script lang="ts">
import type { ComputedRef, Ref } from '@vue/composition-api';
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import { createAccessArea, updateAccessArea } from '../../../../states/accessAreaState';
import type { ModalAction, ResetButton } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';
import type { Ticket } from '@innevent/types';
import Vue from 'vue';
import { useTicketMapState, currentEventRef } from '../../../../states';
import type { AccessArea } from '@innevent/types';

type InputField = {
	name: string;
	type: string;
	value: any;
}

export default defineComponent({
	components: {
		LoadButton
	},
	props: {
		eventId: {
			type: String,
			required: true
		}		
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentAccessAreaRef = ref<AccessArea>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();
		const { ticketMapRef } = useTicketMapState();
		

		function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function clickOnSave(btn: ResetButton) {
			await saveTicketGroup();
			btn.reset();
		}

		const validTickets = computed(() => {
			const ticketIds = Object.keys(inputFields.value.permittedTickets.value);
			return ticketMapRef.value.tickets.filter((ticket) => ticketIds.includes(ticket.ticketId));
		});

		const textFields: ComputedRef<{ [fieldKey: string]: string; }> = computed(() => {
			return Object.entries(inputFields.value).reduce((fields, [key, value]) => {
				if (value.type == 'text') {
					fields[key] = value;
				}
				return fields;
			}, {});
		});

		function removeTicket(value: Ticket) {
			Vue.delete(inputFields.value.permittedTickets.value, value.ticketId);
		}

		function addTicket(value: Ticket) {
			const shortTicket = {
				ticketId: value.ticketId,
				ticketName: value.ticketName
			};
			Vue.set(inputFields.value.permittedTickets.value, value.ticketId, shortTicket);
		}

		async function saveTicketGroup(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					await createAccessArea({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							accessAreaName: inputFields.value.accessAreaName.value,
							description: inputFields.value.description.value,
							permittedTickets: inputFields.value.permittedTickets.value
						}
					});
				} else {
					if (!currentAccessAreaRef.value) throw 'NoValue';
					await updateAccessArea({
						key: {
							eventId: currentEventRef.value!.eventId!,
							accessAreaId: currentAccessAreaRef.value.accessAreaId
						},
						data: {
							accessAreaName: inputFields.value.accessAreaName.value,
							description: inputFields.value.description.value,
							permittedTickets: inputFields.value.permittedTickets.value
						}
					});
				}
				notifySuccess({ instance });
				sidebarOpen.value = false;
			} catch (error: any) {
				if (error?.isAxiosError) {
					const axiosError: AxiosError = error;
					const response: InnEventError = axiosError.response?.data;

					if (response.isInnEventError) {
						if (response.errorCode == 'ValidationError') {
							notifyError({ instance, title: 'Validierungsfehler', message: 'Die Felder wurden nicht richtig gefüllt' });
							return;
						}	
					} 
				}
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;
			inputFields.value.accessAreaName.value = '';
			inputFields.value.description.value = '';
			inputFields.value.permittedTickets.value = {};
		}

		function openForEdit(accessArea: AccessArea) {
			actionRef.value = 'edit';
			sidebarOpen.value = true;
			currentAccessAreaRef.value = accessArea;
			inputFields.value.accessAreaName.value = accessArea.accessAreaName;
			inputFields.value.description.value = accessArea.description;
			inputFields.value.permittedTickets.value = accessArea.permittedTickets;
		}

		const inputFields: Ref<{ [fieldName: string]: InputField; }> = ref({
			accessAreaName: {
				name: 'Name',
				type: 'text',
				value: ''
			},
			description: {
				name: 'Beschreibung',
				type: 'text',
				value: ''
			},
			permittedTickets: {
				name: 'Gültige Tickets',
				type: 'individual',
				value: {}
			}
		});


		return {
			actionRef,
			sidebarOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			openForCreate,
			openForEdit,
			saveTicketGroup,
			validTickets, 
			removeTicket,
			addTicket,
			ticketMapRef,
			textFields
		};
	}
});
</script>
<style>
</style>