<template>
  <b-navbar
    toggleable="xl"
    type="dark"
    variant="dark"
    sticky
  >
    <b-container
      fluid
      :class="containerClass"
    >
      <b-navbar-brand href="#">
        <img
          height="40"
          src="../img/logo-transparent.png"
        >{{ headerText }}
      </b-navbar-brand>

      <b-navbar-toggle target="nav-collapse" />

      <b-collapse
        id="nav-collapse"
        is-nav
      >
        <b-navbar-nav>
          <b-nav-item :href="URL_ORGANIZERPORTAL">
            Veranstalterportal
          </b-nav-item>
          <b-nav-item :href="URL_CUSTOMERPORTAL">
            Kundenportal
          </b-nav-item>
        </b-navbar-nav>

        <b-navbar-nav
          class="ml-auto"
          pills
        >
          <b-nav-form class="mr-4">
            <b-button-group>
              <b-dropdown
                id="portalObjectSelect"
                right
                :text="selectText"
                variant="outline-light"
              >
                <b-dropdown-item-button
                  v-for="v of vendors"
                  :key="v.id"
                  @click="$router.push({ name: 'vBasedata', params: { vendor: v.id }})"
                >
                  {{ v.name }}
                </b-dropdown-item-button>
              </b-dropdown>
              <b-button
                :to="{ name: 'portalHome', params: {}}"
                variant="outline-light"
              >
                Übersicht
              </b-button>
            </b-button-group>
          </b-nav-form>
          <b-nav-item-dropdown right>
            <template #button-content>
              <FontAwesomeIcon
                icon="user-circle"
                fixed-width
              /><span class="ml-1 mr-1">{{ btnAccountText }}</span>
            </template>
            <b-dropdown-text class="text-center">
              {{ accountName }}
            </b-dropdown-text>
            <b-dropdown-text>{{ accountEmail }}</b-dropdown-text>
            <b-dropdown-divider />
            <b-dropdown-item :href="URL_INNLOGIN + '/account'">
              <FontAwesomeIcon
                icon="user-cog"
                fixed-width
              /><span class="ml-3">Kontoeinstellungen</span>
            </b-dropdown-item>
            <b-dropdown-item @click="logout()">
              <FontAwesomeIcon
                icon="power-off"
                fixed-width
              /><span class="ml-3">Abmelden</span>
            </b-dropdown-item>
          </b-nav-item-dropdown>
        </b-navbar-nav>
      </b-collapse>
    </b-container>
  </b-navbar>
</template>
<script>

import { Auth } from 'aws-amplify';

export default {
	components: { },
	props: {
		selectedVendor: Object,
		vendors: Array,
		cognitoUser: Object,
		containerClass: { type: String, default: 'max-w1600' }
	},
	data() { return {
		btnAccountText: 'Mein Konto',
		URL_VENDORPORTAL: process.env.URL_VENDORPORTAL,
		URL_ORGANIZERPORTAL: process.env.URL_ORGANIZERPORTAL,
		URL_CUSTOMERPORTAL: process.env.URL_CUSTOMERPORTAL,
		URL_INNLOGIN: process.env.URL_INNLOGIN
	};},
	computed:{
		headerText() {
			return ` Inn//Event Verkäufer${ process.env.STAGE == 'prod' ? '' : '__' + process.env.STAGE }`;
		},
		selectText() {
			if (this.$route.params.vendor) {
				let v = this.vendors.find(v => v.id == this.$route.params.vendor);
				if (v) return v.name;
			}
			else return 'Verkäufer auswählen';
		},
		accountEmail() {
			if (!this.cognitoUser) return '';
			return this.cognitoUser.attributes.email;
		},
		accountName() {
			let u = this.cognitoUser;
			if (!u) { return ''; }
			if (u.attributes.given_name && u.attributes.family_name) {
				return u.attributes.given_name + ' ' + u.attributes.family_name;
			} else {
				return u.username;
			}
		},
		selectValue() {
			if (!this.selectedVendor) return null;
			else return this.selectedVendor.id;
		}
	},
	methods: {
		async logout() {
			await Auth.signOut();
			window.location.href = process.env.URL_INNLOGIN + '/logout';
		}
	}
};
</script>
<style scoped>
#portalObjectSelect{
  min-width: 180px;
  cursor: pointer;
}
</style>
