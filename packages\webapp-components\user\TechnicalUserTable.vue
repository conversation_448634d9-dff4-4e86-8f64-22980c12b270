<template>
  <div>
    <b-row>
      <b-col><h2>Technische User</h2></b-col>
      <b-col class="d-flex">
        <b-button
          class="ml-auto"
          variant="primary"
          size="sm"
          @click="btnCreateTechnicalUser"
        >
          <FontAwesomeIcon icon="plus-square" />
          Technischen User erstellen
        </b-button>
      </b-col>
    </b-row>
    <b-input-group style="width: 300px">
      <template #prepend>
        <b-input-group-text>Suchen</b-input-group-text>
      </template>
      <b-form-input v-model="searchQuery" />
    </b-input-group>
    <b-table
      id="technicalUserTable"
      ref="technicalUserTable"
      hover
      small
      :items="filteredResult"
      :fields="tableFields"
      class="mt-3 table-clickable"
      head-variant="light"
      :per-page="paginationTechnicalUsers.perPage"
      :current-page="paginationTechnicalUsers.currentPage"
      @row-clicked="tableRowClick"
    >
      <template #cell(index)="data">
        {{ data.index + 1 }}
      </template>
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <LoadButton
            v-if="showEmployeeAddButton(data)"
            size="sm"
            class="mr-2"
            icon="user-plus"
            text="Zu Mitarbeiter hinzufügen"
            variant="primary"
            @click="btnCreateEventEmployee($event, data)"
          />
          <LoadButton
            size="sm"
            class="mr-2"
            icon="qrcode"
            text="Login"
            variant="primary"
            @click="btnLoginTechnicalUser($event, data)"
          />
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteTechnicalUser($event, data)"
          />
        </div>
      </template>
    </b-table>
    <b-pagination
      v-model="paginationTechnicalUsers.currentPage"
      :per-page="paginationTechnicalUsers.perPage"
      :total-rows="filteredResult.length"
      align="center"
    />
    <TechnicalUserModal
      ref="technicalUserModal"
      :referenceObjectId="referenceObjectId"
      :referenceObjectType="referenceObjectType"
      @technicalUserChanged="onTechnicalUserChange"
    />
    <TechnicalUserLoginModal ref="technicalUserLoginModal" />
  </div>
</template>
<script lang="ts">
import LoadButton from '../../organizerportal/src/vue/buttons/LoadButton.vue';
import DeleteButton from '../../organizerportal/src/vue/buttons/DeleteButton.vue';
import { defineComponent, ref, getCurrentInstance, onMounted, computed } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { technicalUserVendor as technicalUserVendorApi } from '@innevent/webapp-api';

import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import TableBusyLoader from '../TableBusyLoader.vue';
import TechnicalUserModal from './TechnicalUserModal.vue';
import TechnicalUserLoginModal from './TechnicalUserLoginModal.vue';


import type { PropType } from '@vue/composition-api';
import type { BvTableCellData, ResetButton, TechnicalInnUser, TechnicalUserReferenceObjectType } from '@innevent/webapp-types';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';

export default defineComponent({
	components: {
		LoadButton,
		DeleteButton,
		TechnicalUserModal,
		TableBusyLoader,
		TechnicalUserLoginModal
	},
	props: {
		referenceObjectId: {
			type: String,
			required: true
		},
		referenceObjectType: {
			type: String as PropType<TechnicalUserReferenceObjectType>,
			required: true
		}
	},
	emits: ['technicalUserChanged'],
	setup(props, { emit }) {
		const instance = getCurrentInstance();
		const searchQuery = ref<string>('');
		const technicalUsers = ref<TechnicalInnUser[]>([]);

		const technicalUserModal = ref<InstanceType<typeof TechnicalUserModal>>();
		const technicalUserLoginModal = ref<InstanceType<typeof TechnicalUserLoginModal>>();
		const technicalUserTable = ref<BTable>();
		const tableFields: BvTableFieldArray =  [
			{ key: 'index', label: 'Nr', sortable: true },
			{ key: 'email', label: 'E-Mail', sortable: true },
			{ key: 'firstName', label: 'Vorname', sortable: true },
			{ key: 'lastName', label: 'Nachname', sortable: true },
			{ key: 'buttons', label: '' }
		];

		onMounted(async ()  => {
			getTechnicalUsers();
		});

		async function getTechnicalUsers() {
			try {
				if (props.referenceObjectType == 'event') {
					technicalUsers.value = await api.getTechnicalUsers({
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId
					});
				} else {
					technicalUsers.value = await technicalUserVendorApi.getTechnicalUsers({
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId
					});
				}
			} catch (error: any) {
				notifyError({ instance, error });
			}
		}
		const paginationTechnicalUsers = ref({
			perPage: 3,
			currentPage: 1
		});

		const filteredResult = computed(() => {
			if (searchQuery.value == '') {
				return technicalUsers.value;
			}
			return technicalUsers.value.filter((employee) => {
				const isFirstname = employee.firstName.toLowerCase().includes(searchQuery.value.toLowerCase());
				const isLastname = employee.lastName.toLowerCase().includes(searchQuery.value.toLowerCase());
				//const isEmail = employee.firstName.toLowerCase().startsWith(searchQuery.value.toLowerCase());
				return isFirstname || isLastname;
			});
		});

		async function btnDeleteTechnicalUser(btn: ResetButton, cellData: BvTableCellData<TechnicalInnUser>) {
			try {
				if (props.referenceObjectType == 'event') {
					await api.deleteTechnicalUser({ 
						userSubject: cellData.item.userSubject,
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId
					});
				} else {
					await technicalUserVendorApi.deleteTechnicalUser({ 
						userSubject: cellData.item.userSubject,
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId
					});
				}
				
				onTechnicalUserChange();
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}
		
		async function btnCreateEventEmployee(btn: ResetButton, cellData: BvTableCellData<TechnicalInnUser>) {
			try {
				if (props.referenceObjectType == 'event') {
					await api.createEmployeeForTechnicalUser({
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId,
						userSubject: cellData.item.userSubject
					});
				} else {
					await technicalUserVendorApi.createEmployeeForTechnicalUser({
						referenceObjectType: props.referenceObjectType,
						referenceId: props.referenceObjectId,
						userSubject: cellData.item.userSubject,
						firstName: cellData.item.firstName,
						lastName: cellData.item.lastName
					});
				}
				
				onTechnicalUserChange();
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		function btnCreateTechnicalUser(): void {
			technicalUserModal.value?.openForCreate();
		}

		function tableRowClick(technicalUser: TechnicalInnUser): void {
			technicalUserModal.value?.openForEdit(technicalUser);
		}

		async function onTechnicalUserChange() {
			await getTechnicalUsers();
			emit('technicalUserChanged');
		}

		function showEmployeeAddButton(cellData: BvTableCellData<TechnicalInnUser>) {
			return props.referenceObjectType == 'event' ? !cellData.item.eventEmployees?.length : false;
		}

		async function btnLoginTechnicalUser(btn: ResetButton, cellData: BvTableCellData<TechnicalInnUser>) {
			technicalUserLoginModal.value?.open(cellData.item);
			btn.reset();
		}

		return {
			tableFields,
			technicalUserModal,
			technicalUserLoginModal,
			technicalUsers,
			btnDeleteTechnicalUser,
			btnCreateEventEmployee,
			btnCreateTechnicalUser,
			tableRowClick,
			onTechnicalUserChange,
			showEmployeeAddButton,
			btnLoginTechnicalUser,
			technicalUserTable,
			searchQuery,
			paginationTechnicalUsers,
			filteredResult
		};
	}
});
</script>

<style>
</style>
