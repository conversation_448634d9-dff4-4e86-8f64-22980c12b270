import type { PermissionInnEvent } from '../permissions';

export type Event = {
    eventId?: string;
    name: string;
    description?: string;
    timeFrom: Date;
    timeTo: Date;
    organizerId: string;
    settings: {
        innticket: EventSettingsInnTicket;
        innorder: EventSettingsInnOrder;
        innaccess: EventSettingsInnAccess;
    };
    counter: {
        accessAreaCode: number;
    };
}

export type EventSettingsInnAccess = {
    
}

export type EventSettingsInnTicket = {
    ticketCodePrefix: string;
}

export type EventSettingsInnOrder = {
    identifierDepositValue: number;
    identifierMinLengthUid: number;
    identifierMaxLengthUid: number;
    minimumTopUpValueTokenstation: number;
    minimumTopUpValueOnline: number;
    maximumTopUpValueTokenstation: number;
    maximumTopUpValueOnline: number;
    maximumTopDownValueTokenstation: number;
    maximumTopDownValueOnline: number;
    topDownRoundTokenstation: EventSettingsInnOrderRoundOptions;
    topDownRoundOnline: EventSettingsInnOrderRoundOptions;
    reActivateIdentifier: boolean;
    encryptionKey: string;
}

export type EventSettingsInnOrderRoundOptions = 
    'NO_ROUND'
    | 'ROUND_DOWN_FULL_EURO'
    | 'ROUND_DOWN_FULL_5EURO'
    | 'ROUND_DOWN_FULL_10EURO'

export type Employee = {
    firstName: string;
    lastName: string;
    position: string;
    isTechnicalUser: boolean;
    userSubject: string;
    permissions: {
        [ permission in PermissionInnEvent ]?: true;
    };
    cashboxAssignments: {
        [ cashboxId: string ]: true;
    };
    ecTerminalAssignments: {
        [ ecTerminalId: string ]: true;
    };
}

export type EventEmployee = Employee & {
    eventId: string;
}
export type VendorEmployee = Employee & {
    vendorId: string;
}

export type ShortEmployee = Pick<EventEmployee, 'firstName' | 'lastName' | 'position' | 'userSubject'>

export type Address = {
    salutation: string;
    organizationName: string;
    firstName: string;
    lastName: string;
    additionalInformation?: string;
    street: string;
    houseNumber: string;
    zipCode: string;
    city: string;
    country: Country;
}
export type Country = {
    iso3166Name: string;
    iso3166Alpha2Code: string;
}

export type Gender = 'male' | 'female' | 'other';
export type CustomerType = 'person' | 'organization' | 'system';

export type Customer = {
    address?: Address; // Address requirement is configurable
    birthdate?: string; // Only if customerType = person
    customerType: CustomerType;
    emailAddress?: string; // on specific order types
    firstName?: string;
    gender?: Gender; // Only if customerType = person
    lastName?: string;
    organizationName?: string; // Only if customerType = organization
    phoneNumber?: string;
    userSubject?: string; // if user has an account
};


export type AuthCache = {
    authCacheId: string;
    username: string;
    idToken: string;
    refreshToken: string;
    accessToken: string;
    expiryDate: number;
}
export type EmailChangeRequest = {
    emailChangeRequestId: string;
    newMail: string;
    userSubject: string;
    expiryDate: number;
}


export type ConnectionDevice = {
    eventId: string;
    deviceId: string;
    manufacturer: string;
    imei: string;
    model: string;
    lastLog?: ConnectionLog;
}
export type ConnectionLog = {
    eventId_deviceId: string;
    createdOn: string;
    userSubject: string;
    userFirstName: string;
    userLastName: string;
    connectivity: MobileConnectivity;
    androidSdk: string;
    androidVersion: string;
}

export type MobileConnectivity = 'WIFI' | 'CELLULAR_NETWORK' | '';

export type InvitationStatus = 'CREATED' | 'ACCEPTED' | 'DECLINED';
export type EventEmployeeInvitation = {
    eventId: string;
    eventName?: string;
    eventEmployeeInvitationId: string;
    email: string;
    status?: InvitationStatus;
    message?: string;
    userSubject?: string;
    createdOn?: string;
}