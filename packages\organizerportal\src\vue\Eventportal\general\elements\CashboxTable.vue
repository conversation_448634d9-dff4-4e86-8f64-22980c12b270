<template>
  <div>
    <b-row>
      <b-col><h2><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="Kasse erstellen"
          icon="plus-square"
          @click="btnCreateCashbox"
        />
      </b-col>
    </b-row>
    <b-table
      id="cashboxTableRef"
      ref="cashboxTableRef"
      hover
      :busy="isLoadingInitialRef"
      :items="cashboxesRef"
      :fields="tableFields"
      class="mt-3 table-clickable"
      striped
      :show-empty="true"
      empty-text="Keine Kassen vorhanden"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(currentCredit)="data">
        {{ `${data.value} €` }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteCashbox($event, data)"
          />
        </div>
      </template>
    </b-table>
    <CashboxSidebar 
      ref="cashboxSidebarRef"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useCashboxState } from '../../../../states';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import LoadButton from '../../../buttons/LoadButton.vue';
import DeleteButton from '../../../buttons/DeleteButton.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import type { Cashbox } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { currentEventRef } from '../../../../states/eventState';
import CashboxSidebar from './CashboxSidebar.vue';
export default defineComponent({
	components: {
		TableBusyLoader,
		LoadButton,
		DeleteButton,
		CashboxSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const { deleteCashbox, createCashbox, cashboxesRef, isLoadingInitialRef } = useCashboxState();
		const cashboxTableRef = ref<BTable>();
		const cashboxSidebarRef = ref<InstanceType<typeof CashboxSidebar>>();
		const tableFields: BvTableFieldArray =  [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'currentCredit', label: 'Aktueller Betrag', sortable: true },
			{ key: 'buttons', label: '' }
		];

		async function btnCreateCashbox(btn: ResetButton) {
			try {
				await createCashbox({
					key: {
						eventId: currentEventRef.value!.eventId!
					},
					data: {
						name: 'Neue Kasse'
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnDeleteCashbox(btn: ResetButton, cellData: BvTableCellData<Cashbox>) {
			try {
				const cashbox = cellData.item;
				await deleteCashbox({
					key: { cashboxId: cashbox.cashboxId,
						eventId: cashbox.eventId! }
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(cashbox: Cashbox) {
			const cashboxObj: Cashbox = JSON.parse(JSON.stringify(cashbox));
			cashboxSidebarRef.value!.openForEdit(cashboxObj);
		}


		return {
			tableFields,
			cashboxTableRef,
			isLoadingInitialRef, 
			cashboxesRef,
			btnCreateCashbox,
			btnDeleteCashbox,
			cashboxSidebarRef,
			tableRowClick
		};
	}
});
</script>

<style>
</style>
