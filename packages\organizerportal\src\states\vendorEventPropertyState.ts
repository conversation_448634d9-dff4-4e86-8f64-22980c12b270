import { vendorEventProperty as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { vendorEventPropertyState as log } from '../loglevel';
import type { VendorEventProperty } from '@innevent/webapp-types';

log.debug('INIT tokenstationState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const vendorEventPropertyRef = ref<VendorEventProperty[]>([]);

watch(vendorEventPropertyRef, () => {
	log.debug('WATCH vendorEventPropertyRef', vendorEventPropertyRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useVendorEventPropertyState() {
	log.debug('useVendorEventPropertyState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		vendorEventPropertyRef,
		loadVendorEventProperties,
		deleteVendorEventProperty
	};
}

// type LoadTokenstationsStateOption = {
//     key: Pick<Tokenstation, 'EventId'>;
// }
async function loadVendorEventProperties(eventId: string): Promise<void> {
	log.debug('loadVendorEventProperties()', eventId);
	isLoadingRef.value = true;
	try {
		vendorEventPropertyRef.value = await api.getVendorEventProperties({
			key: {
				EventId: eventId
			}
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type DeleteVendorEventPropertyStateOption = {
	key: Pick<VendorEventProperty, 'id'>;
}
async function deleteVendorEventProperty(options: DeleteVendorEventPropertyStateOption): Promise<void> {
	log.debug('deleteVendorEventProperty()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteVendorEventProperty(options);
		vendorEventPropertyRef.value = vendorEventPropertyRef.value.filter((vendorEventProperty) => vendorEventProperty.id !== options.key.id);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}