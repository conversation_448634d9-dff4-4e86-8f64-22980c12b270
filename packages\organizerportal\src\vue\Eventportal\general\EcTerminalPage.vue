<template>
  <div>
    <EcTerminalTable />
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { useEcTerminalState } from '../../../states';
import { currentEventRef } from '../../../states/eventState';
import EcTerminalTable from './elements/EcTerminalTable.vue';


export default defineComponent({
	components: { EcTerminalTable },
	setup() {
		const { loadEcTerminals } = useEcTerminalState();
		onMounted(async ()  => {
			await loadEcTerminals({
				key: {
					eventId: currentEventRef.value!.eventId!
				}
			}); 
		});

		return {};
	}
});
</script>