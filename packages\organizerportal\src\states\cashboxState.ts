import type { Cashbox, PartialPick } from '@innevent/types';
import { cashbox as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { cashboxState as log } from '../loglevel';
import Vue from 'vue';
import { useEmployeeState } from './employeeState';
import type { ReferenceObject } from '@innevent/webapp-types';
const { employeesOfEventRef } = useEmployeeState();


log.debug('INIT cashboxState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const cashboxesRef = ref<Cashbox[]>([]);

watch(cashboxesRef, () => {
	log.debug('WATCH cashboxesRef', cashboxesRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useCashboxState() {
	log.debug('useCashboxState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		cashboxesRef,
		loadCashboxes,
		createCashbox,
		updateCashbox,
		deleteCashbox
	};
}

type LoadCashboxesStateOption = {
	key: ReferenceObject;
}
async function loadCashboxes(options: LoadCashboxesStateOption): Promise<void> {
	log.debug('loadCashboxes()', options);
	isLoadingRef.value = true;
	try {
		cashboxesRef.value = await api.listCashboxes(options);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type CreateCashboxStateOption = {
	key: ReferenceObject;
	data: PartialPick<Cashbox, 'name' | 'description'>;
}
async function createCashbox(options: CreateCashboxStateOption): Promise<void> {
	log.debug('createCashbox()', options);
	isLoadingRef.value = true;
	try {
		const cashbox = await api.createCashbox(options);
		cashboxesRef.value.push(cashbox);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type UpdateCashboxStateOption = {
	key: Pick<Cashbox, 'cashboxId'> & ReferenceObject;
	data: PartialPick<Cashbox, 'name' | 'description'>;
}
async function updateCashbox(options: UpdateCashboxStateOption): Promise<void> {
	log.debug('updateCashbox()', options);
	isLoadingRef.value = true;
	try {
		const updatedCashbox = await api.updateCashbox(options);
		const index = cashboxesRef.value.findIndex((oldCashbox) =>oldCashbox.cashboxId == updatedCashbox.cashboxId);
		if (index == -1) {
			log.debug('updateCashbox()', 'Could not find Cashbox');
			return;
		}
		Vue.set(cashboxesRef.value, index, updatedCashbox);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type DeleteCashboxStateOption = {
	key: Pick<Cashbox, 'cashboxId'> & ReferenceObject;
}
async function deleteCashbox(options: DeleteCashboxStateOption): Promise<void> {
	log.debug('deleteCashbox()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteCashbox(options);
		cashboxesRef.value = cashboxesRef.value.filter((cashbox) => cashbox.cashboxId !== options.key.cashboxId);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}