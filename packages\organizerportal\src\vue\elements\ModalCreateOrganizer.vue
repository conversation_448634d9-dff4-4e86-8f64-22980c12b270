<template>
  <b-modal
    id="modalCreateOrganizer"
    ref="modalCreateOrganizerRef"
    title="Veranstalter erstellen"
    size="md"
  >
    <b-tabs
      v-model="activeTab"
      content-class="mt-3"
      justified
    >
      <b-tab
        title="Allgemein"
        active
      >
        <b-form @submit="(event) => event.preventDefault()">
          <b-form-group label="Geschäftsform der Organisation/Person">
            <b-form-select
              v-model="newOrganizerRef.organizationType"
              :options="organizationTypes"
              :state="isValidOrganizationType"
            />
          </b-form-group>
          <b-form-group
            label="Name der Organisation/Person"
          >
            <b-form-input
              v-model="newOrganizerRef.organizationName"
              placeholder="Gib den vollständigen Namen ein"
              :state="isValidOrganizationName"
              @keyup.enter="btnClickCreate"
            />
          </b-form-group>
        </b-form>
      </b-tab>
    </b-tabs>
    <template #modal-footer="{ cancel }">
      <b-button
        class="mr-auto"
        @click="cancel()"
      >
        Abbrechen
      </b-button>
      <LoadButton
        variant="primary"
        :disabled="!isValidGeneralTab"
        :loading="isLoadingRef"
        text="Veranstalter erstellen"
        @click="btnClickCreate"
      />
    </template>
  </b-modal>
</template>
<script lang="ts">

// Frameworks
import type { Organizer, PartialPick } from '@innevent/types';
import type { Ref } from '@vue/composition-api';
import { computed } from '@vue/composition-api';
import { getCurrentInstance } from '@vue/composition-api';
import { defineComponent, ref } from '@vue/composition-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useOrganizerState } from '../../states/organizerState';

// Vue Components
import LoadButton from '@innevent/webapp-components/button/LoadButtonV2.vue';
import type { BModal } from 'bootstrap-vue';

type NewOrganizer = Pick<Organizer, 'organizationName'> & PartialPick<Organizer, 'organizationType'>

export default defineComponent({
	components: { LoadButton },
	setup() {
		const instance = getCurrentInstance();
		const modalCreateOrganizerRef = ref<InstanceType<typeof BModal>>();
		const newOrganizerRef: Ref<NewOrganizer> = ref({
			organizationName: ''
		});
		const { createOrganizer, isLoadingRef } = useOrganizerState();

		const organizationTypes = [
			{ value: 'PERSON', text: 'Einzelperson' },
			{ value: 'GBR', text: 'GbR' },
			{ value: 'GMBH', text: 'GmbH' },
			{ value: 'UG', text: 'UG' },
			{ value: 'AG', text: 'AG' },
			{ value: 'CLUB', text: 'Verein' },
			{ value: 'OTHER', text: 'Andere' }
		];

		const isValidOrganizationType = computed(() => {
			const { organizationType } = newOrganizerRef.value;
			if (!organizationType) return null;
			else return true;
		});

		const isValidOrganizationName = computed(() => {
			const { organizationName } = newOrganizerRef.value;
			if (!organizationName) return null;
			if (organizationName.length < 5) return false;
			if (organizationName.length > 60) return false;
			else return true;
		});

		const isValidGeneralTab = computed(() => {
			return isValidOrganizationType.value && isValidOrganizationName.value;
		});

		async function btnClickCreate() {
			if (!isValidGeneralTab.value) return;
			try {
				await createOrganizer(newOrganizerRef.value);
				notifySuccess({ instance });
				modalCreateOrganizerRef.value?.hide();
			} catch (error) {
				notifyError({ instance, error });
			}
		}

		return {
			btnClickCreate,
			newOrganizerRef,
			isValidGeneralTab,
			isValidOrganizationType,
			isValidOrganizationName,
			organizationTypes,
			isLoadingRef,
			modalCreateOrganizerRef
		};
	}
});


// <b-tab
//   :disabled="!generalIsValid"
//   title="Adressdaten"
// >
//   <div class="form-row">
//     <div class="form-label-group col-9">
//       <b-form-input
//         v-model="organizer.street"
//         placeholder="Straße"
//         :state="validStreet"
//       />
//       <label>Straße</label>
//     </div>
//     <div class="form-label-group col-3">
//       <b-form-input
//         v-model="organizer.housenumber"
//         placeholder="Hausnummer"
//         :state="validHousenumber"
//       />
//       <label>Hausnummer</label>
//     </div>
//   </div>
//   <div class="form-row">
//     <div class="form-label-group col-3">
//       <b-form-input
//         v-model="organizer.postalcode"
//         placeholder="Postleitzahl"
//         :state="validPostalcode"
//       />
//       <label for="input4">Postleitzahl</label>
//     </div>
//     <div class="form-label-group col-9">
//       <b-form-input
//         v-model="organizer.city"
//         placeholder="Stadt"
//         :state="validCity"
//       />
//       <label for="input5">Stadt</label>
//     </div>
//   </div>
//   <div class="form-row">
//     <div class="form-label-group col-6">
//       <b-form-input
//         v-model="organizer.country"
//         placeholder="Land"
//         :state="validCountry"
//       />
//       <label for="input6">Land</label>
//     </div>
//     <div class="form-label-group col-6">
//       <b-form-input
//         v-model="organizer.state"
//         placeholder="Bundesland"
//         :state="validState"
//       />
//       <label for="input7">Bundesland</label>
//     </div>
//   </div>
// </b-tab>
// <b-tab
//   :disabled="!generalIsValid || !addressIsValid"
//   title="Module"
// >
//   <p>Bitte gewünschte Module auswählen</p>
// </b-tab>

// validStreet() {
// 	if (!this.organizer.street) return null;
// 	if (this.organizer.street.length < 2) return false;
// 	if (this.organizer.street.length > 20) return false;
// 	else return true;
// },
// validHousenumber() {
// 	if (!this.organizer.housenumber) return null;
// 	if (this.organizer.housenumber.length > 10) return false;
// 	else return true;
// },
// validPostalcode() {
// 	if (!this.organizer.postalcode) return null;
// 	if (this.organizer.postalcode.length < 2) return false;
// 	if (this.organizer.postalcode.length > 10) return false;
// 	if (!validator.isPostalCode(this.organizer.postalcode, 'any')) return false;
// 	else return true;
// },
// validCity() {
// 	if (!this.organizer.city) return null;
// 	if (this.organizer.city.length < 2) return false;
// 	if (this.organizer.city.length > 60) return false;
// 	else return true;
// },
// validCountry() {
// 	if (!this.organizer.country) return null;
// 	if (this.organizer.country.length < 2) return false;
// 	if (this.organizer.country.length > 60) return false;
// 	else return true;
// },
// validState() {
// 	if (!this.organizer.state) return null;
// 	if (this.organizer.state.length < 2) return false;
// 	if (this.organizer.state.length > 60) return false;
// 	else return true;
// },
// 		validFirstName() {
// 	if (!this.organizer.firstName) return null;
// 	if (this.organizer.firstName.length < 2) return false;
// 	if (this.organizer.firstName.length > 20) return false;
// 	else return true;
// },
// validLastName() {
// 	if (!this.organizer.lastName) return null;
// 	if (this.organizer.lastName.length < 2) return false;
// 	if (this.organizer.lastName.length > 20) return false;
// 	else return true;
// },
</script>
