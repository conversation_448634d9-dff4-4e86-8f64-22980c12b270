import type { TechnicalInnUser, TechnicalUserReferenceObjectType } from '@innevent/webapp-types';
import { apiInnEvent } from './instances';

export type TechnicalUserCreateOptions = Partial<Pick<TechnicalInnUser, 'firstName' | 'lastName'>> & {
    referenceId: string;
	referenceObjectType: TechnicalUserReferenceObjectType;
};
export async function createTechnicalUser(options: TechnicalUserCreateOptions): Promise<TechnicalInnUser> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	const response = await (await apiInnEvent()).post('/TechnicalUser', {
		firstName: options.firstName,
		lastName: options.lastName
	}, { params });
	return response.data;
}


export type TechnicalUserDeleteOptions = {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
};
export async function deleteTechnicalUser(options: TechnicalUserDeleteOptions): Promise<void> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	await (await apiInnEvent()).delete(`/TechnicalUser/${options.userSubject}`, { params });
}

export type EmployeeForTechnicalUserCreateOptions = {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
};
export async function createEmployeeForTechnicalUser(options: EmployeeForTechnicalUserCreateOptions): Promise<string> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	return await (await apiInnEvent()).post(`/TechnicalUser/${options.userSubject}/CreateEmployee`, {}, { params });
}


export type TechnicalUserUpdateOptions = Partial<Pick<TechnicalInnUser, 'firstName' | 'lastName'>> & {
	referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
 }
export async function updateTechnicalUser(options: TechnicalUserUpdateOptions): Promise<void> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	await (await apiInnEvent()).patch(`/TechnicalUser/${options.userSubject}`, {
		firstName: options.firstName,
		lastName: options.lastName
	}, { params });
}


export type TechnicalUserGetOptions =  {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
};
export async function getTechnicalUsers(options: TechnicalUserGetOptions): Promise<TechnicalInnUser[] | never> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	const response = await (await apiInnEvent()).get('/TechnicalUser', { params });
	return response.data;
}

function getReferenceId(referenceId: string, referenceObjectType: TechnicalUserReferenceObjectType): any {
	return referenceObjectType == 'event' ? { eventId: referenceId } : { vendorId: referenceId };

}
