<template>
  <main>
    <MainBaseData
      v-if="mainContent == 'mainBaseData' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
    />
    <MainArticles
      v-if="mainContent == 'mainArticles' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
    />
    <MainEvents
      v-if="mainContent == 'mainEvents' && currentPortalObjectId != null"
      :vendor-event-props="vendorEventProps"
      :selected-vendor="portalObjects"
      @navigate="navigate"
    />
    <MainStaff
      v-if="mainContent == 'mainStaff' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
      @navigate="navigate"
    />
    <EventArticlePage
      v-if="mainContent == 'subArticles' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
      :selected-event="selectedEvent"
      @navigate="navigate"
    />
    <SalesAreaPage
      v-if="mainContent == 'subSalesAreas' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
      :selected-event="selectedEvent"
      @navigate="navigate"
    />
    <OrderPage
      v-if="mainContent == 'subOrders' && currentPortalObjectId != null"
      :selected-vendor="portalObjects"
      :selected-event="selectedEvent"
      @navigate="navigate"
    />
    <div
      v-if="mainContent == 'subAnalysis'"
      class="mainContent"
    >
      Auswerungen
    </div>
  </main>
</template>
<script>
import MainBaseData from './main/MainBaseData.vue';
import MainArticles from './main/MainArticles.vue';
import MainEvents from './main/MainEvents.vue';
import MainStaff from './main/MainStaff.vue';
import EventArticlePage from './eventPages/EventArticlePage.vue';
import SalesAreaPage from './eventPages/SalesAreaPage.vue';
import OrderPage from './eventPages/OrderPage.vue';


export default {
	components: { MainBaseData, MainArticles, MainEvents, MainStaff, EventArticlePage, SalesAreaPage, OrderPage },
	props: ['currentPortalObjectId', 'currentNav', 'mainContent', 'portalObjects', 'vendorEventProps', 'selectedEvent'],
	methods:{
		navigate(site, ev = null) {
			this.$emit('navigate', site, ev);
		}
	}
};
</script>
<style>
.mainContent{
  margin: 20px 10px;
  margin-bottom: 50px;
  top: 66px;
  position: relative;
}
</style>
