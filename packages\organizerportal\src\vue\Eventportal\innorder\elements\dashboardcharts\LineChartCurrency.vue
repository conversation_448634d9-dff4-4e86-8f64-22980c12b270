<script>
import { Line } from 'vue-chartjs';

export default {
	extends: Line,
	props: {
		chartdata: {
			type: Object,
			default: null
		}, 
		reverse: {
			type: Boolean,
			default: false
		}
	},
	data: (props) => ({
		options: {
			responsive: true,
			maintainAspectRatio: false,
			scales: {
				xAxes: [{
					type: 'time',
					distribution: 'series'
				}],
				yAxes: [{
					ticks: {
						reverse: props.reverse,
						beginAtZero: true,
						callback: function(value, index, values) {
							return value.toLocaleString('de-DE', { style: 'currency', currency: 'EUR' });
						}
					}
				}]
			}
		}
	}),
	mounted() {
		this.renderChart(this.chartdata, this.options);
	}
};
</script>


