<template>
  <div>
    <b-row>
      <b-col><h2><PERSON><PERSON><PERSON><PERSON></h2></b-col>
    </b-row>
    <b-table
      id="tableEventEmployeesRef"
      ref="tableEventEmployeesRef"
      hover
      :items="employeesOfEventRef"
      :fields="tableFields"
      :busy="isLoadingInitialRef"
      :per-page="paginationEventEmployee.perPage"
      :current-page="paginationEventEmployee.currentPage"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            class=""
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteEventEmployee($event, data.item)"
          />
        </div>
      </template>
    </b-table>
    <b-pagination
      v-model="paginationEventEmployee.currentPage"
      :per-page="paginationEventEmployee.perPage"
      :total-rows="paginationEventEmployee.totalRows"
      align="center"
    />
    <EventEmployeeSidebar
      ref="eventEmployeeSidebar"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '../../../buttons/DeleteButton.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import { useEmployeeState, deleteEventEmployee } from '../../../../states/employeeState';
import type { BTable } from 'bootstrap-vue';
import type { EventEmployee } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import EventEmployeeSidebar from './EventEmployeeSidebar.vue';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { ResetButton } from '@innevent/webapp-types';


export default defineComponent({
	components: {
		DeleteButton,
		EventEmployeeSidebar,
		TableBusyLoader
	},
	setup() {
		const instance = getCurrentInstance();
		const { employeesOfEventRef, isLoadingInitialRef } = useEmployeeState();
		const tableEventEmployeesRef = ref<BTable>();
		const eventEmployeeSidebar = ref<InstanceType<typeof EventEmployeeSidebar>>();

		const paginationEventEmployee = ref({
			totalRows: employeesOfEventRef.value.length,
			perPage: 20,
			currentPage: 1
		});

		const tableFields = [
			{ key: 'firstName', label: 'Vorname', sortable: true },
			{ key: 'lastName', label: 'Nachname', sortable: true },
			{ key: 'position', label: 'Position', sortable: true },
			{ key: 'isTechnicalUser', label: 'Technischer User', sortable: true, formatter: value => value ? 'Ja' : '' },
			{ key: 'buttons', label: '' }
		];

		function tableRowClick(eventEmployee: EventEmployee): void {
			eventEmployeeSidebar.value?.openForEdit(eventEmployee);
		}

		async function btnDeleteEventEmployee(btn: ResetButton, dataItem: EventEmployee) {
			try {
				await deleteEventEmployee(dataItem.eventId, dataItem.userSubject);
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			tableEventEmployeesRef,
			employeesOfEventRef,
			tableFields,
			isLoadingInitialRef,
			btnDeleteEventEmployee,
			paginationEventEmployee,
			eventEmployeeSidebar,
			tableRowClick
		};
	}
});
</script>