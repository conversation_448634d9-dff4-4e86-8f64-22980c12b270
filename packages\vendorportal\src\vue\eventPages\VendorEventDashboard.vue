<template>
  <div>
    <h2>Event Dashboard</h2>
    <LoadingSpinner v-if="!vendorOrderData" />
    <div v-else>
      <h4 class="mt-4">
        Bestellungen
      </h4>
      <div class="mt-3">
        <b-card-group deck>
          <b-card
            bg-variant="light"
            header="Anzahl Bestellungen"
            class="text-center"
          >
            <b-card-text>{{ vendorOrderData.orders.getCountOrdersByVendor[0].count }}</b-card-text>
          </b-card>

          <b-card
            bg-variant="light"
            header="Netto Umsatz"
            class="text-center"
          >
            <b-card-text>{{ vendorOrderData.orders.getCountAndSaldoOrdersByVendor[0].saldoNet }} €</b-card-text>
          </b-card>
          <b-card
            bg-variant="light"
            header="Brutto Umsatz"
            class="text-center"
          >
            <b-card-text>{{ vendorOrderData.orders.getCountAndSaldoOrdersByVendor[0].saldoGross }} €</b-card-text>
          </b-card>
        </b-card-group>
      </div>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><LineChartCurrency :chartdata="chartSalesOrdersPerHour" /></b-col>
            <b-col><LineChart :chartdata="chartCountOrdersPerHour" /></b-col>
          </b-row>
        </b-container>
      </div>
   
      <h4 class="mt-4">
        Verkaufszonen
      </h4>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><HorizontalBarChart :chartdata="chartDataSalesAreaCountSum" /></b-col>
          </b-row>
        </b-container>
      </div>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><HorizontalBarChart :chartdata="chartDataTop10Barkeepers" /></b-col>
            <b-col><HorizontalBarChart :chartdata="chartDataTop10Articles" /></b-col>
          </b-row>
        </b-container>
      </div>
      <h4 class="mt-4">
        Pfand
      </h4>
      <div class="mt-3">
        <b-container>
          <b-row>
            <b-col><PieChart :chartdata="chartDataDepositvsReturned" /></b-col>
          </b-row>
        </b-container>
      </div>
    </div>
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadingSpinner from '../LoadingSpinner.vue';
import LineChart from './elements/charts/LineChart.vue';
import LineChartCurrency from './elements/charts/LineChartCurrency.vue';

import HorizontalBarChart from './elements/charts/HorizontalBarChart.vue';
import PieChart from './elements/charts/PieChart.vue';


export default {
	components: { LoadingSpinner, HorizontalBarChart, LineChart, PieChart, LineChartCurrency },
	props: ['selectedVendor', 'selectedEvent'],
	data() { return {
		vendorOrderData: null
	};},
	computed: {
		chartDataSalesAreaCountSum() {
			let labels = this.vendorOrderData.salesArea.countAndSumSalesOrdersBySalesArea.map((data) => data.name);
			let countData = this.vendorOrderData.salesArea.countAndSumSalesOrdersBySalesArea.map((data) => data.count);
			let saldoData = this.vendorOrderData.salesArea.countAndSumSalesOrdersBySalesArea.map((data) => data.saldo);

			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl Verkäufe',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					},
					{
						label: 'Umsatz Brutto',
						backgroundColor: 'rgba(55, 206, 86, 0.2)',
						data: saldoData
					}
				]
			};
		},
		chartDataTop10Barkeepers() {
			let labels = this.vendorOrderData.orders.top10Person.map((data) => data.name);
			let countData = this.vendorOrderData.orders.top10Person.map((data) => data.count);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Top 10 Verkäufer',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					}
				]
			};
		},
		chartDataTop10Articles() {
			let labels = this.vendorOrderData.orders.top10Articles.map((data) => data.name);
			let countData = this.vendorOrderData.orders.top10Articles.map((data) => data.count);
			let saldoData = this.vendorOrderData.orders.top10Articles.map((data) => data.saldo);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl',
						backgroundColor: 'rgba(255, 206, 86, 0.2)',
						data: countData
					},
					{
						label: 'Umsatz Brutto',
						backgroundColor: 'rgba(55, 206, 86, 0.2)',
						data: saldoData
					}
				]
			};
		},
		chartSalesOrdersPerHour() {
			let labels = this.vendorOrderData.orders.sumOrdersHourlyByVendor.map((data) => new Date(data.timestamp));
			let countData = this.vendorOrderData.orders.sumOrdersHourlyByVendor.map((data) => data.saldo);

			return {
				labels: labels,
				datasets: [
					{
						label: 'Brutto-Umsatz pro Stunde',
						backgroundColor: 'rgba(20, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartCountOrdersPerHour() {
			let labels = this.vendorOrderData.orders.countOrdersHourlyByVendor.map((data) => new Date(data.timestamp));
			let countData = this.vendorOrderData.orders.countOrdersHourlyByVendor.map((data) => data.count);
			return {
				labels: labels,
				datasets: [
					{
						label: 'Anzahl Bestellungen pro Stunde',
						backgroundColor: 'rgba(20, 99, 132, 0.2)',
						fill: true,
						data: countData
					}
				]
			};
		},
		chartDataDepositvsReturned() {
			const fieldMapDeposit = {
				'DEPOSIT': 'Pfand', 
				'DEPOSITRETURN': 'Pfandrückgabe'
			};
			let labels = this.vendorOrderData.deposit.countDepositvsReturned.map((data) => fieldMapDeposit[data.type]);
			let countData = this.vendorOrderData.deposit.countDepositvsReturned.map((data) => data.count);
			
			return {
				labels: labels,
				datasets: [
					{
						label: 'Umsatz pro Stunde',
						backgroundColor: [
							'rgba(255, 99, 132, 0.2)',
							'rgba(54, 162, 235, 0.2)' ],
						fill: true,
						data: countData
					}
				]
			};
		}
	},
	created() {
		this.getVendorOrderData();
	},
	methods: {
		getVendorOrderData() {
			this.vendorOrderData = null;
			API.get('innorder', '/Dashboard/VendorOrderData?EventId=' + this.selectedEvent.id + '&VendorId=' + this.selectedVendor.id)
				.then((response) => {
					this.vendorOrderData = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der Dashboard Daten ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		}
	}
};
</script>
