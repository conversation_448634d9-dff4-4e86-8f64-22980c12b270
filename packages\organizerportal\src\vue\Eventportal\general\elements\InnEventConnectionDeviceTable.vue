<template>
  <b-table
    id="connectionDeviceTableRef"
    ref="connectionDeviceTableRef"
    hover
    :busy="isLoadingRef"
    :items="connectionDevicesRef"
    :fields="tableFields"
    class="mt-3 table-clickable"
    striped
    :show-empty="true"
    empty-text="Keine Geräte im Einsatz"
    head-variant="light"
  >
    <template #table-busy>
      <div class="text-center text-primary my-2">
        <b-spinner class="align-middle" />
        <strong>Loading...</strong>
      </div>
    </template>
    <template #cell(androidVersion)="{item}">
      {{ (item.lastLog) ? item.lastLog.androidVersion: '' }}
    </template>
    <template #cell(loggedInUser)="{item}">
      {{ (item.lastLog && item.lastLog.userFirstName && item.lastLog.userLastName) ? 
        `${item.lastLog.userFirstName} ${item.lastLog.userLastName}` : '<PERSON><PERSON>' }}
    </template>
    <template #cell(timestamp)="{item}">
      {{ (item.lastLog) ? formatDate(item.lastLog.createdOn) : '' }}
    </template>
  </b-table>
</template>
<script lang="ts">
import { defineComponent, ref } from '@vue/composition-api';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useConnectionDeviceState } from '../../../../states/connectionDeviceState';
import { formatDate } from '@innevent/webapp-utils';


export default defineComponent({
	setup() {
		const { connectionDevicesRef, isLoadingRef } = useConnectionDeviceState();
		const connectionDeviceTableRef = ref<BTable>();
		const tableFields: BvTableFieldArray =  [
			{ key: 'deviceId', label: 'Geräte ID', sortable: true },
			{ key: 'manufacturer', label: 'Hersteller' },
			{ key: 'imei', label: 'IMEI' },
			{ key: 'model', label: 'Modell' },
			{ key: 'androidVersion', label: 'Android Version' },
			{ key: 'loggedInUser', label: 'Benutzer' },
			{ key: 'timestamp', label: 'Timestamp' }
		];

		return {
			tableFields,
			connectionDeviceTableRef,
			isLoadingRef, 
			connectionDevicesRef,
			formatDate
		};
	}
});
</script>

<style>
</style>
