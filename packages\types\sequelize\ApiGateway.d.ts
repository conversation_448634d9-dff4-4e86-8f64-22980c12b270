/* eslint-disable @typescript-eslint/naming-convention */
import type { APIGatewayProxyEvent as AWSAPIGatewayProxyEvent } from 'aws-lambda';

export type ApiGatewayHttpMethod = 'GET' | 'OPTIONS' | 'POST' | 'ANY' | 'DELETE' | 'HEAD' | 'PATCH' | 'PUT';

export type APIGatewayProxyEvent = AWSAPIGatewayProxyEvent & {
    httpMethod: ApiGatewayHttpMethod;
    requestContext: {
        authorizer: {
            claims: {
                sub: string;
            };
        };
    };
}
