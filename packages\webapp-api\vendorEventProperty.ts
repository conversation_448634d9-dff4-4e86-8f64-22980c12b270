import { apiInnOrder } from './instances';
import type { VendorEventProperty } from '@innevent/webapp-types';

type ModelType = VendorEventProperty;
type PrimaryKey = 'id';

export type VendorEventPropertyDeleteOptions = {
	key: Pick<ModelType, PrimaryKey>;
}
export async function deleteVendorEventProperty(options: VendorEventPropertyDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/VendorEventProperty/${options.key.id}`);
}

export type VendorEventPropertyGetOptions =  {
    key: Pick<ModelType, 'EventId'>;
};
export async function getVendorEventProperties(options: VendorEventPropertyGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnOrder()).get(`/VendorEventProperty?filterEvent=${options.key.EventId}`);
	return response.data;
}