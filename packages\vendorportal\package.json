{"name": "@innevent/vendorportal", "version": "1.0.0", "description": "", "scripts": {"dev": "webpack-dev-server", "build": "webpack --mode=production", "vendor": "webpack-dev-server", "lint": "eslint --ext .vue,.js,.ts src/", "lintfix": "eslint --ext .vue,.js,.ts src/ --fix"}, "private": true, "engines": {"npm": "please-use-yarn", "yarn": ">= 1.22.0"}, "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "7.10.0", "@babel/preset-env": "7.10.0", "@fortawesome/fontawesome-free": "^5.15.1", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-regular-svg-icons": "^5.15.1", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/vue-fontawesome": "^0.1.10", "@vue/compiler-sfc": "3.0.9", "@vue/composition-api": "^1.0.0-rc.6", "autoprefixer": "^9.8.6", "aws-amplify": "^4.2.10", "babel-loader": "8.1.0", "babel-plugin-transform-regenerator": "^6.26.0", "babel-polyfill": "6.26.0", "bootstrap": "^4.5.2", "bootstrap-vue": "^2.17.3", "css-loader": "3.6.0", "decimal.js": "^10.2.1", "dotenv-webpack": "^7.0.2", "file-loader": "^5.1.0", "git-revision-webpack-plugin": "^3.0.6", "html-webpack-plugin": "5.3.1", "http-status": "^1.4.1", "jquery": "^3.5.1", "moment": "^2.29.1", "object-filter": "^1.0.2", "popper.js": "^1.16.1", "portal-vue": "^2.1.7", "postcss-loader": "^3.0.0", "sass": "1.32.0", "sass-loader": "^8.0.2", "ts-loader": "^8.1.0", "typescript": "^4.2.3", "validator": "^13.1.17", "vue": "2.6.12", "vue-color": "^2.7.1", "vue-loader": "15.9.8", "vue-multiselect": "2.1.6", "vue-router": "3.5.1", "vue-style-loader": "^4.1.2", "vue-template-compiler": "2.6.12", "webpack": "^5.28.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "webpackbar": "^5.0.0-3"}, "dependencies": {"build-url": "^2.0.0", "precss": "^4.0.0", "query-string": "^6.13.5", "url-parse": "^1.4.7", "vue-chartjs": "^3.5.1", "loglevel": "^1.7.1", "loglevel-plugin-prefix": "^0.8.4"}}