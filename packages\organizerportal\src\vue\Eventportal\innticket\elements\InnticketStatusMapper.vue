<template>
  <div>
    <b-table
      striped
      hover
      :busy="busy"
      :items="valueMappingRef"
      :fields="tableFields"
    >
      <template #cell(innTicketIdentifier)="{item}">
        <Multiselect
          placeholder="Status auswählen"
          :show-labels="false"
          label="label"
          :options="availableStatus"
          style="min-width: 350px"
          :allowEmpty="true"
          :value="getSelectValue(item)"
          @input="opt => item.innTicketIdentifier = opt ? opt.value : undefined"
        />
      </template>
    </b-table>
    <LoadButton
      class="float-right mt-2"
      variant="primary"
      text="Statusmapping speichern"
      :loading="isLoadingRef"
      @click="saveMapping"
    />
  </div>
</template>
<script lang="ts">
/* eslint-disable @typescript-eslint/naming-convention */
import type { PropType } from '@vue/composition-api';
import { onMounted } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { defineComponent, getCurrentInstance } from '@vue/composition-api';
import Multiselect from 'vue-multiselect';
import type { TicketImport, ValueMappingItem } from '@innevent/types';
import { notifyError, formatDate, formatPrice, notifySuccess } from '@innevent/webapp-utils';
import { useTicketMapState } from '../../../../states/ticketMap';
import LoadButton from '@innevent/webapp-components/button/LoadButtonV2.vue';
import { innTicketImport as log } from '../../../../loglevel';
import * as api from '@innevent/webapp-api';

export default defineComponent({
	components: {
		Multiselect,
		LoadButton
	},
	props: {
		value: {
			type: Object as PropType<TicketImport>,
			required: true
		},
		busy: {
			type: Boolean,
			required: false
		}
	},
	emits: ['input'],
	setup(props, { emit }) {
		const instance = getCurrentInstance();
		const { ticketMapRef } = useTicketMapState();
		const isLoadingRef = ref(false);

		let ticketImportInput: TicketImport = JSON.parse(JSON.stringify(props.value));

		const tableFields = [{
			label: 'Ticket Status (Import)',
			key: 'importValue',
			sortable: true
		}, {
			label: 'Zuweisung InnTicket Status',
			key: 'innTicketIdentifier'
		}];

		const availableStatus = [{
			label: 'Gültig',
			value: 'generated'
		}, {
			label: 'Storniert',
			value: 'cancelled'
		}];

		const valueMappingRef = ref<ValueMappingItem[]>([]);

		function refreshStatusValueMapping(ticketImport?: TicketImport) {
			log.debug('ticketImport', ticketImport);
			if (ticketImport) {
				ticketImportInput = JSON.parse(JSON.stringify(ticketImport));
			}
			if (ticketImportInput.fieldMapping.length < 1 || Object.keys(ticketImportInput.columns).length < 1) {
				return;
			}

			log.info('run refreshStatusValueMapping()');
			if (!ticketImportInput.fieldMapping) {
				log.debug('no ticketImportInput.fieldMapping found');
				valueMappingRef.value = [];
				return;
			}
			// Search for status columns fieldMappings
			const fieldMappingsTicket = Object.values(ticketImportInput.fieldMapping).filter(fieldMappingItem => 
				fieldMappingItem.innTicketField === 'ticket.status');
			if (fieldMappingsTicket.length > 1) {
				log.debug('fieldMappingsTicket.length > 1');
				notifyError({ instance, message: 'status is mapped multiple!' });
				valueMappingRef.value = [];
				return;
			}
			else if (fieldMappingsTicket.length === 0) {
				log.debug('fieldMappingsTicket.length === 0');
				valueMappingRef.value = [];
				return;
			}

			valueMappingRef.value = ticketImportInput.valueMapping.status.map(statusMapping => {
				return {
					innTicketIdentifier: '',
					...statusMapping
				};
			});
		}

		onMounted(async () => {
			await refreshStatusValueMapping();
		});

		async function saveMapping() {
			try {
				isLoadingRef.value = true;
				const ticketImportResponse: TicketImport = props.value;
				await api.updateTicketImportValueMapping({
					key: {
						ticketImportId: ticketImportResponse.ticketImportId,
						eventId: ticketImportResponse.eventId,
						mappingAttribute: 'status'
					},
					data: valueMappingRef.value.filter(mapping => mapping.innTicketIdentifier)
				});
				ticketImportResponse.valueMapping.status = valueMappingRef.value;
				emit('input', ticketImportResponse);
				notifySuccess({ instance, title: 'Status-Mapping gespeichert' });
			} catch (error) {
				notifyError({ instance, message: 'Fehler beim Speichern' });
			}
			isLoadingRef.value = false;
		}

		function getSelectValue(item: ValueMappingItem) {
			return availableStatus.find(status => status.value === item.innTicketIdentifier);
		}

		return {
			tableFields,
			valueMappingRef,
			ticketMapRef,
			saveMapping,
			formatDate,
			formatPrice,
			refreshStatusValueMapping,
			isLoadingRef,
			availableStatus,
			getSelectValue
		};
	}
});
</script>