import type { ComponentInstance, Data } from '@vue/composition-api';
import type { VNode } from 'vue';

declare type Slot = (...args: any[]) => VNode[];
declare type InternalSlots = {
    [name: string]: Slot | undefined;
};

export type ComponentInternalInstance = {
    uid: number;
    parent: ComponentInternalInstance | null;
    root: ComponentInternalInstance;
    /**
     * Vnode representing this component in its parent's vdom tree
     */
    vnode: VNode;
    /**
     * Root vnode of this component's own vdom tree
     */
    /**
     * The reactive effect for rendering and patching the component. Callable.
     */
    // update: Function;
    data: Data;
    props: Data;
    attrs: Data;
    refs: Data;
    // emit: EmitFn;
    slots: InternalSlots;
    emitted: Record<string, boolean> | null;
    proxy: ComponentInstance;
    isMounted: boolean;
    isUnmounted: boolean;
    isDeactivated: boolean;
}