.sidebar-nav{
  min-height: calc(100vh - 66px);
  min-width: 300px;
}
.max-w100{
  max-width: 100px;
}
.max-w200{
  max-width: 200px;
}
.max-w300{
  max-width: 300px;
}
.max-w400{
  max-width: 400px;
}
.max-w500{
  max-width: 500px;
}
.max-w600{
  max-width: 600px;
}
.max-w700{
  max-width: 700px;
}
.max-w800{
  max-width: 800px;
}
.max-w900{
  max-width: 900px;
}
.max-w1000{
  max-width: 1000px;
}
.max-w1100{
  max-width: 1100px;
}
.max-w1200{
  max-width: 1200px;
}
.max-w1300{
  max-width: 1300px;
}
.max-w1400{
  max-width: 1400px;
}
.max-w1500{
  max-width: 1500px;
}
.max-w1600{
  max-width: 1600px;
}
.max-w1700{
  max-width: 1700px;
}
.max-w1800{
  max-width: 1800px;
}
.max-w1900{
  max-width: 1900px;
}
.max-w2000{
  max-width: 2000px;
}
.max-w2100{
  max-width: 2100px;
}
.max-w2200{
  max-width: 2200px;
}
.max-w2300{
  max-width: 2300px;
}
.max-w2400{
  max-width: 2400px;
}
.max-w2500{
  max-width: 2500px;
}
.max-w2600{
  max-width: 2600px;
}
.max-w2700{
  max-width: 2700px;
}

.form-label-group {
  position: relative;
  margin-bottom: 1rem;
}

.form-label-group > input,
.form-label-group > label {
  height: 3.125rem;
  padding: .75rem;
}

.form-label-group > label {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  margin-bottom: 0; /* Override default `<label>` margin */
  line-height: 1.5;
  color: #495057;
  pointer-events: none;
  cursor: text; /* Match the input under the label */
  border: 1px solid transparent;
  border-radius: .25rem;
  transition: all .1s ease-in-out;
}

.form-label-group input::-webkit-input-placeholder {
  color: transparent;
}

.form-label-group input:-ms-input-placeholder {
  color: transparent;
}

.form-label-group input::-ms-input-placeholder {
  color: transparent;
}

.form-label-group input::-moz-placeholder {
  color: transparent;
}

.form-label-group input::placeholder {
  color: transparent;
}

.form-label-group input:not(:placeholder-shown) {
  padding-top: 1.25rem;
  padding-bottom: .25rem;
}

.form-label-group input:not(:placeholder-shown) ~ label {
  padding-top: .25rem;
  padding-bottom: .25rem;
  font-size: 12px;
  color: #777;
}

/* Bugfix in Grid System
-------------------------------------------------- */

.form-row > .form-label-group input:placeholder-shown ~ label {
  margin-left: 5px;
}

/* Fallback for Edge
-------------------------------------------------- */
@supports (-ms-ime-align: auto) {
  .form-label-group > label {
    display: none;
  }
  .form-label-group input::-ms-input-placeholder {
    color: #777;
  }
}

/* Fallback for IE
-------------------------------------------------- */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .form-label-group > label {
    display: none;
  }
  .form-label-group input:-ms-input-placeholder {
    color: #777;
  }
}

div.dropdown[squared] > .btn{
  border-radius: 0!important;
}

div.custom-switch > label.custom-control-label{
  cursor: pointer;
}

table.table-clickable > tbody > tr{
  cursor: pointer;
}
