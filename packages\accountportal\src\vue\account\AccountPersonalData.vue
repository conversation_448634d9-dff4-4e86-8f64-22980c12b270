<template>
  <div>
    <b-card class="mb-4">
        <ProfileSettings v-if="cognitoUser" :cognitoUser="cognitoUser"></ProfileSettings>
    </b-card>
    <b-card class="mb-4">
        <AddressSettings v-if="cognitoUser" :cognitoUser="cognitoUser"></AddressSettings>
    </b-card>
    <b-card class="mb-4">
        <EmailSettings v-if="cognitoUser" :cognitoUser="cognitoUser"></EmailSettings>
    </b-card>
  </div>
</template>
<script>

import ProfileSettings from './ProfileSettings.vue'
import EmailSettings from './EmailSettings.vue'
import AddressSettings from './AddressSettings.vue'

export default {
  props: ['cognitoUser'],
  data() { return {
    cardTitle: 'Persönliche Daten'
  }},
  components: { ProfileSettings, EmailSettings, AddressSettings },
  computed:{

  },
}
</script>
<style scoped>

</style>
