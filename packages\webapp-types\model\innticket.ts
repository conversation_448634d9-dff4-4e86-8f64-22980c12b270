import type { EventEmployee, TicketOrder, RedemptionGroup, InnEventQueryResult } from '@innevent/types';
import type { Ref } from '@vue/composition-api';

export type SalesPeriod = {
    eventId: string;
    description: string;
    salesPeriodId: string;
    salesPeriodName: string;
    timeFrom: Date | string;
    timeTo: Date | string;
}

export type TicketGroup = {
    eventId: string;
    ticketGroupId: string;
    ticketGroupName: string;
}

export type Ticket = {
    eventId: string;
    ticketId: string;
    ticketName: string;
    description: string;
    personalizationRequired: boolean;
    ticketGroups: {
        [ticketGroupId: string]: true; // TODo SET & GET TYPES
    };
    ticketSaleProperties: {
        [salesPeriodId: string]: TicketSaleProperty;
    };
    ticketCodePrefix: string;
}

export type TicketSaleProperty = {
    salesPeriodId: string;
    contingent: number | null;
    price: Pick<VatPrice, 'priceGross' | 'vatPercent'>;
}

export type VatPrice = {
    vatValue: number;
    vatPercent: number;
    priceNet: number;
    priceGross: number;
}

export type KeyObject = {
    [key: string]: true;
}

export type TicketMap = {
    ticketGroups: TicketGroup[];
    tickets: Ticket[];
    salesPeriods: SalesPeriod[];
}

export type RedemptionGroupMap = {
    redemptionGroups: RedemptionGroup[];
}

export type TicketOrderMap = {
    ticketOrders: InnEventQueryResult<TicketOrder>;
    eventEmployees: EventEmployee[];
    tickets: Ticket[];
    salesPeriods: SalesPeriod[];
}
export type PermissionRedemptionGroup = 'perm:innticket.redemptiongroup.use' | 'perm:innticket.redemptiongroup.admin';
export type InnEventState<ModelType> = {
    mapRef: Ref<ModelType>;
    isLoadingRef: Ref<boolean>;
}
