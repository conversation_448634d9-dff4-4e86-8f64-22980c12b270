import type { Event, Optional } from '@innevent/types';
import type { ComputedRef } from '@vue/composition-api';
import { watch } from '@vue/composition-api';
import { computed, ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { router } from '../../router';
import { loadEventEmployee, loadEventEmployeesOfEvent } from '.';
import { eventState as log } from '../loglevel';
import type { CustomCognitoUser } from './userState';
import { useUserState } from './userState';

log.debug('INIT eventState');

const { currentUserRef } = useUserState();

const isLoadingRef = ref<boolean>(false);
const permittedEventsRef = ref<Event[]>([]);
const currentEventIdRef = ref<string | undefined>(undefined);

watch([currentEventIdRef, currentUserRef], ([eventId, cognitoUser]) => {
	log.debug('WATCH currentEventIdRef, currentUserRef', eventId, cognitoUser);
	if (eventId && cognitoUser) {
		loadEventEmployee(eventId as string, (cognitoUser as CustomCognitoUser).attributes.sub);
		loadEventEmployeesOfEvent(eventId as string);
	}
});

export const currentEventRef: ComputedRef<Event | undefined> = computed(() => {
	log.debug('COMPUTED currentEventRef START');
	const currentEvent = permittedEventsRef.value.find(event => event.eventId === currentEventIdRef.value);
	log.debug('COMPUTED currentEventRef END', currentEvent);
	return currentEvent;
});

async function createEvent(newEvent: Optional<Event, 'eventId'>): Promise<Event> {
	log.debug('createEvent()', newEvent);
	isLoadingRef.value = true;
	try {
		const createdEvent = await api.createEvent({
			data: newEvent
		});
		isLoadingRef.value = false;
		permittedEventsRef.value.push(createdEvent);
		router.push({
			name: 'eDashboard',
			params: {
				event: createdEvent.eventId
			}
		});
		return createdEvent;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function deleteEvent(eventId: string): Promise<void> {
	log.debug('deleteEvent()', eventId);
	isLoadingRef.value = true;
	try {
		await api.deleteEvent({
			key: { eventId }
		});
		permittedEventsRef.value.filter(event => event.eventId !== eventId);
		if (currentEventRef.value?.eventId === eventId) {
			router.push({
				name: 'portalHome'
			});
		}
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function updateEvent(updateAttributes: Partial<Event>): Promise<void> {
	log.debug('updateEvent()', updateAttributes);
	isLoadingRef.value = true;
	try {
		const updatedEvent = await api.updateEvent({
			key: { eventId: currentEventRef.value!.eventId },
			data: {
				name: updateAttributes.name,
				description: updateAttributes.description,
				timeFrom: updateAttributes.timeFrom,
				timeTo: updateAttributes.timeTo,
				settings: updateAttributes.settings
			}
		});
		permittedEventsRef.value = permittedEventsRef.value.map(event => {
			return event.eventId === updateAttributes.eventId ? updatedEvent : event;
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

async function reloadPermittedEvents() {
	log.debug('reloadPermittedEvents()');
	isLoadingRef.value = true;
	try {
		permittedEventsRef.value = await api.getPermittetEvents();
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useEventState() {
	log.debug('useEventState()');

	return {
		updateEvent,
		deleteEvent,
		createEvent,
		isLoadingRef,
		reloadPermittedEvents,
		permittedEventsRef,
		currentEventRef,
		currentEventIdRef
	};
}