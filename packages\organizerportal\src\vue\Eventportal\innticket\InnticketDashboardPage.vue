<template>
  <div>
    <h2>Headline</h2>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import LoadButton from '../../buttons/LoadButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
const uuid = require('uuid/v4');

export default {
	components: { FontAwesomeIcon, LoadButton, DeleteButton },
	props: ['selectedEvent'],
	data() { return {

	};},
	computed:{

	},
	created() {

	},
	methods:{

	}
};
</script>
<style scoped>

</style>
