import { apiInnEvent } from './instances';
import type { EventEmployee, InnEventQueryOptions, InnEventQueryResult } from '@innevent/types';

type ModelType = EventEmployee;
type PrimaryKey = 'userSubject';

export type GetEventEmployeeOptions =  {
    key: Pick<ModelType, 'eventId' | 'userSubject'>;
};
export async function getEventEmployee(options: GetEventEmployeeOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).get(`/EventEmployee/${options.key.userSubject}`, { 
		params: { 
			eventId: options.key.eventId 
		} 
	});
	return response.data;
}

export type GetEventEmployeesOptions =  {
    key: Pick<ModelType, 'eventId'>;
	queryParameter?: InnEventQueryOptions<EventEmployee>;
};
export async function getEventEmployees(options: GetEventEmployeesOptions): Promise<InnEventQueryResult<EventEmployee>> {
	const response = await (await apiInnEvent()).get('/EventEmployee', { 
		params: {
			eventId: options.key.eventId,
			...options.queryParameter
		} 
	});
	return response.data;
}

export type EventEmployeeDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteEventEmployee(options: EventEmployeeDeleteOptions): Promise<void> {
	await (await apiInnEvent()).delete(`/EventEmployee/${options.key.userSubject}`, { 
		params: { 
			eventId: options.key.eventId
		} 
	});
}

export type EventEmployeeUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'firstName' | 'lastName' | 'position'>>;
};

export async function updateEventEmployee(options: EventEmployeeUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/EventEmployee/${options.key.userSubject}`, options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW' 
		} 
	});
	return response.data;
}

export type EventEmployeePermissionOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'permissions'>>;
};

export async function addPermissionsEventEmployee(options: EventEmployeePermissionOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).post(`/EventEmployee/${options.key.userSubject}/Permission`, options.data.permissions, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW' 
		} 
	}
	);
	return response.data;
}

export async function deletePermissionsEventEmployee(options: EventEmployeePermissionOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).delete(`/EventEmployee/${options.key.userSubject}/Permission`, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW' 
		},
		data: options.data.permissions
	});
	return response.data;
}
