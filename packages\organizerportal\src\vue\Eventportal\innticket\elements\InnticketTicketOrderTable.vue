<template>
  <div>
    <b-row>
      <b-col>
        <h2>Bestellungen</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto"
            variant="primary"
            size="sm"
            @click="btnCreateTicketOrder()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Neue Bestellungen
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      id="ticketOrderTable"
      ref="ticketOrderTable"
      hover
      :items="tableData"
      :fields="tableFields"
      :busy="isLoadingRef"
      table-class="mt-3 table-clickable"
      head-variant="light"
      sticky-header="600px"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(orderType)="{value}">
        {{ orderType[value] }}
      </template>
      <template #cell(totalPrice)="{value}">
        {{ `${value.priceGross} €` }}
      </template>
      <template #cell(status)="{value}">
        {{ value == 'completed' ? 'Abgeschlossen' : 'Storniert' }}
      </template>
      <template #cell(orderDate)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(customer)="{value}">
        {{ getCustomerName(value) }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <LoadButton
            v-if="data.item.status == 'completed'"
            size="sm"
            icon="ban"
            text="Stornieren"
            variant="danger"
            @click="btnCancelTicketOrder($event, data)"
          />
        </div>
      </template>
    </b-table>
    <b-pagination
      v-model="pagination.currentPage"
      :total-rows="pagination.totalRows"
      :per-page="pagination.perPage"
      align="center"
      @input="click"
    />
    <TicketOrderSidebar 
      ref="ticketOrderSidebar"
    />
  </div>
</template>
<script lang="ts">
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import TicketOrderSidebar from './InnticketTicketOrderSidebar.vue';
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import { formatDate } from '@innevent/webapp-utils';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useTicketOrderMap } from '../../../../states/ticketOrder';
import type { Customer, TicketOrder } from '@innevent/types';
import { currentEventRef } from '../../../../states/eventState';
import { innTicketImport as log } from '../../../../loglevel';


export default defineComponent({
	components: {
		LoadButton,
		TableBusyLoader,
		TicketOrderSidebar
	},
	emits: ['ticketOrderChanged'],
	setup(props, { emit }) {
		const instance = getCurrentInstance();
		const ticketOrderTable = ref<BTable>();
		const ticketOrderSidebar = ref<InstanceType<typeof TicketOrderSidebar>>();
		const { ticketOrderMapRef: ticketOrderMap, isLoadingRef } = useTicketOrderMap();

		let pagination = ref({
			totalRows: 26,
			perPage: 25,
			currentPage: 1
		});
		const orderType = { 
			import: 'Import', 
			manual: 'Manuelle Bestellung', 
			boxOffice: 'Vorverkaufsstelle', 
			onlineShop: 'Online Shop', 
			offlineShop: 'Offline Shop', 
			promoter: 'Promoter' 
		};

		function click(page: number) {
			paginate();
		}

		async function paginate() {
			if (ticketOrderMap.value && 
				ticketOrderMap.value.ticketOrders.items.length < pagination.value.perPage * pagination.value.currentPage &&
				ticketOrderMap.value.ticketOrders.items.length % pagination.value.perPage === 0) {
				
				isLoadingRef.value = true;
				//GET additional TicketOrders
				const ticketOrders = await api.getTicketOrders({
					key: { eventId: currentEventRef.value!.eventId! },
					queryParameter: {
						itemLimit: 25,
						startKey: ticketOrderMap.value?.ticketOrders.lastEvaluatedKey,
						partitionKeyValue: currentEventRef.value!.eventId!
					}
				});
				isLoadingRef.value = false;

				ticketOrderMap.value.ticketOrders.items.push(...ticketOrders.items);
				ticketOrderMap.value.ticketOrders.lastEvaluatedKey = ticketOrders.lastEvaluatedKey;

				pagination.value.totalRows = ticketOrderMap.value.ticketOrders.items.length + 1;
			}
		}

		function getCustomerName(customer: Customer) {
			if (customer.customerType == 'person' && customer.firstName && customer.lastName)
				return `${customer.firstName} ${customer.lastName}`;
			else if ((customer.customerType == 'organization' && customer.organizationName))
				return customer.organizationName;
			else
				return 'Keine Daten angegeben';
		}

		const tableFields: BvTableFieldArray =  [
			{ key: 'orderNumber', label: 'Bestellnummer', sortable: true },
			{ key: 'orderType', label: 'Kanal' },
			{ key: 'orderDate', label: 'Datum' },
			{ key: 'totalPrice', label: 'Gesamtpreis' },
			{ key: 'status', label: 'Status' },
			{ key: 'customer', label: 'Kunde' },
			{ key: 'buttons', label: '' }
		];

		const tableData = computed(() => {
			const startIndex = (pagination.value.perPage * pagination.value.currentPage) - pagination.value.perPage;
			log.debug('startIndex', startIndex);
			let endIndex = ticketOrderMap.value?.ticketOrders.items.findIndex((ticketOrder) => 
				ticketOrder.ticketOrderId == ticketOrderMap.value?.ticketOrders.lastEvaluatedKey) ?? 0;
			log.debug('endIndex', endIndex);
			if (endIndex < pagination.value.perPage) endIndex = pagination.value.perPage;

			return ticketOrderMap.value?.ticketOrders.items.slice(
				startIndex,
				endIndex === -1 ? ticketOrderMap.value.ticketOrders.items.length : endIndex
			) ?? [];
		});

		async function btnCancelTicketOrder(btn: ResetButton, cellData: BvTableCellData<TicketOrder>) {
			try {
				const ticketOrder = await api.updateTicketOrder({
					key: {
						eventId: cellData.item.eventId,
						ticketOrderId: cellData.item.ticketOrderId
					},
					data: {
						status: 'cancelled'
					}
				});
				const index = ticketOrderMap.value?.ticketOrders.items?.findIndex(element => 
					element.ticketOrderId == cellData.item.ticketOrderId
				) as number;
				ticketOrderMap.value?.ticketOrders?.items.splice(index, 1, ticketOrder);
				onTicketOrderChange();
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(ticketOrder: TicketOrder): void {
			const ticketOrderObj: TicketOrder = JSON.parse(JSON.stringify(ticketOrder));
			ticketOrderSidebar.value?.openForView(ticketOrderObj);
		}

		function onTicketOrderChange() {
			ticketOrderTable.value?.refresh();
			emit('ticketOrderChanged');
		}

		function btnCreateTicketOrder(): void {
			ticketOrderSidebar.value?.openForCreate();
		}

		return {
			tableFields,
			ticketOrderSidebar,
			ticketOrderTable,
			ticketOrderMap,
			isLoadingRef,
			btnCancelTicketOrder,
			onTicketOrderChange,
			tableRowClick,
			btnCreateTicketOrder,
			formatDate,
			click,
			tableData,
			getCustomerName,
			orderType,
			pagination
		};
	}
});
</script>

<style>
</style>
