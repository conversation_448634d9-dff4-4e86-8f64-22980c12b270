<template>
  <div>
    <h3>Adresse</h3>
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
    <form class="mt-3 max-w600" v-else>
      <div class="form-row">
        <div class="form-label-group col-10">
          <input v-model="userData['custom:street']" type="text" id="inputStreet" placeholder="Straße" class="form-control">
          <label for="inputStreet">Straße</label>
        </div>
        <div class="form-label-group col-2">
          <input v-model="userData['custom:housenumber']" type="text" id="inputNumber" placeholder="Hausnummer" class="form-control">
          <label for="inputNumber">Hausnummer</label>
        </div>
      </div>
      <div class="form-row">
        <div class="form-label-group col-3">
          <input v-model="userData['custom:postalcode']" type="text" id="inputPzl" placeholder="Postleitzahl" class="form-control">
          <label for="inputPzl">Postleitzahl</label>
        </div>
        <div class="form-label-group col-9">
          <input v-model="userData['custom:city']" type="text" id="inputCity" placeholder="Stadt" class="form-control">
          <label for="inputCity">Stadt</label>
        </div>
      </div>
      <div class="form-row">
        <div class="form-label-group col-6">
          <input v-model="userData['custom:country']" type="text" id="inputCountry" placeholder="Land" class="form-control">
          <label for="inputCountry">Land</label>
        </div>
        <div class="form-label-group col-6">
          <input v-model="userData['custom:state']" type="text" id="inputState" placeholder="Bundesland" class="form-control">
          <label for="inputState">Bundesland</label>
        </div>
      </div>
      <SingleSaveButton @save="save"></SingleSaveButton>
    </form>
  </div>
</template>
<script>
import { Auth } from 'aws-amplify'
import LoadingSpinner from '../tools/LoadingSpinner.vue'
import SingleSaveButton from '../buttons/SingleSaveButton.vue'
var validator = require('validator')

export default {
  props: ['selectedVendor', 'events', 'selectedEvent'],
  created(){
    this.getUserData()
  },
  data(){ return{
    isLoading: true,
    userData:{},
    genderSelection:[
      { value: 'null', text: "Nicht ausgewählt" },
      { value: 'male', text: "Männlich" },
      { value: 'female', text: "Weiblich" },
      { value: 'divers', text: "Divers" }
    ],
    titelSelection:[
      { value: 'null', text: "Kein Titel" },
      { value: 'Dr.', text: "Dr." },
      { value: 'Prof.', text: "Prof." }
    ]
  }},
  components: { LoadingSpinner, SingleSaveButton},
  computed:{

  },
  methods:{
    async getUserData(){
      this.userData = (await Auth.currentUserInfo()).attributes
      this.isLoading = false
    },
    async save(btn){
      //Validate PostalCode
      if(this.userData['custom:postalcode'] && !validator.isPostalCode(this.userData['custom:postalcode'], 'DE')){
        this.$bvToast.toast('Bitte gebe deine richtige Postleitzahl ein', {
          title: 'Postleitzahl fehlerhaft',
          autoHideDelay: 3000,
          variant: 'danger'
        })
        btn.setError(btn, "Eingabe fehlerhaft")
        return
      }

      let user = await Auth.currentAuthenticatedUser();

      Auth.updateUserAttributes(user, this.userData).then((data) => {
        btn.setSuccess(btn)
      }).catch(e => {
          btn.setError(btn, "Eingabe fehlerhaft")
      });
    }
  }
}
</script>
<style scoped>

</style>
