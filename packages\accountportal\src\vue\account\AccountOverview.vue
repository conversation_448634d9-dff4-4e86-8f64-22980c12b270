<template>
  <div>
    <h2 class="text-center mt-4"><PERSON><PERSON><PERSON><PERSON>, {{ displayName }}</h2>
    <p>Hier kannst du deine Daten sowie die Datenschutz- Benachrichtugungs- und Sicherheitseinstellungen verwalten, um Inn//Systems optimal an deine Bedürfnisse anzupassen</p>
  </div>
</template>
<script>

export default {
  props: ['cognitoUser'],
  data() { return {
    cardTitle: 'Account <PERSON>icht'
  }},
  components: { },
  computed:{
    displayName(){
      if(!this.cognitoUser) return ''
      else if(!this.cognitoUser.attributes.given_name) return this.cognitoUser.attributes.email
      else return this.cognitoUser.attributes.given_name + ' ' + this.cognitoUser.attributes.family_name
    }
  },
}
</script>
<style scoped>

</style>
