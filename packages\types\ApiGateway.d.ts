/* eslint-disable @typescript-eslint/naming-convention */
import type { APIGatewayProxyEvent } from 'aws-lambda';
import type { SortDirection } from './common';
export type { APIGatewayProxyResult } from 'aws-lambda';

export type ApiGatewayHttpMethod = 'GET' | 'OPTIONS' | 'POST' | 'ANY' | 'DELETE' | 'HEAD' | 'PATCH' | 'PUT';

export type InnEventAPIGatewayProxyEvent = APIGatewayProxyEvent & {
    httpMethod: ApiGatewayHttpMethod;
    queryStringParameters: {
        itemLimit?: string;
        startKey?: string;
        sortField?: string;
        sortDirection?: SortDirection;
        eventId?: string;
        vendorId?: string;
        organizerId?: string;
        returnValue?: 'NONE' | 'ALL_NEW';
    };
    requestContext: {
        authorizer: {
            claims: {
                sub: string;
                given_name: string;
                family_name: string;
                email: string;
            };
        };
    };
    pathParameters: {
        accessAreaId: string;
        authCacheId: string;
        emailChangeRequestId: string;
        userSubject: string;
        ticketGroupId: string;
        ticketId: string;
        salesPeriodId: string;
        ticketImportId: string;
        ticketOrderId: string;
        generatedTicketId: string;
        redemptionGroupId: string;
        generatedTicketCode: string;
        ecTerminalId: string;
        cashboxId: string;
        connectionDeviceId: string;
        mappingAttribute: string;
        isoDate: string;
    };
}