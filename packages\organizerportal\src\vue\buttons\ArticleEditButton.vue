<template>
  <div>
    <b-button-group
      v-if="state == 'initial'"
      size="sm"
    >
      <b-button
        variant="warning"
        @click="clickEdit"
      >
        <FontAwesomeIcon icon="edit" /> Edit
      </b-button>
      <b-button
        variant="danger"
        @click="clickDelete"
      >
        <FontAwesomeIcon icon="trash" /> Delete
      </b-button>
    </b-button-group>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default {
	components: { FontAwesomeIcon },
	props: ['cellData'],
	data() { return {
		state: 'initial'
	};},
	methods: {
		clickEdit() {
			this.cellData.item.editMode = true;
			// console.log('ok' + this.cellData.item)
		},
		clickDelete() {
			alert('del');
			// this.cellData.item.editMode = true
			// console.log('ok' + this.cellData.item)
		}
	}
};
</script>
<style scoped>

</style>
