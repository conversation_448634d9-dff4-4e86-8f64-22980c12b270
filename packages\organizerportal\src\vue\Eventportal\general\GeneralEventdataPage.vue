<template>
  <div>
    <h2>Eventdaten</h2>
    <b-form
      class="w-25"
    >
      <b-form-group
        label="Event Name"
        label-for="input1"
      >
        <b-form-input
          id="input1"
          v-model="eventPropertiesRef.name"
          placeholder="Event Name"
        />
      </b-form-group>
      <b-form-group
        label="Event Description"
        label-for="input2"
      >
        <b-form-input
          id="input2"
          v-model="eventPropertiesRef.description"
          placeholder="Event Description"
        />
      </b-form-group>
      <LoadButton
        :loading="isLoadingRef"
        @click="updateEvent(eventPropertiesRef)"
      />
    </b-form>
    <b-alert
      show
      variant="danger"
      class="mt-3"
    >
      <h4 class="alert-heading">
        Event löschen
      </h4>
      <p>
        Mit dem Löschen des Events werden alle produktiven Daten aller Inn//Event Module gelöscht.
        Es stehen keine Auswertungen und Transaktionen mehr zur Verfügung.
        Bitte bestätige den Löschvorgang, indem du "delete" in das Feld eingibst.
      </p>
      <b-form-input
        v-model="deleteConfirmTextRef"
        :state="deleteConfirmed"
        style="max-width: 400px"
      />
      <hr>
      <LoadButton
        icon="trash"
        :loading="isLoadingRef"
        :disabled="!deleteConfirmed"
        variant="danger"
        text="Event entgültig löschen"
        @click="btnDeleteEvent"
      />
    </b-alert>
  </div>
</template>
<script lang="ts">
import LoadButton from '@innevent/webapp-components/button/LoadButtonV2.vue';
import { useEventState } from '../../../states';
import type { Event } from '@innevent/types';
import type { Ref } from '@vue/composition-api';
import { computed, defineComponent, ref, watch } from '@vue/composition-api';
const { currentEventRef, updateEvent, deleteEvent, isLoadingRef } = useEventState();
import { eventPortalGeneral as log } from '../../../loglevel';

export default defineComponent({
	components: { LoadButton },
	setup() {
		const deleteConfirmTextRef = ref<string>('');
		const eventPropertiesRef: Ref<Event> = ref(currentEventRef.value as Event);

		const deleteConfirmed = computed(() => {
			return deleteConfirmTextRef.value === 'delete';
		});

		watch(currentEventRef, () => {
			log.debug('WATCH currentEventRef', currentEventRef.value);
			if (currentEventRef.value) {
				eventPropertiesRef.value = Object.assign({}, currentEventRef.value);
			}
		});

		async function btnDeleteEvent() {
			await deleteEvent(currentEventRef.value?.eventId as string);
		}

		return {
			deleteConfirmTextRef,
			deleteConfirmed,
			eventPropertiesRef,
			updateEvent,
			btnDeleteEvent,
			isLoadingRef,
			currentEventRef
		};
	}
});
</script>
<style scoped>

</style>
