<template>
  <b-container class="mt-5 mb-5">
    <template v-if="isLoadingRef">
      <b-skeleton width="85%" />
      <b-skeleton width="55%" />
      <b-skeleton width="70%" />
    </template>
    <div v-else>
      <b-card
        v-if="!eventEmployeeInvitationRef"
        bg-variant="danger"
        text-variant="white"
        header="Request Invalid"
        class="text-center"
      >
        <b-card-text>Dieser Link scheint fehlerhaft oder nicht mehr gültig zu sein.</b-card-text>
      </b-card>
      <div v-else>
        <b-card
          v-if="eventEmployeeInvitationRef.status == 'CREATED'"
          title="Einladung zur Zusammenarbeit"
          sub-title="INN//Order"
        >
          <b-card-text>
            Hallo{{ currentUserRef && currentUserRef.attributes.given_name ? " " + currentUserRef.attributes.given_name:"" }},
            <br>du wurdest eingeladen, auf der der Veranstaltung {{ eventEmployeeInvitationRef.eventName }} mitzuarbeiten.
          </b-card-text>
          <b-card-text v-if="invitateventEmployeeInvitationRefion && eventEmployeeInvitationRef.message">
            Folgende Nachricht wurde dir hinterlassen:<br>{{ eventEmployeeInvitationRef.message }}
          </b-card-text>
          <b-card-text>
            Bitte bestätige die Zusammenarbeit:
          </b-card-text>
          <LoadButton
            variant="success"
            text="Einladung akzeptieren"
            icon="check"
            @click="answerInvitation($event, 'ACCEPT')"
          />
          <LoadButton
            variant="outline-danger"
            text="Einladung ablehnen"
            icon="trash"
            @click="answerInvitation($event, 'DECLINE')"
          />
        </b-card>
        <b-card
          v-if="eventEmployeeInvitationRef.status !== 'CREATED'"
          class="text-center"
        >
          <b-alert
            show
            variant="success"
          >
            Deine Antwort wurde erfolgreich übermittelt!
          </b-alert>
        </b-card>
      </div>
    </div>
  </b-container>
</template>
<script lang="ts">

import LoadButton from '../../buttons/LoadButton.vue';
import { defineComponent, onMounted, ref, getCurrentInstance } from '@vue/composition-api';
import { useUserState } from '../../../states/userState';
import { eventEmployeeInvitation as api } from '@innevent/webapp-api'; 
import type { EventEmployeeInvitation } from '@innevent/types';
import { notifyError } from '@innevent/webapp-utils';
import type { ResetButton } from '@innevent/webapp-types';


export default defineComponent({
	components: { 
		LoadButton 
	},
	setup(props, context) {
		const route = context.root.$route;
		const { currentUserRef } = useUserState();
		const eventEmployeeInvitationRef = ref<EventEmployeeInvitation>();
		const isLoadingRef = ref<boolean>(true);
		const instance = getCurrentInstance();


		onMounted(async () => {
			try {
				eventEmployeeInvitationRef.value = await api.getEventEmployeeInvitation({
					key: {
						eventId: route.query.eventId,
						eventEmployeeInvitationId: route.params.eventEmployeeInvitationId
					}
				});
        
			} catch (error: any) {
				notifyError({ instance, error });
			}
			isLoadingRef.value = false;
		});

		async function answerInvitation(btn: ResetButton, action) {
			try {
				if (action == 'ACCEPT') {
					eventEmployeeInvitationRef.value = await api.acceptInvitation({
						key: {
							eventId: route.query.eventId,
							eventEmployeeInvitationId: route.params.eventEmployeeInvitationId
						}
					});
				} else {
					eventEmployeeInvitationRef.value = await api.declineInvitation({
						key: {
							eventId: route.query.eventId,
							eventEmployeeInvitationId: route.params.eventEmployeeInvitationId
						}
					});
				}	
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}
			
      
		return { 
			currentUserRef, 
			isLoadingRef,
			eventEmployeeInvitationRef, 
			answerInvitation 
		};
	}
});
</script>