const HTMLWebpackPlugin = require('html-webpack-plugin')
const { join, resolve } = require('path')
const { VueLoaderPlugin } = require('vue-loader')
const { HotModuleReplacementPlugin } = require('webpack')
var GitRevisionPlugin = require('git-revision-webpack-plugin')
const git = new GitRevisionPlugin()
const Dotenv = require('dotenv-webpack')
const WebpackBar = require('webpackbar');

const branchMap = {
  dev: 'dev',
  master: 'stg',
  prod: 'prod'
}

module.exports = {
  mode: 'development',
  entry: ['babel-polyfill', join(__dirname, 'app.ts')],
  output: {
    path: join(__dirname, 'dist'),
    filename: git.branch() + '-bundle.js',
    publicPath: '/',
    clean: true
  },
  devtool: 'eval-source-map',
  resolve: {
    alias: {
      vue: 'vue/dist/vue.js'
      // 'vue$': 'vue/dist/vue.esm.js'
    },
    extensions: ['.ts', '.js', '.json'],
    symlinks: false,
    fallback: {
      "fs": false,
      "tls": false,
      "net": false,
      "path": false,
      "zlib": false,
      "http": false,
      "https": false,
      "stream": false,
      "crypto": false,
      "util": false,
      "crypto": require.resolve("crypto-browserify")
    }
  },
  devServer: {
    host: '0.0.0.0',
    port: 81,
    // contentBase: join(__dirname, 'src/'),
    publicPath: '/',
    historyApiFallback: true
  },
  plugins: [
    new Dotenv({
      path: '../../env/' + branchMap[process.env.AWS_BRANCH] + '.env',
      defaults: './../../env/dev.env'
    }),
    new VueLoaderPlugin(),
    new HotModuleReplacementPlugin(),
    new HTMLWebpackPlugin({
      title: 'Organizer Portal - INN//SYSTEMS',
      filename: 'index.html',
      template: 'src/index-template.html'
    }),
    new WebpackBar()

  ],
  module: {
    rules: [
      {
        test: /\.js$/,
        use: [{
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }],
      },
      {
        test: /\.m?js/,
        resolve: {
          fullySpecified: false
        }
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.tsx?$/,
        use: [{
          loader: 'ts-loader',
          options: {
            appendTsSuffixTo: [/\.vue$/],
            transpileOnly: true
          }
        }],
        // exclude: /node_modules/, //DO NOT EXCLUDE NODE_MODULES => Sonst können die Workspace Packages nicht geladen werden
        // include: [
        //   resolve(__dirname, "../../node_modules/@innevent")
        // ],
      },
      {
        test: /\.css$/,
        use: [
          'vue-style-loader',
          'css-loader'
        ]
      },
      {
        test: /\.(scss)$/,
        use: [{
          loader: 'vue-style-loader', // inject CSS to page
        }, {
          loader: 'css-loader', // translates CSS into CommonJS modules
        }, {
          loader: 'postcss-loader', // Run post css actions
          options: {
            plugins: function () { // post css plugins, can be exported to postcss.config.js
              return [
                require('precss'),
                require('autoprefixer')
              ];
            }
          }
        }, {
          loader: 'sass-loader' // compiles Sass to CSS
        }]
      },
      {
        test: /\.(gif|png|jpe?g|svg)$/i,
        use: [{
          loader: 'file-loader?outputPath=images/',
          options: { esModule: false, outputPath: "images/" }
        }]
      }
    ]
  }
}
