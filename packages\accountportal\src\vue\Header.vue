<template>
  <b-navbar
    toggleable="xl"
    type="dark"
    variant="dark"
    sticky
  >
    <b-container
      fluid
      class="max-w1600"
    >
      <b-navbar-brand href="#">
        <img
          height="40"
          src="../img/logo-transparent.png"
        >{{ headerText }}
      </b-navbar-brand>

      <b-navbar-toggle target="nav-collapse" />

      <b-collapse
        id="nav-collapse"
        is-nav
      >
        <b-navbar-nav>
          <b-nav-item :href="URL_ORGANIZERPORTAL">
            Veranstalterportal
          </b-nav-item>
          <b-nav-item :href="URL_VENDORPORTAL">
            Verkäuferportal
          </b-nav-item>
          <b-nav-item :href="URL_CUSTOMERPORTAL">
            Kundenportal
          </b-nav-item>
        </b-navbar-nav>

        <b-navbar-nav
          class="ml-auto"
          pills
        >
          <b-nav-item-dropdown right>
            <template #button-content>
              <FontAwesomeIcon
                icon="user-circle"
                fixed-width
              /><span class="ml-1 mr-1">{{ btnAccountText }}</span>
            </template>
            <b-dropdown-text class="text-center">
              {{ accountName }}
            </b-dropdown-text>
            <b-dropdown-text>{{ accountEmail }}</b-dropdown-text>
            <b-dropdown-divider />
            <b-dropdown-item to="/account">
              <FontAwesomeIcon
                icon="user-cog"
                fixed-width
              /><span class="ml-3">Kontoeinstellungen</span>
            </b-dropdown-item>
            <b-dropdown-item to="/logout">
              <FontAwesomeIcon
                icon="power-off"
                fixed-width
              /><span class="ml-3">Abmelden</span>
            </b-dropdown-item>
          </b-nav-item-dropdown>
        </b-navbar-nav>
      </b-collapse>
    </b-container>
  </b-navbar>
</template>
<script>

export default {
	components: { },
	props: ['portalName', 'currentPortalObjectId', 'portalObjects', 'cognitoUser'],
	data() { return {
		btnAccountText: 'Mein Konto',
		URL_VENDORPORTAL: process.env.URL_VENDORPORTAL,
		URL_ORGANIZERPORTAL: process.env.URL_ORGANIZERPORTAL,
		URL_CUSTOMERPORTAL: process.env.URL_CUSTOMERPORTAL,
		URL_INNLOGIN: process.env.URL_INNLOGIN
	};},
	computed:{
		headerText() {
			return ` Inn//Event Account${ process.env.STAGE == 'prod' ? '' : '__' + process.env.STAGE }`;
		},
		accountEmail() {
			if (!this.cognitoUser) return '';
			return this.cognitoUser.attributes.email;
		},
		accountName() {
			let u = this.cognitoUser;
			if (!u) { return ''; }
			if (u.attributes.given_name && u.attributes.family_name) {
				return u.attributes.given_name + ' ' + u.attributes.family_name;
			} else {
				return u.username;
			}
		}
	}
};
</script>
<style scoped>

</style>
