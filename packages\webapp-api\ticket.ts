import { apiInnTicket } from './instances';
import type { Ticket } from '@innevent/webapp-types';

type ModelType = Ticket;
type PrimaryKey = 'ticketId';


export type TicketCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'ticketName' | 'description' | 'personalizationRequired' | 'ticketCodePrefix' | 'ticketGroups' | 'ticketSaleProperties'>;
}
export async function createTicket(options: TicketCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/Ticket', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type TicketDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteTicket(options: TicketDeleteOptions): Promise<void> {
	await (await apiInnTicket()).delete(`/Ticket/${options.key.ticketId}`, { 
		params: { eventId: options.key.eventId } 
	});
}


export type TicketUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Pick<ModelType, 'ticketName' | 'description' |'ticketCodePrefix' | 'ticketGroups' | 'ticketSaleProperties'>;
};

export async function updateTicket(options: TicketUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/Ticket/${options.key.ticketId}`, 
		{
			ticketName: options.data.ticketName,
			description: options.data.description,
			//personalizationRequired: options.data.personalizationRequired,
			ticketCodePrefix: options.data.ticketCodePrefix,
			ticketGroups: options.data.ticketGroups,
			ticketSaleProperties: options.data.ticketSaleProperties
		}, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}


export type TicketGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getTickets(options: TicketGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnTicket()).get('/Ticket', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}