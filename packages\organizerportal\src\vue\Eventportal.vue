<template>
  <b-container fluid>
    <div
      v-if="currentEventRef"
      class="d-flex flex-column flex-md-row"
    >
      <div class="p-3 shadow sidebar-nav">
        <Navigation />
      </div>
      <div class="flex-fill p-3">
        <router-view
          :selected-event="currentEventRef"
        />
      </div>
    </div>
    <b-alert
      v-else
      show
      variant="primary"
      class="mt-5"
    >
      404 Event Not Found (or is loading...)
    </b-alert>
  </b-container>
</template>
<script lang="ts">

import Navigation from './Eventportal/Navigation.vue';
import { useEventState } from '../states/eventState';
import { defineComponent } from '@vue/composition-api';

export default defineComponent({
	components: { Navigation },
	setup() {
		const { currentEventRef } = useEventState();

		return {
			currentEventRef
		};
	}
});
</script>
<style scoped>
</style>
