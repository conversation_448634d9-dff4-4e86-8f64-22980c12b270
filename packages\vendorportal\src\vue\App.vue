<template>
  <div>
    <Header
      :selected-vendor="vendorData.vendor"
      :vendors="vendors"
      :cognito-user="cognitoUser"
      :container-class="containerClass"
    />
    <b-container
      fluid
      :class="containerClass"
    >
      <div
        v-if="loading"
        class="d-flex justify-content-center mt-5"
      >
        <b-spinner
          style="width: 5rem; height: 5rem;"
          variant="primary"
          class="float-right"
        />
      </div>
      <router-view
        v-else-if="$route.name == 'portalHome' || $route.name == 'vendorInvitation' || $route.name == 'vendorStaffInvitation'"
        :vendors="vendors"
        :cognito-user="cognitoUser"
      />
      <div
        v-else-if="vendorData.vendor"
        class="d-flex flex-column flex-md-row"
      >
        <div class="p-3 shadow sidebar-nav">
          <router-view
            name="nav"
            :selected-vendor="vendorData.vendor"
            :selected-event="selectedEvent"
          />
        </div>
        <div class="flex-fill p-3">
          <router-view
            :cognito-user="cognitoUser"
            :selected-vendor="vendorData.vendor"
            :selected-event="selectedEvent"
            @reloadVendor="loadVendorData"
          />
        </div>
      </div>
      <b-alert
        v-else
        show
        variant="danger"
        class="mt-5"
      >
        404 Vendor Not Found
      </b-alert>
    </b-container>
    <ModalCreateVendor 
      @organizationCreated="onOrganizationCreated" 
    />
  </div>
</template>
<script>

// Frameworks
import { API } from 'aws-amplify';
import EventBus from './event-bus';

// Vue Components
import Header from './Header.vue';
import ModalCreateVendor from '@innevent/webapp-components/modal/ModalCreateVendor.vue';
import { defineComponent } from '@vue/composition-api';

export default defineComponent({
	components: { 
		Header, 
		ModalCreateVendor 
	},
	props: ['cognitoUser'],
	data() { return {
		vendors: [],
		vendorData: {},
		loading: false,
		containerClass: 'max-w2000'
	};},
	computed: {
		selectedEvent() {
			if (this.$route.params.event) {
				let response = null;
				this.vendorData.vendor.VendorEventProperties.forEach(ep => {
					if (ep.Event.id == this.$route.params.event) response = ep.Event;
				});
				return response;
			} else return null;
		}
	},
	watch: {
		$route(to, from) {
			// console.log(to)
			// console.log(from)
			// Wenn Vendor geändert wird
			if (to.params.vendor && to.params.vendor != from.params.vendor) {
				this.loadVendorData(this.$route.params.vendor);
			} else if (!to.params.vendor) {
				this.vendorData = {};
			}
		}
	},
	mounted() {
		EventBus.$on('organizationCreated', vendor => {
			vendor.name = vendor.organizationType == 'PERSON' ? vendor.firstName + ' ' + vendor.lastName : vendor.organizationName;
			this.vendors.push(vendor);
		});
	},
	created: async function() {
		this.loadVendors();

		// Vendor oder Event per URL gesetzt
		if (this.$route.params.vendor) {
			this.loadVendorData(this.$route.params.vendor);
		}
	},
	methods: {
		onOrganizationCreated(event) {
			EventBus.$emit('organizationCreated', event);
		},
		loadVendors() {
			API.get('innorder', '/Vendor').then(response => {
				this.vendors = response;
			}).catch(error => {
				this.$bvToast.toast('Beim Laden der Vendors ist ein Fehler aufgetreten.', {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		loadVendorData(vendorId) {
			this.loading = true;
			this.vendorData = {};
			this.loadVendors();
			API.get('innorder', '/getVendorPortalData/Vendor/' + vendorId).then(response => {
				this.vendorData = response;
				this.loading = false;
			}).catch(error => {
				this.loading = false;
				this.vendorData = {};
			});
		}
	}
});

</script>
