import { apiInnEvent } from './instances';
import type { AuthCache } from '@innevent/types';
import type { AxiosResponse } from 'axios';

export type AuthCacheGetOptions =  {
    key: Pick<AuthCache, 'authCacheId'>;
};
export async function getAuthCache(options: AuthCacheGetOptions): Promise<AuthCache> {
	const response = await (await apiInnEvent({
		skipAuth: true
	})).get(`/AuthCache/${options.key.authCacheId}`);
	return response.data;
}


export type AuchCacheCreateOptions =  {
    data: Pick<AuthCache, 'accessToken' | 'idToken' | 'refreshToken' | 'username'>;
    fullResponse?: false;
};
export type AuchCacheCreateOptionsFullResponse = {
    data: Pick<AuthCache, 'accessToken' | 'idToken' | 'refreshToken' | 'username'>;
    fullResponse: true;
};
export async function createAuthCache(options: AuchCacheCreateOptionsFullResponse): Promise<AxiosResponse<void>>;
export async function createAuthCache(options: AuchCacheCreateOptions): Promise<void>;

export async function createAuthCache(
	options: AuchCacheCreateOptions | AuchCacheCreateOptionsFullResponse
): Promise<AxiosResponse<void> | void> {
	const response = await (await apiInnEvent()).post('/AuthCache', options.data);
	console.log(response);
	if (options.fullResponse) return response;
}
