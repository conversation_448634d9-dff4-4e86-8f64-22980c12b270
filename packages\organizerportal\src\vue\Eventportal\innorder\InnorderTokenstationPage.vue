<template>
  <div>
    <b-row>
      <b-col><h2>Tokenstation</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="Tokenstation erstellen"
          icon="plus-square"
          @click="btnCreateTokenstation"
        />
      </b-col>
    </b-row>
    <LoadingSpinner v-if="isLoadingInitialRef" />
    <template v-if="employeesOfEventRef && tokenstationsRef">
      <TokenstationVue
        v-for="ts in tokenstationsRef"
        :key="ts.id"
        :tokenstation="ts"
        @deleteTokenstation="btnDeleteTokenstation"
      />
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted } from '@vue/composition-api';
import LoadingSpinner from '../../elements/LoadingSpinner.vue';
import LoadButton from '../../buttons/LoadButton.vue';
import { currentEventRef } from '../../../states/eventState';
import { useEmployeeState, loadEventEmployeesOfEvent } from '../../../states/employeeState';
import { useTokenstationState } from '../../../states/tokenstationState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { ResetButton } from '@innevent/webapp-types';
import type { Tokenstation } from '@innevent/webapp-types';
import TokenstationVue from './elements/Tokenstation.vue';


export default defineComponent({
	components: { 
		LoadingSpinner, 
		LoadButton, 
		TokenstationVue 
	},
	setup() {
		const { employeesOfEventRef } = useEmployeeState();
		const { loadTokenstations, tokenstationsRef, deleteTokenstation, createTokenstation, isLoadingInitialRef } = useTokenstationState();
		const instance = getCurrentInstance();


		onMounted(async ()  => {
			try {
				await loadTokenstations(currentEventRef.value!.eventId!);
				await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
			} catch (error: any) {
				notifyError({ instance, error });
			}			
		});

		async function btnDeleteTokenstation(btn: ResetButton, tokenstation: Tokenstation) {
			try {
				await deleteTokenstation({
					key: {
						id: tokenstation.id
					}
				});
				notifySuccess({ instance, message: 'Tokenstation gelöscht!' });
			} catch (error) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		async function btnCreateTokenstation(btn: ResetButton) {
			try {
				const item: Partial<Tokenstation> = {
					name: 'Neue Tokenstation',
					description: '',
					EventId: currentEventRef.value!.eventId!
				};
				await createTokenstation({
					data: item as Tokenstation
				});
				notifySuccess({ instance, message: 'Tokenstation erstellt!' });
			} catch (error) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			currentEventRef,
			tokenstationsRef,
			isLoadingInitialRef,
			btnDeleteTokenstation,
			btnCreateTokenstation,
			employeesOfEventRef
		};
	}
});
</script>
