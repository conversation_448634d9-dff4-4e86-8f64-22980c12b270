import { apiInnTicket } from './instances';
import type { TicketImport, TicketImportExecution, TicketImportFile, ValueMappingItem }  from '@innevent/types';
import axios from 'axios';


type ModelType = TicketImport;
type PrimaryKey = 'ticketImportId';


export type TicketImportCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'name' | 'description' | 'isFirstLineHeader'>;
}

export async function createTicketImport(options: TicketImportCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/TicketImport', options.data, { 
		params: { 
			'eventId': options.key.eventId, 
			'returnValue': 'ALL_NEW' 
		} 
	});
	return response.data;
}

export type TicketImportDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteTicketImport(options: TicketImportDeleteOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).delete(`/TicketImport/${options.key.ticketImportId}`, { 
		params: { 'eventId': options.key.eventId } 
	});
	return response.data;
}


export type TicketImportUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'columns' | 'isFirstLineHeader' | 'description' | 'name' | 'valueMapping' | 'fieldMapping'>>;
};
export async function updateTicketImport(options: TicketImportUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/TicketImport/${options.key.ticketImportId}`, options.data, { 
		params: { 
			'eventId': options.key.eventId, 
			'returnValue': 'ALL_NEW' 
		} 
	}
	);
	return response.data;
}


export type TicketImportsGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getTicketImports(options: TicketImportsGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnTicket()).get('/TicketImport', { 
		params: { 'eventId': options.key.eventId } 
	});
	return response.data;
}


export type TicketImportGetOptions =  {
    key: Pick<ModelType, 'eventId' | 'ticketImportId'>;
};
export async function getTicketImport(options: TicketImportGetOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).get(`/TicketImport/${options.key.ticketImportId}`, { 
		params: { 'eventId': options.key.eventId } 
	});
	return response.data;
}


//Upload S3
export type CreateUploadOptions = {
	key: Pick<ModelType, 'eventId' | 'ticketImportId'>;
	data: {
		filename: string;
	};
};
export async function createUpload(options: CreateUploadOptions): Promise<TicketImportFile> {
	const response = await (await apiInnTicket()).patch(`/TicketImport/${options.key.ticketImportId}/CreateUpload`, 
		{
			filename: options.data.filename
		}, { 
			params: { 
				'eventId': options.key.eventId, 
				'returnValue': 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}


export type UploadFileOptions = {
	data: { 
		uploadUrl: string;
		file: Blob ; 
	};
};
export async function uploadTicketImportFile(options: UploadFileOptions): Promise<ModelType> {
	const formData = new FormData();
	formData.append('file', options.data.file);
	const response = await axios.put(options.data.uploadUrl, formData, { 
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	});
	return response.data;
}


export type RunImportOptions = {
	key: Pick<ModelType, 'eventId' | 'ticketImportId'>;
	data: {
		importFileId: string;
	};
};
export async function runImport(options: RunImportOptions): Promise<TicketImportExecution> {
	const response = await (await apiInnTicket()).patch(`/TicketImport/${options.key.ticketImportId}/RunImport`, 
		{
			importFileId: options.data.importFileId
		}, { 
			params: { 
				'eventId': options.key.eventId, 
				'returnValue': 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}


export type UpdateTicketImportValueMappingOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey> & {
		mappingAttribute: keyof ModelType['valueMapping'];
	};
	data: ValueMappingItem[];
};
export async function updateTicketImportValueMapping(options: UpdateTicketImportValueMappingOptions): Promise<ModelType> {
	const url = `/TicketImport/${options.key.ticketImportId}/ValueMapping/${options.key.mappingAttribute}`;
	const response = await (await apiInnTicket()).patch(url, options.data, { 
		params: { 
			'eventId': options.key.eventId, 
			'returnValue': 'ALL_NEW' 
		},
		data: options.data
	}
	);
	return response.data;
}

export type RemoveTicketImportFileOptions = {
	key: Pick<ModelType, 'eventId' | 'ticketImportId'> & {
		fileNumber: number;
	};
};
export async function removeTicketImportFile(options: RemoveTicketImportFileOptions): Promise<TicketImportExecution> {
	const url = `/TicketImport/${options.key.ticketImportId}/RemoveFile`;
	const response = await (await apiInnTicket()).patch(url, {
		importFileId: options.key.fileNumber
	}, { 
		params: { 
			'eventId': options.key.eventId, 
			'returnValue': 'ALL_NEW' 
		} 
	}
	);
	return response.data;
}