{"name": "innevent-webapps", "version": "1.0.0", "main": "index.js", "repository": "https://gitlab.com/innevent/webapp/innevent-webapps.git", "scripts": {"lint": "eslint ./", "lintfix": "eslint --fix ./", "organizer": "cd packages/organizerportal && yarn run dev", "customer": "cd packages/customerportal && yarn run dev", "account": "cd packages/accountportal && yarn run dev", "vendor": "cd packages/vendorportal && yarn run dev", "build-organizer": "cd packages/organizerportal && yarn run build", "build-customer": "cd packages/customerportal && yarn run build", "build-account": "cd packages/accountportal && yarn run build", "build-vendor": "cd packages/vendorportal && yarn run build", "merge-dev": "git checkout dev && git pull && git merge - && git push && git checkout - && git push"}, "author": "<PERSON><PERSON>", "license": "MIT", "private": true, "engines": {"npm": "please-use-yarn", "yarn": ">= 1.0.0"}, "workspaces": {"packages": ["packages/*"]}, "types": "./vue-shim.d.ts", "devDependencies": {"@innevent/types": "1.2.1", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "eslint": "^7.20.0", "eslint-plugin-unused-imports": "^1.1.0", "eslint-plugin-vue": "^7.7.0", "typescript": "^4.2.2"}}