import type { EcTerminal, PartialPick } from '@innevent/types';
import { ecterminal as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { ecTerminalState as log } from '../loglevel';
import Vue from 'vue';
import { useEmployeeState } from './employeeState';
import type { ReferenceObject } from '@innevent/webapp-types';

const { employeesOfEventRef } = useEmployeeState();

log.debug('INIT ecTerminalState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const ecTerminalsRef = ref<EcTerminal[]>([]);


watch(ecTerminalsRef, () => {
	log.debug('WATCH ecTerminalsRef', ecTerminalsRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useEcTerminalState() {
	log.debug('useEcTerminalState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		ecTerminalsRef,
		loadEcTerminals,
		deleteEcTerminal,
		createEcTerminal,
		updateEcTerminal
	};
}

type LoadEcTerminalStateOption = {
	key: ReferenceObject;
}
async function loadEcTerminals(options: LoadEcTerminalStateOption): Promise<void> {
	log.debug('loadEcTerminals()', options);
	isLoadingRef.value = true;
	try {
		ecTerminalsRef.value = await api.listEcTerminals(options);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type DeleteEcTerminalStateOption = {
	key: Pick<EcTerminal, 'ecTerminalId'> & ReferenceObject;
}
async function deleteEcTerminal(options: DeleteEcTerminalStateOption): Promise<void> {
	log.debug('deleteEcTerminal()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteEcTerminal(options);
		ecTerminalsRef.value = ecTerminalsRef.value.filter((ecTerminal) => ecTerminal.ecTerminalId !== options.key.ecTerminalId);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}


type CreateEcTerminalStateOption = {
	key: ReferenceObject;
	data: PartialPick<EcTerminal, 'name' | 'description'>;
}
async function createEcTerminal(options: CreateEcTerminalStateOption): Promise<void> {
	log.debug('createEcTerminal()', options);
	isLoadingRef.value = true;
	try {
		const ecTerminal = await api.createEcTerminal(options);
		ecTerminalsRef.value.push(ecTerminal);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type UpdateEcTerminalStateOption = {
	key: Pick<EcTerminal, 'ecTerminalId'> & ReferenceObject;
	data: PartialPick<EcTerminal, 'name' | 'description'>;
}
async function updateEcTerminal(options: UpdateEcTerminalStateOption): Promise<void> {
	log.debug('updateEcTerminal()', options);
	isLoadingRef.value = true;
	try {
		const updatedEcTerminal = await api.updateEcTerminal(options);
		const index = ecTerminalsRef.value.findIndex((oldEcTerminal) =>oldEcTerminal.ecTerminalId == updatedEcTerminal.ecTerminalId);
		if (index == -1) {
			log.debug('updateCashbox()', 'Could not find EcTerminal');
			return;
		}
		Vue.set(ecTerminalsRef.value, index, updatedEcTerminal);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}