// Vue Components
import Vue from 'vue'

import VueCompositionAPI from '@vue/composition-api';
Vue.use(VueCompositionAPI);

import VueRouter from 'vue-router'
Vue.use(VueRouter)
import Login from './src/vue/Login.vue'
import Account from './src/vue/Account.vue'
import Logout from './src/vue/Logout.vue'
import App from './src/vue/App.vue'
import PageNotFound from './src/vue/PageNotFound.vue'
import ConfirmSignUp from './src/vue/ConfirmSignUp.vue'
import ConfirmEmail from './src/vue/ConfirmEmail.vue'
import ForgotPassword from './src/vue/ForgotPassword.vue'
import AccountOverview from './src/vue/account/AccountOverview.vue'
import AccountPersonalData from './src/vue/account/AccountPersonalData.vue'
import AccountSecurity from './src/vue/account/AccountSecurity.vue'
import AccountNotification from './src/vue/account/AccountNotification.vue'

// Bootstrap
import { BootstrapVue } from 'bootstrap-vue'
import 'bootstrap' //Importing JavaScript https://getbootstrap.com/docs/4.1/getting-started/webpack/
import 'bootstrap/dist/css/bootstrap.min.css' //Importing Compiled CSS
import 'bootstrap-vue/dist/bootstrap-vue.css'
Vue.use(BootstrapVue)

// CSS
import './src/css/global.css'

// Fontawesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
library.add(fas)
library.add(far)
Vue.component('FontAwesomeIcon', FontAwesomeIcon)
// Vue.config.productionTip = false
// https://github.com/FortAwesome/vue-fontawesome


import Amplify from '@aws-amplify/core';
import { Auth } from 'aws-amplify'
import awsconfig from './src/js/AmplifyConfig';
Amplify.configure(awsconfig);
Auth.configure(awsconfig)

// Vue Router
const router = new VueRouter({
  mode: 'history',
  routes: [
    // dynamic segments start with a colon
    { path: '/', redirect: '/account' },
    { path: '/login', alias: '/register', component: Login },
    { path: '/logout', component: Logout },
    { path: '/account', component: Account, children: [
        { path: 'overview', component: AccountOverview },
        { path: 'personaldata', component: AccountPersonalData },
        { path: 'security', component: AccountSecurity },
        { path: 'notification', component: AccountNotification },
        { path: '/', redirect: 'overview' }
    ]},
    { path: '/confirmUser', component: ConfirmSignUp },
    { path: '/confirmEmail', component: ConfirmEmail },
    { path: '/forgotPassword', component: ForgotPassword },
    { path: '*', component: PageNotFound }
  ]
})

var app = new Vue({
  router,
  render: (createEl) => createEl(App)
}).$mount('#app')
