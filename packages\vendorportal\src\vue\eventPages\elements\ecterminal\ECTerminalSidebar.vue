<template>
  <b-sidebar
    v-model="sidebarVisible"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="1"
    :title="terminal ? terminal.name : ''"
    width="1000px"
    backdrop
    right
  >
    <template v-if="terminal">
      <h3>Basic</h3>
      <label for="terminalInputName">Name</label>
      <b-form-input
        id="terminalInputName"
        v-model="terminal.name"
        max-length="20"
      />
      <label
        for="terminalInputDescription"
        class="mt-2"
      >Beschreibung</label>
      <b-form-input
        id="terminalInputDescription"
        v-model="terminal.description"
      />
      <LoadButton
        size="sm"
        icon="save"
        class="mt-3"
        @click="updateTerminal"
      />
      <b-row>
        <b-col>
          <h4 class="mt-3">
            Zugewiesene Mitarbeiter
          </h4>
          <p v-if="terminal.VendorEmployees.length == 0">
            Derzeit sind keine Mitarbeiter zugeordnet.
          </p>
          <b-list-group v-else>
            <b-list-group-item
              v-for="employee of terminal.VendorEmployees"
              :key="employee.sub"
              class="d-flex"
            >
              {{ employee.firstname + " " + employee.lastname }}
              <LoadButton
                text=""
                icon="arrow-circle-right"
                size="sm"
                class="ml-auto"
                @click="deleteTerminalEmployeeAssign($event, employee)"
              />
            </b-list-group-item>
          </b-list-group>
        </b-col>
        <b-col>
          <h4 class="mt-3">
            Verfügbare Mitarbeiter
          </h4>
          <b-list-group>
            <b-list-group-item
              v-for="employee of unassignedEmployees"
              :key="employee.sub"
            >
              <LoadButton
                text=""
                icon="arrow-circle-left"
                size="sm"
                @click="addTerminalEmployeeAssign($event, employee)"
              />
              {{ employee.firstname + " " + employee.lastname }}
            </b-list-group-item>
          </b-list-group>
        </b-col>
      </b-row>
      <b-row class="mt-5">
        <b-col><h3>Buchungen</h3></b-col>
        <b-col class="d-flex">
          <LoadButton
            v-b-modal.modalAddTransaction
            class="ml-auto"
            style="height: fit-content"
            size="sm"
            text="Buchung erstellen"
            icon="plus-square"
            @click="
              (btn) => {
                btn.reset();
              }
            "
          />
        </b-col>
      </b-row>
      <b-table
        ref="tableECTerminalTransactions"
        :items="getECTerminalTransactions"
        :current-page="currentPage"
        :per-page="perPage"
        :fields="fieldsTransactionTable"
        class="mt-3"
        striped
        hover
        head-variant="light"
      >
        <template #table-busy>
          <div class="text-center text-primary my-2">
            <b-spinner class="align-middle" />
            <strong>Loading...</strong>
          </div>
        </template>
        <template #cell(EventEmployee)="data">
          {{ data.value.firstname + " " + data.value.lastname }}
        </template>
      </b-table>
      <b-pagination
        v-model="currentPage"
        :per-page="perPage"
        :total-rows="rows"
        align="center"
      />
    </template>
    <b-modal
      id="modalAddTransaction"
      title="Buchung hinzufügen"
    >
      <div role="group">
        <label for="input1">Betrag:</label>
        <b-input-group
          id="input1"
          append="€"
        >
          <b-form-input
            v-model="manualTransaction.creditChange"
            type="number"
          />
        </b-input-group>
      </div>
      <div
        role="group"
        class="mt-3"
      >
        <label for="input2">Kommentar:</label>
        <b-form-input
          id="input2"
          v-model="manualTransaction.comment"
          :state="manualTransaction.comment.length > 2"
          aria-describedby="input2feedback"
          placeholder="Kommentar"
          trim
          @keyup.enter="btnClickCreateTransaction()"
        />
        <b-form-invalid-feedback id="input2feedback">
          Es werden mindestens 3 Buchstaben zur Beschreibung erwartet
        </b-form-invalid-feedback>
      </div>
      <template #modal-footer="{ hide }">
        <LoadButton
          ref="loadingBtnCreateTrans"
          variant="primary"
          size="sm"
          icon="save"
          text="Buchung erstellen"
          @click="createTransaction($event, hide)"
        />
      </template>
    </b-modal>
  </b-sidebar>
</template>
<script>
import { API } from 'aws-amplify';
import LoadButton from '../../../buttons/LoadButton.vue';
let moment = require('moment');
const uuid = require('uuid/v4');

export default {
	components: { LoadButton },
	props: ['terminal', 'value', 'employees'],
	data() {
		return {
			fieldsTransactionTable: [
				{
					key: 'timestamp',
					label: 'Uhrzeit/Datum',
					formatter: (val) => this.formatDate(val)
				},
				{
					key: 'creditBefore',
					label: 'Betrag vorher',
					formatter: (val) => this.formatCurrency(val)
				},
				{
					key: 'creditChange',
					label: 'Betrag',
					formatter: (val) => this.formatCurrency(val)
				},
				{
					key: 'creditAfter',
					label: 'Betrag nachher',
					formatter: (val) => this.formatCurrency(val)
				},
				{ key: 'comment', label: 'Kommentar' },
				{ key: 'EventEmployee', label: 'Mitarbeiter' }
			],
			currentPage: 1,
			perPage: 10,
			rows: 0,
			manualTransaction: {
				comment: ''
			}
		};
	},
	computed: {
		sidebarVisible: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			}
		},
		unassignedEmployees() {
			return this.employees.filter((employee) => {
				return !this.terminal.VendorEmployees.some(
					(terminalEmployee) => terminalEmployee.id == employee.id
				);
			});
		}
	},
	created() {},
	methods: {
		btnClick() {
			this.$refs.loadingBtnCreateTrans.$el.click();
		},
		createTransaction(btn, hideModal) {
			this.manualTransaction.id = uuid();
			API.post('core', '/ECTerminal/' + this.terminal.id + '/Transaction', {
				body: this.manualTransaction
			})
				.then((response) => {
					this.$bvToast.toast('Die Transaktion wurde erfolgreich erstellt.', {
						title: 'Erstellen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});
					this.manualTransaction.comment = '';
					this.manualTransaction.creditChange = '';
					
					btn.reset();
					hideModal();
					
					this.$refs.tableECTerminalTransactions.refresh();
					this.$emit('reloadData');
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Erstellen der Buchung ist ein Fehler aufgetreten.',
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
					hideModal();
				});
		},
		formatCurrency(value) {
			return new Intl.NumberFormat('de-DE', {
				style: 'currency',
				currency: 'EUR'
			}).format(value);
		},
		async updateTerminal(btn) {
			let attr = {
				name: this.terminal.name,
				description: this.terminal.description
			};
			await API.put('core', '/ECTerminal/' + this.terminal.id, { body: attr })
				.then((response) => {
					this.$bvToast.toast('ECTerminal erfolgreich geändert.', {
						title: 'Erstellen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});
					btn.reset();
				})
				.catch((error) => {
					console.log(error);
					this.$bvToast.toast('Beim Speichern ist ein Fehler aufgetreten.', {
						title: 'Erstellen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
		},
		formatDate(date) {
			return moment(date).locale('de').format('DD. MMMM YYYY HH:mm');
		},
		async deleteTerminalEmployeeAssign(btn, employee) {
			await API.del(
				'core',
				'/ECTerminal/' + this.terminal.id + '/User/' + employee.UserId
			)
				.then((response) => {
					this.terminal.VendorEmployees = this.terminal.VendorEmployees.filter(
						(terminalEmployee) => terminalEmployee.id != employee.id
					);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Löschen des Mitarbeiters ist ein Fehler aufgetreten.',
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		async addTerminalEmployeeAssign(btn, employee) {
			await API.put(
				'core',
				'/ECTerminal/' + this.terminal.id + '/User/' + employee.UserId
			)
				.then((response) => {
					this.terminal.VendorEmployees.push(employee);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Hinzufügen des Mitarbeiters ist ein Fehler aufgetreten.',
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		async deleteTerminal(btn) {
			let item = btn.data.item;
			await API.del('core', '/ECTerminal/' + item.id)
				.then((response) => {
					this.$bvToast.toast(
						'Das EC-Terminal "' + item.name + '" wurde erfolgreich gelöscht.',
						{
							title: 'Löschen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					btn.reset();
				})
				.catch((error) => {
					console.log(error);
					this.$bvToast.toast(
						'Beim Löschen von "' + item.name + '" ist ein Fehler aufgetreten.',
						{
							title: 'Löschen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		async getECTerminalTransactions(ctx) {
			return await API.get(
				'core',
				'/ECTerminal/' +
          this.terminal.id +
          '/Transactions?currentPage=' +
          (ctx.currentPage - 1) +
          '&pageSize=' +
          ctx.perPage
			)
				.then((response) => {
					this.rows = response.count;
					return response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der Transaktionen ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		}
	}
};
</script>
<style>
.top-fixed-header {
  padding-top: 66px;
}
</style>
