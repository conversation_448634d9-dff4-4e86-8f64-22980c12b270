import type { EventEmployeeInvitation } from '@innevent/types';
import { eventEmployeeInvitation as api } from '@innevent/webapp-api'; 
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { currentEventRef } from '.';
import { eventEmployeeInvitationState as log } from '../loglevel';

log.debug('INIT eventEmployeeInvitationState');

const isLoadingRef = ref<boolean>(false);
const eventEmployeeInvitationsRef = ref<EventEmployeeInvitation[]>([]);

watch(eventEmployeeInvitationsRef, () => {
	log.debug('WATCH eventEmployeeInvitationsRef', eventEmployeeInvitationsRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useEventEmployeeInvitationState() {
	log.debug('useEventEmployeeInvitationState()');

	return {
		eventEmployeeInvitationsRef,
		isLoadingRef
	};
}

export async function loadEventEmployeeInvitations(eventId: string): Promise<void> {
	log.debug('loadEventEmployeeInvitations()', eventId);
	isLoadingRef.value = true;
	try {
		const eventEmployeeInvitations = await api.getEventEmployeeInvitations({
			key: { 
				eventId
			}
		});
		eventEmployeeInvitationsRef.value = eventEmployeeInvitations;
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

export async function deleteEventEmployeeInvitation(eventEmployeeInvitationId: string): Promise<void> {
	log.debug('deleteEventEmployeeInvitation()', eventEmployeeInvitationId);
	await api.deleteEventEmployeeInvitation({
		key: { 
			eventId: currentEventRef.value!.eventId!,
			eventEmployeeInvitationId
		}
	});
	eventEmployeeInvitationsRef.value = eventEmployeeInvitationsRef.value.filter((eventEmployeeInvitation) => 
		eventEmployeeInvitation.eventEmployeeInvitationId !== eventEmployeeInvitationId);
}

type EventEmployeeInvitationCreateOption = Pick<EventEmployeeInvitation, 'email'| 'message'>;
export async function createEventEmployeeInvitation(options: EventEmployeeInvitationCreateOption): Promise<void> {
	log.debug('createEventEmployeeInvitation()', options);
	const invitation = await api.createEventEmployeeInvitation({
		data: {
			email: options.email,
			message: options.message,
			eventId: currentEventRef.value!.eventId!
		}
	});
	eventEmployeeInvitationsRef.value.push(invitation);
}