<template>
  <div>
    <b-row>
      <b-col>
        <h2>Ticketgruppen</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto"
            variant="primary"
            size="sm"
            @click="btnCreateTicketGroups()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Ticketgruppe erstellen
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      v-if="ticketMapRef"
      id="ticketGroupTable"
      ref="ticketGroupTable"
      hover
      small
      :items="ticketMapRef.ticketGroups"
      :fields="tableFields"
      :busy="isLoadingRef"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteTicketGroup($event, data)"
          />
        </div>
      </template>
    </b-table>
    <TicketGroupSidebar 
      ref="ticketGroupSidebar"
      :eventId="evenId"
      @ticketGroupChanged="onTicketGroupChange"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import TicketGroupSidebar from './InnticketTicketGroupSidebar.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton, TicketGroup } from '@innevent/webapp-types';
import { formatDate } from '@innevent/webapp-utils';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useTicketMapState } from '../../../../states/ticketMap';

export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		TicketGroupSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const { ticketMapRef, isLoadingRef } = useTicketMapState();
		const ticketGroupTable = ref<BTable>();
		const ticketGroupSidebar = ref<InstanceType<typeof TicketGroupSidebar>>();

		
		const tableFields: BvTableFieldArray =  [
			{ key: 'ticketGroupName', label: 'Name', sortable: true },
			{ key: 'buttons', label: '' }
		];

		async function btnDeleteTicketGroup(btn: ResetButton, cellData: BvTableCellData<TicketGroup>) {
			try {
				await api.deleteTicketGroup({
					key: {
						eventId: cellData.item.eventId,
						ticketGroupId: cellData.item.ticketGroupId
					}
				});
				ticketMapRef.value.ticketGroups = ticketMapRef.value.ticketGroups.filter(item => 
					(item.ticketGroupId !== cellData.item.ticketGroupId));
				onTicketGroupChange();
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(ticketGroup: TicketGroup): void {
			ticketGroupSidebar.value?.openForEdit(ticketGroup);
		}

		function onTicketGroupChange() {
			ticketGroupTable.value?.refresh();
		}

		function btnCreateTicketGroups(): void {
			ticketGroupSidebar.value?.openForCreate();
		}
		

		return {
			tableFields,
			ticketGroupSidebar,
			ticketGroupTable,
			btnDeleteTicketGroup,
			onTicketGroupChange,
			tableRowClick,
			btnCreateTicketGroups,
			formatDate,
			ticketMapRef,
			isLoadingRef
		};
	}
});
</script>

<style>
</style>
