{"name": "@innevent/accountportal", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "webpack-dev-server", "build": "webpack --mode=production", "account": "webpack-dev-server", "lint": "eslint --ext .vue,.js,.ts src/", "lintfix": "eslint --ext .vue,.js,.ts src/ --fix"}, "private": true, "engines": {"npm": "please-use-yarn", "yarn": ">= 1.22.0"}, "author": "<PERSON><PERSON>", "devDependencies": {"@babel/core": "7.10.0", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "7.10.0", "@fortawesome/fontawesome-svg-core": "^1.2.28", "@fortawesome/free-brands-svg-icons": "^5.13.0", "@fortawesome/free-regular-svg-icons": "^5.13.0", "@fortawesome/free-solid-svg-icons": "^5.13.0", "@fortawesome/vue-fontawesome": "^0.1.9", "@vue/compiler-sfc": "3.0.9", "@vue/composition-api": "^1.0.0-rc.6", "aws-amplify": "^4.2.9", "babel-loader": "8.1.0", "babel-polyfill": "6.26.0", "bootstrap-vue": "^2.13.0", "css-loader": "3.6.0", "dotenv-webpack": "^7.0.2", "file-loader": "^6.0.0", "git-revision-webpack-plugin": "^3.0.6", "html-webpack-plugin": "5.3.1", "jquery": "^3.5.0", "password-validator": "^5.0.3", "qrcode.vue": "^1.7.0", "query-string": "^6.12.1", "ts-loader": "^8.1.0", "typescript": "^4.2.3", "validator": "^13.0.0", "vue": "2.6.12", "vue-loader": "15.9.8", "vue-router": "3.5.1", "vue-style-loader": "^4.1.2", "vue-template-compiler": "2.6.12", "webpack": "^5.28.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.10.3", "webpackbar": "^5.0.0-3"}, "dependencies": {"build-url": "^2.0.0", "url-parse": "^1.4.7"}}