/* eslint-disable @typescript-eslint/naming-convention */
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';
import { Auth } from 'aws-amplify';

export type ApiOptions = {
	skipAuth?: boolean;
}

export async function apiInnAccess(): Promise<AxiosInstance> {
	const token = (await Auth.currentSession()).getIdToken().getJwtToken();
	const instanceConfig: AxiosRequestConfig = {
		baseURL: process.env.API_URL_INNACCESS,
		timeout: 10000,
		headers: {
			Authorization: 'Bearer ' + token
		}
	};
	return axios.create(instanceConfig);
}

export async function apiInnTicket(): Promise<AxiosInstance> {
	const token = (await Auth.currentSession()).getIdToken().getJwtToken();
	const instanceConfig: AxiosRequestConfig = {
		baseURL: process.env.API_URL_INNTICKET,
		timeout: 10000,
		headers: {
			Authorization: 'Bearer ' + token
		}
	};
	return axios.create(instanceConfig);
}

export async function apiCore(options?: ApiOptions): Promise<AxiosInstance> {
	const instanceConfig: AxiosRequestConfig = {
		baseURL: process.env.API_URL_CORE,
		timeout: 10000,
		headers: {}
	};
	if (!options || !options.skipAuth) {
		try {
			const token = (await Auth.currentSession()).getIdToken().getJwtToken();
			instanceConfig.headers['Authorization'] = 'Bearer ' + token;
		} catch (error) {
			console.log('User is not logged in, TODO in instances', error);
		}
	}
	return axios.create(instanceConfig);
}

export async function apiInnEvent(options?: ApiOptions): Promise<AxiosInstance> {
	const instanceConfig: AxiosRequestConfig = {
		baseURL: process.env.API_URL_INNEVENT,
		timeout: 10000,
		headers: {}
	};

	if (!options || !options.skipAuth) {
		try {
			const token = (await Auth.currentSession()).getIdToken().getJwtToken();
			instanceConfig.headers['Authorization'] = 'Bearer ' + token;
		} catch (error) {
			console.log('User is not logged in, TODO in instances', error);
		}
	}
	return axios.create(instanceConfig);
}

export async function apiInnOrder(options?: ApiOptions): Promise<AxiosInstance> {
	const instanceConfig: AxiosRequestConfig = {
		baseURL: process.env.API_URL_INNORDER,
		timeout: 10000,
		headers: {}
	};

	if (!options || !options.skipAuth) {
		try {
			const token = (await Auth.currentSession()).getIdToken().getJwtToken();
			instanceConfig.headers['Authorization'] = 'Bearer ' + token;
		} catch (error) {
			console.log('User is not logged in, TODO in instances', error);
		}
	}
	return axios.create(instanceConfig);
}