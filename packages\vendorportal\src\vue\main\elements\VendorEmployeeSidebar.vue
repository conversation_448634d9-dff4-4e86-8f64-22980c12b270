<template>
  <b-sidebar
    v-model="sidebarVisible"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Mitarbeiter Berechtigung bearbeiten"
    width="1000px"
    backdrop
    right
  >
    <div class="px-3 py-2">
      <div class="mb-4">
        <h2>Allgemeines</h2>
        <b-form-group
          label="Vorname"
          label-for="input-1"
        >
          <b-form-input
            id="input-1"
            v-model="vendorEmployee.firstname"
          />
        </b-form-group>
        <b-form-group
          label="Nachname"
          label-for="input-2"
        >
          <b-form-input
            id="input-2"
            v-model="vendorEmployee.lastname"
          />
        </b-form-group>
        <b-form-group
          label="Position"
          label-for="input-3"
        >
          <b-form-input
            id="input-3"
            v-model="vendorEmployee.position"
          />
        </b-form-group>
        <SaveButton
          :data="vendorEmployee"
          size="sm"
          @click="saveVendorEmployee"
        />
      </div>
      <h2 class="mb-2">
        Berechtigungen
      </h2>
      <div class="mb-4">
        <h5 class="mt-2">
          Allgemein
        </h5>
        <LoadButton
          v-for="(perm, index) in availableVendorPermissions"
          :key="perm.urn"
          size="sm"
          class="mr-1"
          icon="random"
          :text="perm.badge"
          :variant="permissionMatch(vendorEmployee, perm) ? 'primary':'outline-secondary'"
          @click="changePermission($event, vendorEmployee, perm)"
        />
        <h5 class="mt-2 mb-0">
          Salesarea
        </h5>
        <p
          id="descriptionFieldVendorEmployeeSidebar"
          class="mt-0"
        >
          Diese Einstellung gilt für alle Verkaufszonen. 
          Solltest du Personen nur auf bestimmte Verkaufszonen berechtigen wollen, dann wähle ein Event aus und klicke auf "Verkaufsbereiche"
        </p>
        <LoadButton
          v-for="(perm, index) in availableSalesareaPermissions"
          :key="perm.urn"
          size="sm"
          class="mr-1"
          icon="random"
          :text="perm.badge"
          :variant="permissionMatch(vendorEmployee, perm) ? 'primary':'outline-secondary'"
          @click="changePermission($event, vendorEmployee, perm)"
        />
      </div>
      <div style="height: 66px">
        <!-- Platzhalter, da Slider nach unten verschoben -->
      </div>
    </div>
  </b-sidebar>
</template>
<script>

import { API } from 'aws-amplify';
import LoadButton from '../../buttons/LoadButton.vue';
import SaveButton from '../../buttons/SaveButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
import Multiselect from 'vue-multiselect';
let moment = require('moment');
const uuid = require('uuid/v4');


export default {
	components: { LoadButton, DeleteButton, Multiselect, SaveButton },
	props: ['vendorEmployee', 'value', 'selectedVendorId'],
	data() { return {
		availableVendorPermissions:[
			{ value: false, urn: 'perm:innorder.vendor.owner', badge: 'VendorOwner', name: 'Vendor Eigentümer', description: 'Obereste Berechtigungsstufe eines Vendors' },
			{ value: false, urn: 'perm:innorder.vendor.admin', badge: 'VendorAdmin', name: 'Vendor Admin', description: 'Zweithöchste Berechtigungsstufe eines Vendors' },
			{ value: false, urn: 'perm:innorder.vendor.show', badge: 'VendorShow', name: 'Vendor Leser', description: 'Vendor Lesezugriff' },
			{ value: false, urn: 'perm:innorder.vendor.edit', badge: 'VendorEdit', name: 'Vendor Schreiber', description: 'Vendor Schreibzugriff' }
		],
		availableSalesareaPermissions:[
			{
				value: false,
				urn: 'perm:innorder.salesarea.use',
				badge: 'SalesAreaUse',
				name: 'SalesArea Use',
				description: 'Mitarbeiter kann alle Salesareas auswählen'
			},
			{
				value: false,
				urn: 'perm:innorder.salesarea.admin',
				badge: 'SalesAreaAdmin',
				name: 'SalesArea Admin',
				description: 'Mitarbeiter ist Administrator bei allen Salesareas'
			}
		]
	};},
	computed:{
		sidebarVisible: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			}
		}
	},
	created() {

	},
	methods:{
		permissionMatch(user, perm) {
			for (let assignedPerm of user.PermVendorUsers) {
				if (assignedPerm.permission == perm.urn) {
					user[perm.urn] = true;
					return true;
				}
			}
			user[perm.urn] = false;
			return false;
		},

		changePermission(btn, user, perm) {
			let currentlyAssigned = user[perm.urn];
			let newAssigned = !currentlyAssigned;
			if (newAssigned) { // Create Permission
				let assignedPerm = {
					id: uuid(),
					permission: perm.urn,
					VendorEmployeeId: user.id,
					VendorId: this.selectedVendorId
				};
				API.post('innorder', '/Vendor/' + this.selectedVendorId + '/Permission', { body: assignedPerm }).then(response => {
					this.$bvToast.toast('Die Berechtigung wurde erfolgreich gesetzt.', {
						title: 'Berechtigung setzen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});
					user.PermVendorUsers.push(assignedPerm);
					btn.reset();
				}).catch(error => {
					this.$bvToast.toast('Beim Setzen der Berechtigung ist ein Fehler aufgetreten...', {
						title: 'Berechtigung setzen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
			} else { // Remove Permission
				let removeID = null;
				let filtered = user.PermVendorUsers.filter(function(assignedP, index, arr) {
					if (assignedP.permission == perm.urn) {
						removeID = assignedP.id;
						return false;
					} else {
						return true;
					}
				});
				API.del('innorder', '/Vendor/Permission/' + removeID).then(response  => {
					if (response?.code == 565) {
						this.$bvToast.toast(
							'Der Besitzer kann seine eigene Berechtigung nicht löschen.',
							{
								title: 'Löschen Fehlgeschlagen',
								autoHideDelay: 10000,
								variant: 'danger'
							}
						);
					} else {
						this.$bvToast.toast('Die Berechtigung wurde erfolgreich gelöscht.', {
							title: 'Löschen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						});
						user.PermVendorUsers.splice(user.PermVendorUsers.findIndex(function(i) {
							return i.id === removeID;
						}), 1);
					}
					btn.reset();
				}).catch(error => {
					this.$bvToast.toast('Beim Löschen der Berechtigung ist ein Fehler aufgetreten. StatusCode: ' + httpStatus[error.response.status], {
						title: 'Löschen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
			}
			user[perm.urn] = newAssigned;
		},
		saveVendorEmployee(btn) {
			let item = btn.data;
			API.put('innorder', '/VendorEmployee/' + item.id, { body: item }).then(response  => {
				this.$bvToast.toast('Der VendorEmployee "' + item.firstname + '" wurde erfolgreich geändert.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
			}).catch(error => {
				this.$bvToast.toast('Beim Ändern von "' + item.firstname + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Ändern Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});

		}
	}
};
</script>

<style>
.top-fixed-header {
  padding-top: 66px;
}
#descriptionFieldVendorEmployeeSidebar{
	color: grey;
	font-size: 10px;
}
</style>
