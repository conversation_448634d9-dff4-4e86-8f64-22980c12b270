<template>
  <b-container fluid>
    <b-row>
      <b-col cols="7">
        <SalesPeriodTable />
      </b-col>
      <b-col cols="5">
        <TicketGroupTable />
      </b-col>
    </b-row>
    <TicketTable
      class="mt-5"
    />
  </b-container>
</template>
<script lang="ts">
import SalesPeriodTable from './elements/InnticketSalesPeriodTable.vue';
import TicketGroupTable from './elements/InnticketTicketGroupTable.vue';
import TicketTable from './elements/InnticketTicketTable.vue';
import { onMounted } from '@vue/composition-api';
import { defineComponent } from '@vue/composition-api';
import { refreshTicketMap } from '../../../states/ticketMap';

export default defineComponent({
	components: {
		SalesPeriodTable,
		TicketGroupTable,
		TicketTable
	},
	setup() {
		onMounted(async () => {
			await refreshTicketMap();
		});
		
		return { };
	}
	
});
</script>
