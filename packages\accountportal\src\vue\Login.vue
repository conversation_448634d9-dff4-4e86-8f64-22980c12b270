<template>
  <div
    class="d-flex justify-content-center mt-5"
  >
    <div class="innLoginForm">
      <div class="text-center mb-4">
        <img
          class="mb-4"
          src="../img/logo-transparent.png"
          alt=""
          width="150"
          height="150"
        >
        <h1 class="h4 mb-3 font-weight-normal">
          {{ title }}
        </h1>
        <b-spinner
          v-if="signUpOrsignInForm.loggedInSpinner"
          style="width: 200px; height: 200px;"
          label="Large Spinner"
          type="grow"
          variant="primary"
        />
      </div>
      <div v-if="!signUpOrsignInForm.loggedInSpinner && status == 'signUpOrsignInForm'">
        <b-form-group
          id="input-group-email"
          :label="fieldNameEmail"
          label-for="input-email"
        >
          <b-form-input
            id="input-email"
            ref="inputEmail"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            autofocus
            placeholder="Email Adresse eingeben"
            :state="validateEmail"
            :disabled="!signUpOrsignInForm.emailEnabled"
            @keyup.enter="signUpOrsignInSubmit()"
          />
          <b-form-invalid-feedback :state="validateEmail">
            Bitte gibt eine gültige Email Adresse ein
          </b-form-invalid-feedback>
        </b-form-group>
        <b-button
          block
          :disabled="!validateEmail || signUpOrsignInForm.btnLoading"
          variant="primary"
          squared
          @click="signUpOrsignInSubmit()"
        >
          <b-spinner
            v-if="signUpOrsignInForm.btnLoading"
            small
          />
          {{ signUpOrsignInForm.btnText }}
        </b-button>
        <b-button
          v-if="url.query.back_uri"
          class="mt-4 pl-0"
          variant="link"
          @click="redirectBack()"
        >
          Zurück
        </b-button>
        <b-alert
          v-if="signUpOrsignInForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ signUpOrsignInForm.errorMessage }}
        </b-alert>
        <b-alert
          v-if="signUpOrsignInForm.confirmationError"
          class="mt-3"
          show
          variant="danger"
        >
          Dein Account wurde noch nicht verifiziert.
          <b-link @click="resendConfirmationLink()">
            Bestätigungs-Email erneut senden
          </b-link>
        </b-alert>
        <b-alert
          v-if="signUpOrsignInForm.successMessage"
          class="mt-3"
          show
          variant="success"
        >
          {{ signUpOrsignInForm.successMessage }}
        </b-alert>
      </div>
      <div v-if="status == 'signInForm'">
        <b-form-group
          id="input-group-email-signin"
          :label="fieldNameEmail"
          label-for="input-email-signin"
        >
          <b-form-input
            id="input-email-signin"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            disabled
          />
          <b-form-text>
            <b-link @click="setStatus('signUpOrsignInForm')">
              Email Adresse ändern
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-form-group
          id="input-group-password"
          :label="fieldNamePassword"
          label-for="input-password"
        >
          <b-form-input
            id="input-password"
            ref="inputPassword"
            v-model="signUpOrsignInForm.password"
            squared
            type="password"
            placeholder="Passwort eingeben"
            :disabled="!signUpOrsignInForm.passwordEnabled"
            @keyup.enter="signInSubmit()"
          />
          <b-form-text>
            <b-link @click="setStatus('forgotPasswordForm')">
              Passwort vergessen
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-button
          block
          :disabled="signUpOrsignInForm.password == '' || signInForm.btnLoading"
          variant="primary"
          squared
          @click="signInSubmit()"
        >
          <b-spinner
            v-if="signInForm.btnLoading"
            small
          />
          {{ signInForm.btnText }}
        </b-button>
        <b-alert
          v-if="signInForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ signInForm.errorMessage }}
        </b-alert>
      </div>
      <div v-if="status == 'signUpForm'">
        <b-form-group
          id="input-group-email-signup"
          :label="fieldNameEmail"
          label-for="input-email-signup"
        >
          <b-form-input
            id="input-email-signup"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            disabled
          />
          <b-form-text>
            <b-link @click="setStatus('signUpOrsignInForm')">
              Email Adresse ändern
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-form-group
          id="input-group-givenName"
          :label="signUpForm.fieldNameGivenName"
          label-for="input-givenName"
        >
          <b-form-input
            id="input-givenName"
            ref="inputGivenName"
            v-model="signUpForm.givenName"
            squared
            type="text"
            :state="validateGivenName"
          />
          <b-form-invalid-feedback :state="validateGivenName">
            Zu Kurz
          </b-form-invalid-feedback>
        </b-form-group>
        <b-form-group
          id="input-group-familyName"
          :label="signUpForm.fieldNameFamilyName"
          label-for="input-familyName"
        >
          <b-form-input
            id="input-familyName"
            v-model="signUpForm.familyName"
            squared
            type="text"
            :state="validateFamilyName"
          />
          <b-form-invalid-feedback :state="validateFamilyName">
            Zu Kurz
          </b-form-invalid-feedback>
        </b-form-group>
        <b-form-group
          id="input-group-password-signup"
          :label="fieldNamePassword"
          label-for="input-password-signup"
        >
          <b-form-input
            id="input-password-signup"
            ref="inputPasswordSignUp"
            v-model="signUpOrsignInForm.password"
            squared
            type="password"
            :state="validatePassword"
            placeholder="Passwort eingeben"
            :disabled="!signUpOrsignInForm.passwordEnabled"
            @keyup.enter="signUpSubmit()"
          />
          <template :state="validatePassword">
            <b-form-invalid-feedback
              v-for="err in signUpForm.passwordErrors"
              :key="err"
            >
              {{ err }}
            </b-form-invalid-feedback>
          </template>
        </b-form-group>
        <b-button
          block
          :disabled="(!validatePassword || !validateFamilyName || !validateGivenName) || signUpForm.btnLoading"
          variant="primary"
          squared
          @click="signUpSubmit()"
        >
          <b-spinner
            v-if="signUpForm.btnLoading"
            small
          />
          {{ signUpForm.btnText }}
        </b-button>
        <b-alert
          v-if="signUpForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ signUpForm.errorMessage }}
        </b-alert>
        <b-alert
          v-if="signUpForm.successMessage"
          class="mt-3"
          show
          variant="success"
        >
          {{ signUpForm.successMessage }}
        </b-alert>
      </div>
      <div v-if="status == 'newPasswordForm'">
        <b-form-group
          id="input-group-email-signup"
          :label="fieldNameEmail"
          label-for="input-email-signup"
        >
          <b-form-input
            id="input-email-signup"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            disabled
          />
          <b-form-text>
            <b-link @click="setStatus('signUpOrsignInForm')">
              Email Adresse ändern
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-alert
          show
          variant="primary"
        >
          Unsere Passwortrichtlinien erfordern, dass ein neues Passwort gesetzt werden muss. <br>Bitte gib ein neues sicheres Passwort ein.
        </b-alert>
        <b-form-group
          id="input-group-password-new"
          :label="newPasswordForm.fieldNameNewPassword"
          label-for="input-password-new"
        >
          <b-form-input
            id="input-password-new"
            ref="inputPasswordNew"
            v-model="newPasswordForm.password"
            squared
            type="password"
            :state="validateNewPassword"
            placeholder="Neues Passwort eingeben"
            @keyup.enter="newPasswordSubmit()"
          />
          <template :state="validateNewPassword">
            <b-form-invalid-feedback
              v-for="err in newPasswordForm.passwordErrors"
              :key="err"
            >
              {{ err }}
            </b-form-invalid-feedback>
          </template>
        </b-form-group>
        <b-button
          block
          :disabled="(!validateNewPassword) || newPasswordForm.btnLoading"
          variant="primary"
          squared
          @click="newPasswordSubmit()"
        >
          <b-spinner
            v-if="newPasswordForm.btnLoading"
            small
          />
          {{ newPasswordForm.btnText }}
        </b-button>
        <b-alert
          v-if="newPasswordForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ newPasswordForm.errorMessage }}
        </b-alert>
      </div>
      <div v-if="status == 'forgotPasswordForm'">
        <b-form-group
          id="input-group-email-signup"
          :label="fieldNameEmail"
          label-for="input-email-signup"
        >
          <b-form-input
            id="input-email-signup"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            disabled
          />
          <b-form-text>
            <b-link @click="setStatus('signUpOrsignInForm')">
              Email Adresse ändern
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-alert
          show
          variant="primary"
        >
          Wir senden dir einen Link an deine Email-Adresse zu.<br>Mit diesem kannst du dein Passwort neu setzen.
        </b-alert>
        <b-button
          ref="forgotPasswordFormBtn"
          block
          :disabled="forgotPasswordForm.btnLoading"
          variant="primary"
          squared
          @click="forgotPasswordSubmit()"
        >
          <b-spinner
            v-if="forgotPasswordForm.btnLoading"
            small
          />
          {{ forgotPasswordForm.btnText }}
        </b-button>
        <b-alert
          v-if="forgotPasswordForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ forgotPasswordForm.errorMessage }}
        </b-alert>
        <b-alert
          v-if="forgotPasswordForm.successMessage"
          class="mt-3"
          show
          variant="success"
        >
          {{ forgotPasswordForm.successMessage }}
        </b-alert>
      </div>
      <div v-if="status == 'softwareTokenForm'">
        <b-form-group
          id="input-group-email-signin"
          :label="fieldNameEmail"
          label-for="input-email-signin"
        >
          <b-form-input
            id="input-email-signin"
            v-model="signUpOrsignInForm.email"
            squared
            type="email"
            disabled
          />
          <b-form-text>
            <b-link @click="setStatus('signUpOrsignInForm')">
              Email Adresse ändern
            </b-link>
          </b-form-text>
        </b-form-group>
        <b-alert
          show
          variant="primary"
        >
          Die Zwei-Faktor-Authentifizierung ist aktiviert.<br>Bitte gib einen Code aus deiner Authenticator-App ein.
        </b-alert>
        <b-form-group
          id="input-group-token"
          :label="softwareTokenForm.fieldNameToken"
          label-for="input-token"
        >
          <b-form-input
            id="input-token"
            ref="inputToken"
            v-model="softwareTokenForm.token"
            squared
            type="text"
            placeholder="Code eingeben"
            @keyup.enter="softwareTokenSubmit()"
          />
        </b-form-group>
        <b-button
          block
          :disabled="softwareTokenForm.btnLoading"
          variant="primary"
          squared
          @click="softwareTokenSubmit()"
        >
          <b-spinner
            v-if="softwareTokenForm.btnLoading"
            small
          />
          {{ softwareTokenForm.btnText }}
        </b-button>
        <b-alert
          v-if="softwareTokenForm.errorMessage"
          class="mt-3"
          show
          variant="danger"
        >
          {{ softwareTokenForm.errorMessage }}
        </b-alert>
      </div>
      <b-alert
        v-model="isInvitation"
        class="mt-5"
      >
        Melde dich zuerst an, bevor du die Einladung akzeptieren kannst!
      </b-alert>
    </div>
  </div>
</template>
<script>

import validator from 'validator';
import { Auth } from 'aws-amplify';
import PasswordValidator from 'password-validator';
import URL from 'url-parse';
import * as api from '@innevent/webapp-api';

export default {
	data() { return {
		url: null,
		status: 'signUpOrsignInForm',
		title: 'Anmelden oder Registrieren',
		fieldNameEmail: 'Email Adresse',
		fieldNamePassword: 'Passwort',
		btnTextSignIn: 'Anmelden',
		cognitoUser: null,
		signUpOrsignInForm: {
			emailEnabled: true,
			btnLoading: false,
			btnText: 'Los',
			email: '',
			errorMessage: null,
			password: '',
			passwordEnabled: true,
			confirmationError: false,
			loggedInSpinner: false,
			successMessage: null
		},
		signInForm: {
			btnLoading: false,
			btnText: 'Anmelden',
			errorMessage: null
		},
		signUpForm: {
			btnLoading: false,
			btnText: 'Account erstellen',
			givenName: '',
			familyName: '',
			fieldNameGivenName: 'Vorname',
			fieldNameFamilyName: 'Nachname',
			errorMessage: null,
			successMessage: null,
			passwordErrors: []
		},
		newPasswordForm: {
			btnLoading: false,
			btnText: 'Passwort Ändern',
			errorMessage: null,
			password: '',
			passwordErrors: [],
			fieldNameNewPassword: 'Neues Passwort'
		},
		forgotPasswordForm: {
			btnLoading: false,
			btnText: 'Link senden',
			errorMessage: null,
			successMessage: null
		},
		softwareTokenForm: {
			btnLoading: false,
			btnText: 'Bestätigen',
			errorMessage: null,
			successMessage: null,
			fieldNameToken: 'Einmal-Passwort',
			token: ''
		}
	};},
	computed: {
		isInvitation() {
			let red_url = this.$route.query.redirect_uri;
			let conditions = ['vendor-invitation', 'vendor-staff-invitation', 'employee-invitation'];

			if (red_url && conditions.some(el => red_url.includes(el))) {
				return true;
			}
			return false;
		},
		validateEmail() {
			if (this.signUpOrsignInForm.email == '') return null;
			else if (validator.isEmail(this.signUpOrsignInForm.email)) return true;
			else return false;
		},
		validateGivenName() {
			if (this.signUpForm.givenName.length > 1) return true;
			else return false;
		},
		validateFamilyName() {
			if (this.signUpForm.familyName.length > 1) return true;
			else return false;
		},
		validatePassword() {
			let schema = new PasswordValidator();
			schema
				.is().min(8)
				.is().max(50)
				.has().uppercase()
				.has().lowercase()
				.has().digits()
				.has().not().spaces()
				.is().not().oneOf([this.signUpOrsignInForm.email, this.signUpForm.givenName, this.signUpForm.familyName]);

			let errors = schema.validate(this.signUpOrsignInForm.password, { list: true });
			this.signUpForm.passwordErrors = errors.map(error => {
				switch (error) {
				case 'min': return 'mind. 8 Zeichen';
				case 'uppercase': return 'mind. 1 Großbuchstabe';
				case 'lowercase': return 'mind. 1 Kleinbuchstabe';
				case 'digits': return 'mind. eine Zahl';
				case 'spaces': return 'keine Leerzeichen';
				case 'oneOf': return 'darf nicht Vorname, Nachname oder Email enthalten';
				}
			});
			if (errors.length > 0) return false;
			return true;
		},
		validateNewPassword() {
			let schema = new PasswordValidator();
			schema
				.is().min(8)
				.is().max(50)
				.has().uppercase()
				.has().lowercase()
				.has().digits()
				.has().not().spaces();

			let errors = schema.validate(this.newPasswordForm.password, { list: true });
			this.newPasswordForm.passwordErrors = errors.map(error => { switch (error) {
			case 'min': return 'mind. 8 Zeichen'; break;
			case 'uppercase': return 'mind. 1 Großbuchstabe'; break;
			case 'lowercase': return 'mind. 1 Kleinbuchstabe'; break;
			case 'digits': return 'mind. eine Zahl'; break;
			case 'spaces': return 'keine Leerzeichen'; break;
			case 'oneOf': return 'darf nicht Vorname, Nachname oder Email enthalten'; break;
			}});
			if (errors.length > 0) return false;
			return true;
		}
	},
	async created() {
		this.url = new URL(window.location.href, true);
		await Auth.currentAuthenticatedUser().then(user => {
			if (this.url.query.redirect_uri) this.signUpOrsignInForm.loggedInSpinner = true;
			this.cognitoUser = user;
			console.log('User ' + user.username + ' already logged in');
			this.success();
		}).catch(err => {
			console.log('User Unauthorized');
			if (this.url.query.email) this.signUpOrsignInForm.email = this.url.query.email;
		});
	},
	methods: {
		redirectBack() {
			let parsedUrl = new URL(this.url.query.back_uri);
			window.location.replace(parsedUrl.toString());
		},
		async softwareTokenSubmit() {
			this.softwareTokenForm.btnLoading = true;
			try {
				if (this.softwareTokenForm.token.length < 4) throw { code: 'CodeMismatchException' };
				let cognitoUser = await Auth.confirmSignIn(this.cognitoUser, this.softwareTokenForm.token, 'SOFTWARE_TOKEN_MFA');
				this.cognitoUser = cognitoUser;
				this.success();
			} catch (err) {
				if (err.code == 'CodeMismatchException') this.softwareTokenForm.errorMessage = 'Das Einmal-Passwort ist ungültig';
				else console.log(err);
			}
			this.softwareTokenForm.btnLoading = false;
		},
		async resendConfirmationLink() {
			try {
				await Auth.resendSignUp(this.signUpOrsignInForm.email);
				this.signUpOrsignInForm.successMessage = 'Wir haben dir eine Bestätigungsmail gesendet. Bitte überprüfe dein Postfach.';
			} catch (err) {
				this.signUpOrsignInForm.errorMessage = 'Leider ist ein Fehler aufgetreten. Bitte lade die Seite neu.';
			}
		},
		setStatus(status) {
			this.status = status;
			if (this.status == 'signInForm') {
				this.$nextTick(() => this.$refs.inputPassword.focus());
				this.title = 'Anmelden';
			}
			else if (this.status == 'signUpForm') {
				this.$nextTick(() => this.$refs.inputGivenName.focus());
				this.title = 'Account erstellen';
			}
			else if (this.status == 'signUpOrsignInForm') {
				this.$nextTick(() => this.$refs.inputEmail.focus());
				this.title = 'Anmelden oder Registrieren';
			}
			else if (this.status == 'newPasswordForm') {
				this.$nextTick(() => this.$refs.inputPasswordNew.focus());
				this.title = 'Neues Passwort setzen';
			}
			else if (this.status == 'forgotPasswordForm') {
				this.$nextTick(() => this.$refs.forgotPasswordFormBtn.focus());
				this.title = 'Passwort zurücksetzen';
			}
			else if (this.status == 'softwareTokenForm') {
				this.$nextTick(() => this.$refs.inputToken.focus());
				this.title = 'Anmelden';
			}
		},
		async signUpOrsignInSubmit() {
			if (this.validateEmail) {
				this.signUpOrsignInForm.emailEnabled = false;
				this.signUpOrsignInForm.btnLoading = true;
				try {
					await Auth.signIn(this.signUpOrsignInForm.email, 'dummy444Password777');
					console.log('login okay');
				} catch (error) {
					console.log('login failed');
					console.log(error);
					if (error.code == 'UserNotFoundException') {
						this.setStatus('signUpForm');
					}
					else if (error.code == 'NotAuthorizedException') {
						this.setStatus('signInForm');
					}
					else if (error.code == 'UserNotConfirmedException') {
						this.signUpOrsignInForm.confirmationError = true;
					}
					else if (error.code == 'PasswordResetRequiredException') {
						this.setStatus('signInForm');
						this.signInForm.errorMessage = 'Das Passwort muss aus sicherheitstechnischen Gründen neu gesetzt werden. ' +
              'Bitte wählen Sie Passwort vergessen.';
					}
					else this.signUpOrsignInForm.errorMessage = 'Unbekannter Fehler';
				}
				this.signUpOrsignInForm.emailEnabled = true;
				this.signUpOrsignInForm.btnLoading = false;
			}
		},
		async signInSubmit() {
			if (this.signUpOrsignInForm.password.length > 0) {
				this.signUpOrsignInForm.passwordEnabled = false;
				this.signInForm.btnLoading = true;
				try {
					let cognitoUser = await Auth.signIn(this.signUpOrsignInForm.email, this.signUpOrsignInForm.password);
					this.cognitoUser = cognitoUser;
					if (cognitoUser.challengeName == 'NEW_PASSWORD_REQUIRED') {
						//const { requiredAttributes } = cognitoUser.challengeParam;
						// the array of required attributes, e.g ['email', 'phone_number']
						// You need to get the new password and required attributes from the UI inputs
						// and then trigger the following function with a button click
						// For example, the email and phone_number are required attributes
						this.setStatus('newPasswordForm');
					} else if (cognitoUser.challengeName == 'SOFTWARE_TOKEN_MFA') {
						this.setStatus('softwareTokenForm');
					} else if (cognitoUser.challengeName == 'MFA_SETUP') {
						this.signInForm.errorMessage = 'MFA_SETUP';

					} else if (cognitoUser.challengeName) {
						this.signInForm.errorMessage = 'Unbekannte Challenge';
					} else {
						this.success();
					}
				} catch (error) {
					console.log('error logged in');
					console.log(error);
					if (error.code == 'UserNotFoundException') this.setStatus('signUpForm');
					else if (error.code == 'NotAuthorizedException') {
						if (error.message == 'User is disabled.') this.signInForm.errorMessage = 'Account ist gesperrt';
						else this.signInForm.errorMessage = 'Email Adresse oder Passwort falsch';
					}
					else if (error.code == 'PasswordResetRequiredException') this.setStatus('newPasswordForm');
					else this.signInForm.errorMessage = 'Unbekannter Fehler';
				}
				this.signUpOrsignInForm.passwordEnabled = true;
				this.signInForm.btnLoading = false;
			}
		},
		async newPasswordSubmit() {
			if (this.validateNewPassword) {
				this.newPasswordForm.btnLoading = true;
				try {
					const loggedUser = await Auth.completeNewPassword(
						this.cognitoUser,
						this.newPasswordForm.password
						// OPTIONAL, the required attributes
						// {
						//     email,
						//     phone_number,
						// }
					);
					console.log(loggedUser);
					this.success();
				} catch (error) {
					// console.log('error logged in')
					console.log(error);
					// if(error.code == 'UserNotFoundException') this.setStatus('signUpForm')
					// else if(error.code == 'NotAuthorizedException') {
					//   if(error.message == 'User is disabled.') this.signInForm.errorMessage = 'Account ist gesperrt'
					//   else this.signInForm.errorMessage = 'Email Adresse oder Passwort falsch'
					// }
					// else this.signInForm.errorMessage = 'Unbekannter Fehler'
				}
				this.newPasswordForm.btnLoading = false;
			}
		},
		async forgotPasswordSubmit() {
			this.forgotPasswordForm.btnLoading = true;
			try {
				await Auth.forgotPassword(this.signUpOrsignInForm.email);
				console.log('send forgot pw');
				this.forgotPasswordForm.successMessage = 'Email erfolgreich versendet. Bitte überprüfe dein Postfach.';
			} catch (error) {
				// console.log('error logged in')
				console.log(error);
				// if(error.code == 'UserNotFoundException') this.setStatus('signUpForm')
				// else if(error.code == 'NotAuthorizedException') {
				//   if(error.message == 'User is disabled.') this.signInForm.errorMessage = 'Account ist gesperrt'
				//   else this.signInForm.errorMessage = 'Email Adresse oder Passwort falsch'
				// }
				// else this.signInForm.errorMessage = 'Unbekannter Fehler'
			}
			this.forgotPasswordForm.btnLoading = false;
			this.forgotPasswordForm.btnText = 'Link erneut senden';
		},
		async signUpSubmit() {
			if ((!this.validatePassword || !this.validateFamilyName || !this.validateGivenName) || this.signUpForm.btnLoading) return;
			this.signUpForm.btnLoading = true;
			try {
				const cognitoUser = await Auth.signUp({
					username: this.signUpOrsignInForm.email,
					password: this.signUpOrsignInForm.password,
					attributes: {
						given_name: this.signUpForm.givenName,
						family_name: this.signUpForm.familyName
					}
				});
				console.log({ cognitoUser });
				this.signUpForm.errorMessage = null;
				this.signUpForm.successMessage = 'Wir haben dir einen Bestätigungslink an ' + this.signUpOrsignInForm.email + 
          ' gesendet. Bitte überprüfe deine Emails und schließe die Registrierung mit einem Klick auf den Link ab.';
			} catch (error) {
				if (error.code == 'UsernameExistsException') {
					this.signUpForm.errorMessage = 'Es existiert bereits ein Account mit dieser E-Mail Adresse.';
				} else {
					this.signUpForm.errorMessage = 'Unbekannter Fehler';
				}
				console.log('error signing up:', error);
			}
			this.signUpForm.btnLoading = false;
		},
		async success() {
			console.log('success');
			if (this.url.query.redirect_uri && this.url.query.redirect_uri.includes('accounts.inn.systems')) {
				console.log('stay on account portal');
				window.location.replace(this.url.query.redirect_uri);
			}
			else if (this.url.query.redirect_uri) { // Zurück leiten
				console.log('redirect back');
				try {
					let session = await Auth.currentSession();
					let postObject = {
						idToken: session.idToken.jwtToken,
						accessToken: session.accessToken.jwtToken,
						refreshToken: session.refreshToken.token,
						username: Auth.user.username
					};
					const response = await api.createAuthCache({
						data: postObject,
						fullResponse: true
					});

					let parsedUrl = new URL(this.url.query.redirect_uri, true);
					parsedUrl.query.code = response.headers.createdobjectid;
					window.location.replace(parsedUrl.toString());
				}
				catch (error) {
					console.log('Redirect or Create AuthCache failed');
					console.log(error);
				}
			}
			else { // Account Seite anzeigen
				this.$router.push('/account');
			}
		}
	}
};
</script>

