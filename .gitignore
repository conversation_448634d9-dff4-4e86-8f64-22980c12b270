#root level
/node_modules/
/package-lock.json
/yarn.lock
/.vscode
/.yarn


#packages
packages/*/build
packages/*/dist
packages/*/node_modules
packages/webapp-*/**/*.js
packages/**/*.d.ts.map


#amplify
amplify/\#current-cloud-backend
amplify/.config/local-*
amplify/mock-data
amplify/backend/amplify-meta.json
amplify/backend/awscloudformation
aws-exports.js
awsconfiguration.json
amplifyconfiguration.json
amplify-build-config.json
amplify-gradle-config.json
amplifyxc.config