import type { ShortEmployee, ShortRedemptionGroup } from '.';

export type InnEventTransaction = {
    transactionId: string;
    transactionType: InnEventTransactionType;
    eventId: string;
    createdOn: string;
    createdIn: 'CUSTOMERPORTAL' | 'EMPLOYEEAPP' | 'ORGANIZERPORTAL';
    connectionState: 'ONLINE' | 'OFFLINE';
    customerVisible: boolean;
    pushedAt: string | null;
    innorderData?: TransactionDataInnOrder;
    innticketData?: TransactionDataInnTicket;
    innaccessData?: TransactionDataInnAccess;
}

export type InnOrderShortTransactionType = 'TOPUP' | 'TOPDOWN' | 'ORDER' | 'CASHLESSORDER' | 'REPAIR';
export type InnOrderTransactionType = 'INNORDER_TOPUP' | 'INNORDER_TOPDOWN' | 'INNORDER_ORDER' | 'INNORDER_CASHLESSORDER' | 'INNORDER_REPAIR';
export type InnTicketTransactionType = 'INNTICKET_REDEEM' | 'INNTICKET_REMOVE_REDEMPTION' | 'INNTICKET_REDEEM_CONNECT';
export type InnAccessTransactionType = 'INNACCESS_CHECK' | 'INNACCESS_IDENTIFIER_UPDATE';
export type InnEventTransactionType = InnOrderTransactionType | InnTicketTransactionType | InnAccessTransactionType;

export type TransactionDataInnOrder = {
    status: InnOrderTransactionStatus;
    creditBefore: string;
    creditChange: string;
    creditAfter: string;
    paymentMethod: InnOrderPaymentMethod;
    type: InnOrderShortTransactionType;
    // TODO
}
export type TransactionDataInnTicket = {
    eventEmployee: ShortEmployee;
    redemptionGroup: ShortRedemptionGroup;
    generatedTicketIds: string[];
    identifierTag: {
        identifierUid: string;
    };
}
export type InnAccessCheckResult = 'PASSED' | 'REJECTED';
export type InnAccessCheckData = {
    accessAreaId: string;
    checkResult: InnAccessCheckResult;
    availableAreas: string[]; // Array of uuids
}
export type InnAccessIdentifierUpdateData = {
    addedAreas: string[]; // Array of uuids
    removedArea: string[]; // Array of uuids
}
export type TransactionDataInnAccess = {
    eventEmployee: ShortEmployee;
    identifierTag: {
        identifierUid: string;
    };
    checkData?: InnAccessCheckData;
    identifierUpdateData?: InnAccessIdentifierUpdateData;
}

export type InnOrderTransactionStatus = 'OPEN' |
    'SUCCESS_IDENTIFIER_READ' |
    'CANCEL_IDENTIFIER_INVALID' |
    'SUCCESS_IDENTIFIER_OK' |
    'WAIT_FOR_AWS_RESPONSE' |
    'SUCCESS_AWS_VERIFY' |
    'SUCCESS_AWS_RESPONSE' |
    'ERROR_AWS_RESPONSE' |
    'CANCEL_NOT_ENOUGH_CREDIT' |
    'SUCCESS_ENOUGH_CREDIT' |
    'SUCCESS_CALC_TRANS' |
    'ERROR_CALC_1' |
    'ERROR_CALC_2' |
    'WAIT_ENCRYPT_NEW_DATA' |
    'ERROR_ENCRYPT' |
    'WAIT_WRITE_IDENTIFIER' |
    'WAIT_WRITE_IDENTIFIER_RETRY' |
    'SUCCESS_WRITE_IDENTIFIER' |
    'CANCEL_WRITE_FAILED' |
    'SUCCESS_ADJUST_CREDIT';

export type InnOrderPaymentMethod = 'CASH' | 'CARD' | 'INNCASHLESS' | 'CORRECTION' | 'VOUCHER';