<template>
  <b-sidebar
    v-model="sidebarVisibleRef"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Mitarbeiter Berechtigung bearbeiten"
    width="1000px"
    backdrop
    right
  >
    <template v-if="eventEmployeeRef">
      <div class="px-3 py-2">
        <div class="mb-4">
          <h2>Allgemeines</h2>
          <b-form-group
            label="Vorname"
            label-for="input-1"
          >
            <b-form-input
              id="input-1"
              v-model="eventEmployeeRef.firstName"
              @keyup.enter="onKeyupEnter()"
            />
          </b-form-group>
          <b-form-group
            label="Nachname"
            label-for="input-2"
          >
            <b-form-input
              id="input-2"
              v-model="eventEmployeeRef.lastName"
              @keyup.enter="onKeyupEnter()"
            />
          </b-form-group>
          <b-form-group
            label="Position"
            label-for="input-3"
          >
            <b-form-input
              id="input-3"
              v-model="eventEmployeeRef.position"
              @keyup.enter="onKeyupEnter()"
            />
          </b-form-group>
          <LoadButton
            ref="saveButtonRef"
            variant="primary"
            @click="clickOnSave"
          />
        </div>
        <h2 class="mb-2">
          Berechtigungen
        </h2>
        <div class="mb-4">
          <h4>Allgemein</h4>
          <LoadButton
            v-for="perm in allPermissions.availableEventPermissions"
            :key="perm.urn"
            size="sm"
            class="mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(perm.urn)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, perm.urn)"
          />
        </div>
        <div class="mb-4">
          <h4>Berechtigungen auf INN//ORDER</h4>
          <h5>Allgemein</h5>
          <LoadButton
            v-for="perm in allPermissions.availableOrderPermissions"
            :key="perm.urn"
            size="sm"
            class="mb-1 mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(perm.urn)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, perm.urn)"
          />
          <h5 class="mt-2 mb-0">
            Tokenstation
          </h5>
          <p
            id="descriptionFieldEmployeeSidebar"
            class="mt-0"
          >
            Diese Einstellung gilt für alle Tokenstations.
            <router-link :to="{ name: 'eTokenstation' }">
              Hier
            </router-link>
            kannst du auf einzelne Tokenstations Berechtigungen vergeben.
          </p>
          <LoadButton
            v-for="perm in allPermissions.availableTokenstationPermissions"
            :key="perm.urn"
            size="sm"
            class="mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(perm.urn)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, perm.urn)"
          />
          <!-- <h5>Salesarea</h5>
          <LoadButton
            v-for="(perm, index) in availableSalesAreaPermissions"
            :key="perm.urn"
            size="sm"
            class="mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(eventEmployeeRef, perm)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, eventEmployeeRef, perm)"
          /> -->
        </div>
        <div class="mb-4">
          <h4>Berechtigungen auf INN//TICKET</h4>
          <h5>Entwertungsgruppen</h5>
          <LoadButton
            v-for="perm in allPermissions.availableRedemptionGroupPermissions"
            :key="perm.urn"
            size="sm"
            class="mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(perm.urn)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, perm.urn)"
          />
        </div>
        <div class="mb-4">
          <h4>Berechtigungen auf INN//ACCESS</h4>
          <h5>Allgemein</h5>
          <LoadButton
            v-for="perm in allPermissions.availableAccessPermissions"
            :key="perm.urn"
            size="sm"
            class="mb-1 mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(perm.urn)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, perm.urn)"
          />
          <!-- <h5>Accessstations</h5>
          <LoadButton
            v-for="(perm, index) in availableAccessStationPermissions"
            :key="perm.urn"
            size="sm"
            class="mr-1"
            icon="random"
            :text="perm.badge"
            :variant="
              permissionMatch(eventEmployeeRef, perm)
                ? 'primary'
                : 'outline-secondary'
            "
            @click="changePermission($event, eventEmployeeRef, perm)"
          /> -->
        </div>
        <div style="height: 66px">
          <!-- Platzhalter, da Slider nach unten verschoben -->
        </div>
      </div>
    </template>
  </b-sidebar>
</template>
<script lang="ts">

import LoadButton from '../../../buttons/LoadButton.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import { addPermissions, deletePermissions, updateEventEmployee } from '../../../../states/employeeState';
import Vue from 'vue';
import * as allPermissions from '../../../../conf/permissions';
import type { EventEmployee, PermissionInnEvent } from '@innevent/types';
import type { ModalAction, ResetButton } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarVisibleRef = ref<boolean>(false);
		const eventEmployeeRef = ref<EventEmployee>();
		const saveButtonRef = ref<InstanceType<typeof LoadButton>>();
		const instance = getCurrentInstance();

		async function onKeyupEnter() {
			saveButtonRef.value?.clickButton();
		}

		function openForEdit(eventEmployee: EventEmployee) {
			actionRef.value = 'edit';
			sidebarVisibleRef.value = true;
			eventEmployeeRef.value = JSON.parse(JSON.stringify(eventEmployee));
		}

		function permissionMatch(perm: PermissionInnEvent) {
			return Object.keys(eventEmployeeRef.value!.permissions).some(permission => permission === perm);
		}

		async function clickOnSave(btn: ResetButton) {
			try {
				await updateEventEmployee({
					eventId: eventEmployeeRef.value!.eventId,
					userSubject: eventEmployeeRef.value!.userSubject,
					data: eventEmployeeRef.value!
				});
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();			
		}

		async function changePermission(btn: ResetButton, perm: string) {
			const eventEmployee = eventEmployeeRef.value!;
			const isAlreadyAssigned = Boolean(eventEmployee.permissions[perm]);
			try {
				if (!isAlreadyAssigned) {//Add Permission for EventEmployee
					await addPermissions(eventEmployee.eventId, eventEmployee.userSubject, { [perm]: true });
					Vue.set(eventEmployeeRef.value!.permissions, perm, true);
				} else {
					await deletePermissions(eventEmployee.eventId, eventEmployee.userSubject, { [perm]: true });
					Vue.delete(eventEmployeeRef.value!.permissions, perm);
				}
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			allPermissions,
			sidebarVisibleRef,
			eventEmployeeRef,
			permissionMatch,
			clickOnSave,
			changePermission,
			openForEdit,
			onKeyupEnter,
			saveButtonRef
		};
	}
});
</script>

<style>
.top-fixed-header {
  padding-top: 66px;
}

#descriptionFieldEmployeeSidebar {
  color: grey;
  font-size: 10px;
}
</style>