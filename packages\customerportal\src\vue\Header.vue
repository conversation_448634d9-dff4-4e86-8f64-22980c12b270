<template>
  <b-navbar
    toggleable="xl"
    type="dark"
    variant="dark"
    sticky
  >
    <b-container
      fluid
      :class="containerClass"
    >
      <b-navbar-brand to="/">
        <img
          height="40"
          src="../img/inn_systems-white.png"
        >{{ headerText }}
      </b-navbar-brand>

      <!-- <b-navbar-toggle target="nav-collapse" />

      <b-collapse
        id="nav-collapse"
        is-nav
      >
        <b-navbar-nav
          class="ml-auto"
          pills
        >
          <b-nav-item-dropdown
            v-if="cognitoUser"
            id="myAccountDropdown"
            right
          >
            <template #button-content>
              <FontAwesomeIcon
                icon="user-circle"
                fixed-width
              /><span class="ml-1 mr-1">{{ btnAccountText }}</span>
            </template>
            <b-dropdown-text class="text-center">
              {{ accountName }}
            </b-dropdown-text>
            <b-dropdown-text>{{ accountEmail }}</b-dropdown-text>
            <b-dropdown-divider />
            <b-dropdown-item :href="URL_INNLOGIN + '/account'">
              <FontAwesomeIcon
                icon="user-cog"
                fixed-width
              /><span class="ml-3">Kontoeinstellungen</span>
            </b-dropdown-item>
            <b-dropdown-item @click="logout()">
              <FontAwesomeIcon
                icon="power-off"
                fixed-width
              /><span class="ml-3">Abmelden</span>
            </b-dropdown-item>
          </b-nav-item-dropdown>
          <b-nav-form v-else>
            <b-button
              variant="outline-light"
              @click="signIn"
            >
              <FontAwesomeIcon
                icon="sign-in-alt"
                fixed-width
                class="mr-2"
              />{{ btnSignInText }}
            </b-button>
            <b-button
              variant="light"
              class="ml-3"
              @click="signUp"
            >
              <FontAwesomeIcon
                icon="user-plus"
                fixed-width
                class="mr-2"
              />{{ btnSignUpText }}
            </b-button>
          </b-nav-form>
        </b-navbar-nav>
      </b-collapse> -->
    </b-container>
  </b-navbar>
</template>
<script>

import { Auth } from 'aws-amplify';
import URL from 'url-parse';

export default {
	components: { },
	props: {
		containerClass: { type: String, default: 'max-w1600' }
	},
	data() { return {
		btnAccountText: 'Mein Konto',
		btnSignInText: 'Anmelden',
		btnSignUpText: 'Registrieren',
		cognitoUser: null,
		URL_VENDORPORTAL: process.env.URL_VENDORPORTAL,
		URL_ORGANIZERPORTAL: process.env.URL_ORGANIZERPORTAL,
		URL_CUSTOMERPORTAL: process.env.URL_CUSTOMERPORTAL,
		URL_INNLOGIN: process.env.URL_INNLOGIN
	};},
	computed: {
		headerText() {
			return ` Portal${ process.env.STAGE == 'prod' ? '' : '__' + process.env.STAGE }`;
		},
		accountEmail() {
			if (!this.cognitoUser || !this.cognitoUser.attributes) return '';
			return this.cognitoUser.attributes.email;
		},
		accountName() {
			let u = this.cognitoUser;
			if (!u || !u.attributes) { return ''; }
			if (u.attributes.given_name && u.attributes.family_name) {
				return u.attributes.given_name + ' ' + u.attributes.family_name;
			} else {
				return u.username;
			}
		}
	},
	async created() {
		this.cognitoUser = await Auth.currentAuthenticatedUser().catch(err => {});
	},
	methods: {
		async logout() {
			await Auth.signOut();
			window.location.href = process.env.URL_INNLOGIN + '/logout';
		},
		signIn() {
			let loginUrl = URL(process.env.URL_INNLOGIN, true);
			loginUrl.pathname = '/login';
			loginUrl.query.redirect_uri = window.location.href;
			loginUrl.query.back_uri = window.location.href;
			window.location.replace(loginUrl.toString());
		},
		signUp() {
			let loginUrl = URL(process.env.URL_INNLOGIN, true);
			loginUrl.pathname = '/register';
			loginUrl.query.redirect_uri = window.location.href;
			loginUrl.query.back_uri = window.location.href;
			window.location.replace(loginUrl.toString());
		}
	}
};
</script>
