import type { RedemptionGroupMap } from '@innevent/webapp-types';
import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';

const isLoadingRef = ref<boolean>(false);
const redemptionGroupMapRef = ref<RedemptionGroupMap>({ 
	redemptionGroups: []
});
    
export function useRedemptionGroupMap() {
	return {
		redemptionGroupMapRef,
		isLoadingRef
	};
}

export async function refreshRedemptionGroups(eventId: string): Promise<void> {
	isLoadingRef.value = true;

	redemptionGroupMapRef.value.redemptionGroups = await api.getRedemptionGroups({
		key: { eventId }
	});
	isLoadingRef.value = false;
}