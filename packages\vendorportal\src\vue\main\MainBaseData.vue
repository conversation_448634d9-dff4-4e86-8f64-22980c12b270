<template>
  <div class="max-w600">
    <h2>Stammdaten</h2>
    <h6>Organisation</h6>
    <div class="form-label-group">
      <b-form-group label="Geschäftsform">
        <b-form-select
          v-model="vendor.organizationType"
          :options="organizationTypes"
        />
      </b-form-group>
    </div>
    <div
      v-if="vendor.organizationType == 'PERSON'"
      class="form-row"
    >
      <div class="form-label-group col-6">
        <input
          id="input_firstname"
          v-model="vendor.firstName"
          type="text"
          placeholder="Straße"
          class="form-control"
        >
        <label for="input_firstname">Vorname</label>
      </div>
      <div class="form-label-group col-6">
        <input
          id="input_lastname"
          v-model="vendor.lastName"
          type="text"
          placeholder="Hausnummer"
          class="form-control"
        >
        <label for="input_lastname">Nachname</label>
      </div>
    </div>
    <div
      v-if="vendor.organizationType != 'PERSON'"
      class="form-label-group"
    >
      <input
        id="input1"
        v-model="vendor.organizationName"
        type="text"
        placeholder="Gib den vollständigen Namen der Organisation ein"
        required="required"
        class="form-control"
      >
      <label for="input1">Firmenname</label>
    </div>
    <h6>Anschrift</h6>
    <div class="form-row">
      <div class="form-label-group col-9">
        <input
          id="input2"
          v-model="vendor.street"
          type="text"
          placeholder="Straße"
          class="form-control"
        >
        <label for="input2">Straße</label>
      </div>
      <div class="form-label-group col-3">
        <input
          id="input3"
          v-model="vendor.housenumber"
          type="text"
          placeholder="Hausnummer"
          class="form-control"
        >
        <label for="input3">Hausnummer</label>
      </div>
    </div>
    <div class="form-row">
      <div class="form-label-group col-3">
        <input
          id="input4"
          v-model="vendor.postalcode"
          type="text"
          placeholder="Postleitzahl"
          class="form-control"
        >
        <label for="input4">Postleitzahl</label>
      </div>
      <div class="form-label-group col-9">
        <input
          id="input5"
          v-model="vendor.city"
          type="text"
          placeholder="Stadt"
          class="form-control"
        >
        <label for="input5">Stadt</label>
      </div>
    </div>
    <div class="form-row">
      <div class="form-label-group col-6">
        <input
          id="input6"
          v-model="vendor.country"
          type="text"
          placeholder="Land"
          class="form-control"
        >
        <label for="input6">Land</label>
      </div>
      <div class="form-label-group col-6">
        <input
          id="input7"
          v-model="vendor.state"
          type="text"
          placeholder="Bundesland"
          class="form-control"
        >
        <label for="input7">Bundesland</label>
      </div>
    </div>
    <h6>Steuerliche Angaben</h6>
    <div class="form-label-group">
      <input
        id="input8"
        v-model="vendor.taxNumber"
        type="text"
        placeholder="Steuernummer"
        class="form-control"
      >
      <label for="input8">Steuernummer</label>
    </div>
    <div class="form-label-group">
      <input
        id="input9"
        v-model="vendor.vatNumber"
        type="text"
        placeholder="Umsatzsteuer-Identifikationsnummer"
        class="form-control"
      >
      <label for="input9">Umsatzsteuer-Identifikationsnummer</label>
    </div>
    <h6>Bankkonto</h6>
    <div class="form-label-group">
      <input
        id="input10"
        v-model="vendor.iban"
        type="text"
        placeholder="IBAN"
        class="form-control"
      >
      <label for="input10">IBAN</label>
    </div>
    <div class="form-label-group">
      <input
        id="input11"
        v-model="vendor.bic"
        type="text"
        placeholder="BIC"
        class="form-control"
      >
      <label for="input11">BIC</label>
    </div>
    <LoadButton
      class="ml-auto float-right"
      size="sm"
      text="Speichern"
      @click="save"
    />
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';

export default {
	components: { LoadButton },
	props: ['selectedVendor'],
	data() {
		return {
			vendor: Object.assign({}, this.selectedVendor),
			organizationTypes: [
				{ value: 'PERSON', text: 'Einzelperson' },
				{ value: 'GBR', text: 'GbR' },
				{ value: 'GMBH', text: 'GmbH' },
				{ value: 'UG', text: 'UG' },
				{ value: 'AG', text: 'AG' },
				{ value: 'CLUB', text: 'Verein' },
				{ value: 'OTHER', text: 'Andere' }
			]
		};
	},
	computed: {
		// validOrganizationType(){
		//   if(!this.vendor.organizationType) return null
		//   else return true
		// },
		validOrganizationName() {
			if (!this.vendor.organizationName) return null;
			if (this.vendor.organizationName.length < 4) return false;
			if (this.vendor.organizationName.length > 60) return false;
			else return true;
		},
		validFirstName() {
			if (!this.vendor.firstName) return null;
			if (this.vendor.firstName.length < 2) return false;
			if (this.vendor.firstName.length > 20) return false;
			else return true;
		},
		validLastName() {
			if (!this.vendor.lastName) return null;
			if (this.vendor.lastName.length < 2) return false;
			if (this.vendor.lastName.length > 20) return false;
			else return true;
		}
	},
	methods: {
		async save(btn) {
			await API.put('innorder', '/Vendor/' + this.vendor.id, {
				body: this.vendor
			})
				.then((response) => {
					this.$bvToast.toast('Der Lieferant wurde erfolgreich geändert.', {
						title: 'Erstellen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});
					this.$emit('reloadVendor', this.vendor.id);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Ändern ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		}
	}
};
</script>
<style scoped>
#formBaseData {
  max-width: 800px;
}
</style>
