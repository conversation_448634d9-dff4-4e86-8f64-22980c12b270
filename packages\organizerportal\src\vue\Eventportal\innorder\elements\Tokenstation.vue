<template>
  <b-card class="mt-3">
    <b-modal
      :id="'saModal_' + tokenstation.id"
      title="Tokenstation bearbeiten"
    >
      <div class="form-label-group">
        <input
          id="input1"
          v-model="tokenstationRef.name"
          type="text"
          placeholder="Name"
          required="required"
          class="form-control"
        >
        <label for="input1">Name</label>
      </div>
      <div class="form-label-group">
        <b-form-input
          id="input2"
          v-model="tokenstationRef.description"
          placeholder="Beschreibung"
        />
        <label for="input2">Beschreibung</label>
      </div>
      <template #modal-footer="{ hide }">
        <DeleteButton
          text="Tokenstation löschen"
          @click="$emit('deleteTokenstation', $event, tokenstation)"
        />
        <SaveButton @click="btnUpdateTokenstation($event, hide)" />
      </template>
    </b-modal>
    <template #header>
      <div class="d-flex">
        <h5 class="flex-grow-1">
          {{ tokenstation.name }}
        </h5>
        <b-button
          v-b-modal="'saModal_' + tokenstation.id"
          variant="secondary"
          size="sm"
          style="height: fit-content"
        >
          <FontAwesomeIcon :icon="['fas', 'edit']" /> Tokenstation bearbeiten
        </b-button>
      </div>
    </template>
    <b-table
      hover
      :items="tokenstationEmployees"
      :fields="tokenstationEmployeeFields"
      class="mt-3 table-clickable"
      head-variant="light"
    >
      <template #cell(employee)="data">
        {{ data.item.firstName + " " + data.item.lastName }}
      </template>
      <template #cell(permissions)="data">
        <LoadButton
          v-for="perm of availableTokenstationPermissions"
          :key="perm.urn"
          size="sm"
          class="mr-1"
          icon="lock"
          :text="perm.badge"
          :variant="matchedPermission(data.item, perm) ? 'info':'outline-info'"
          @click="changePermission($event, data.item, perm)"
        />
      </template>
    </b-table>
    <b-button
      v-b-modal="&quot;assignEmployeeModal&quot; + tokenstation.id"
      variant="primary"
      size="sm"
    >
      <FontAwesomeIcon icon="plus-square" /> Mitarbeiter hinzufügen
    </b-button>
    <b-modal
      :id="&quot;assignEmployeeModal&quot; + tokenstation.id"
      ref="assignEmployeeModalRef"
      title="Mitarbeiter hinzufügen"
      hide-footer
      centered
    >
      <b-list-group>
        <b-list-group-item
          v-for="employee in selectableModalEmployees"
          :key="employee.userSubject"
          button
          @click="addEmployeeToTokenstation(employee)"
        >
          {{ employee.firstName + " " + employee.lastName }}
        </b-list-group-item>
      </b-list-group>
    </b-modal>
  </b-card>
</template>
<script lang="ts">
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import LoadButton from '../../../buttons/LoadButton.vue';
import SaveButton from '../../../buttons/SaveButton.vue';
import DeleteButton from '../../../buttons/DeleteButton.vue';
import type { PropType } from '@vue/composition-api';
import { defineComponent, getCurrentInstance, onMounted, ref, computed } from '@vue/composition-api';
import type { OldPermission, ResetButton, Tokenstation } from '@innevent/webapp-types';
import { tokenstation as api } from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useEmployeeState, loadEventEmployeesOfEvent } from '../../../../states/employeeState';
import { currentEventRef } from '../../../../states/eventState';
import type { Employee, EventEmployee } from '@innevent/types';
import type { BModal } from 'bootstrap-vue';
import { useTokenstationState } from '../../../../states/tokenstationState';

export default defineComponent({
	components: { FontAwesomeIcon, LoadButton, DeleteButton, SaveButton },
	props: {
		tokenstation: {
			type: Object as PropType<Tokenstation>, 
			required: true
		} 
	},
	emits: ['deleteTokenstation'],
	setup(props) {
		const permTokenstationUsers = ref<OldPermission[]>([]);
		const assignEmployeeModalRef = ref<BModal>();
		const instance = getCurrentInstance();
		const { employeesOfEventRef } = useEmployeeState();
		const { updateTokenstation } = useTokenstationState();

		const tokenstationRef = ref<Tokenstation>(props.tokenstation);
		const tokenstationEmployeeFields = [
			{ key: 'employee', label: 'Mitarbeiter', sortable: true },
			{ key: 'permissions', label: 'Berechtigungen' },
			{ key: 'buttons', label: '' }
		];
		const availableTokenstationPermissions = [{ 
			value: false, 
			urn: 'perm:innorder.tokenstation.use', 
			badge: 'TokenstationUse', 
			name: 'Tokenstation benutzen', 
			description: 'Mitarbeiter darf in dieser Tokenstation arbeiten' 
		}, { 
			value: false, 
			urn: 'perm:innorder.tokenstation.admin', 
			badge: 'TokenstationAdmin', 
			name: 'Tokenstation Admin', 
			description: 'Mitarbeiter mit allen Berechtigungen in einer Verkaufszone' 
		}
		];

		onMounted(async () => {
			try {
				permTokenstationUsers.value = await api.getPermTokenstations(props.tokenstation.id);
				await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
			} catch (error: any) {
				notifyError({ instance, error });
			}
		});

		const tokenstationEmployees = computed(() => {
			return employeesOfEventRef.value.filter((employee) => {
				return permTokenstationUsers.value.some((perm) => perm.userSubject == employee.userSubject);
			});
		});

		const selectableModalEmployees = computed(() => {
			return employeesOfEventRef.value.filter((employee) => {
				return !permTokenstationUsers.value.some((perm) => perm.userSubject == employee.userSubject);
			});
		});

		function matchedPermission(employee: Employee, perm) {
			return permTokenstationUsers.value.some((tokenstationPermission) => {
				return tokenstationPermission.userSubject == employee.userSubject && tokenstationPermission.permission == perm.urn;
			});
		}

		async function addEmployeeToTokenstation(employee) {
			assignEmployeeModalRef.value!.hide();
			let perm = availableTokenstationPermissions.find(perm => perm.urn == 'perm:innorder.tokenstation.use');
			createPermission(perm, employee);
		}
		async function createPermission(perm, employee: EventEmployee) {
			try {
				const newPermission = await api.createPermTokenstation({
					data: {
						permission: perm.urn,
						userSubject: employee.userSubject,
						TokenstationId: props.tokenstation.id,
						eventId: currentEventRef.value!.eventId!
					}
				});
				permTokenstationUsers.value.push(newPermission);
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
		}
		async function deletePermission(matchedPermission: OldPermission) {
			try {
				await api.deletePermTokenstation({
					key: {
						id: matchedPermission.id
					}
				});
				permTokenstationUsers.value = permTokenstationUsers.value.filter((permission) => permission.id !== matchedPermission.id);
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
		}
		async function changePermission(btn: ResetButton, employee, perm) {
			const permissionToDelete = permTokenstationUsers.value.find((tokenstationPermission) => {
				return tokenstationPermission.userSubject == employee.userSubject && tokenstationPermission.permission == perm.urn;
			});
			if (permissionToDelete) await deletePermission(permissionToDelete!);
			else await createPermission(perm, employee);
			btn.reset();
		}

		async function btnUpdateTokenstation(btn: ResetButton, hideModal) {
			try {
				await api.updateTokenstation({
					key: {
						id: props.tokenstation.id
					},
					data: {
						name: tokenstationRef.value.name,
						description: tokenstationRef.value.description
					}
				});
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
			hideModal();
		}

		return {
			tokenstationEmployeeFields,
			availableTokenstationPermissions,
			tokenstationEmployees,
			selectableModalEmployees,
			matchedPermission,
			addEmployeeToTokenstation,
			changePermission,
			btnUpdateTokenstation,
			assignEmployeeModalRef,
			tokenstationRef
		};

	}
});
</script>

<style>
.card-header .fa {
  transition: .3s transform ease-in-out;
}
.card-header .collapsed .fa {
  transform: rotate(-90deg);
}
</style>
