import type { ComponentInternalInstance } from '@innevent/webapp-types';
import type { BvToastOptions } from 'bootstrap-vue';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';

export type NotifyOptions = {
    instance: ComponentInternalInstance | null;
    title: string;
    message: string;
    toastOptions?: BvToastOptions;
}

export type ShortNotifyOptions = {
    instance: ComponentInternalInstance | null;
    title?: string;
    message?: string;
    toastOptions?: BvToastOptions;
	error?: any;
}

export function notify(options: NotifyOptions): void {
	options.instance?.proxy.$bvToast.toast(options.message, {
		...options.toastOptions,
		title: options.title
	});

}

export function notifyError(options: ShortNotifyOptions): void {
	const notifyoptions = {
		instance: options.instance,
		title: options.title ?? 'Fehler',
		message: options.message ?? 'Leider ist ein <PERSON> aufgetreten!',
		toastOptions: {
			...options.toastOptions,
			variant: 'danger',
			autoHideDelay: 10000
		}
	};
	if (options.error?.isAxiosError) {
		const axiorError = options.error as AxiosError;
		if (axiorError.response?.data.isInnEventError) {
			const innEventError = axiorError.response.data as InnEventError;
			if (innEventError.message) notifyoptions.message = innEventError.message;
			if (innEventError.validationDetails) {
				innEventError.validationDetails.forEach(validationError => {
					notifyoptions.message += `/ ${validationError.message}`;
				});
			}
		}
	}
	notify(notifyoptions);
}

export function notifySuccess(options: ShortNotifyOptions): void {
	notify({
		instance: options.instance,
		title: options.title ?? 'Erfolgreich',
		message: options.message ?? 'Erfolgreich gespeichert',
		toastOptions: {
			...options.toastOptions,
			variant: 'success'
		}
	});
}
