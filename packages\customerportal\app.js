// Vue Components
import Vue from 'vue'

import VueCompositionAPI from '@vue/composition-api';
Vue.use(VueCompositionAPI);

import VueRouter from 'vue-router'
Vue.use(VueRouter)
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.min.css'
Vue.component('multiselect', Multiselect)

// Bootstrap
import { BootstrapVue } from 'bootstrap-vue'
import 'bootstrap' //Importing JavaScript https://getbootstrap.com/docs/4.1/getting-started/webpack/
import './src/css/custom.scss'
Vue.use(BootstrapVue, {
  breakpoints: [`xs`, 'sm', 'md', 'lg', 'xl', 'xxl', 'xxxl']
})

// CSS
import './src/css/global.css'

// Fontawesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
library.add(fas)
library.add(far)
Vue.component('FontAwesomeIcon', FontAwesomeIcon)

// import Amplify, { Auth, API } from 'aws-amplify';
// // import awsconfig from './aws-exports';
// import amplifyConfig from './src/js/AmplifyConfig';
// Amplify.configure(amplifyConfig);
import Amplify from '@aws-amplify/core';
import { Auth } from 'aws-amplify'
import awsconfig from './src/js/AmplifyConfig';
Amplify.configure(awsconfig);
Auth.configure(awsconfig)

// Other Modules
import URL from 'url-parse'

// Vue Pages
import App from './src/vue/App.vue'
import PageNotFound from './src/vue/PageNotFound.vue'
import CustomerPortalHome from './src/vue/pages/CustomerPortalHome.vue'
import TokenInfoPage from './src/vue/pages/TokenInfoPage.vue'
import TokenPayoutPage from './src/vue/pages/TokenPayoutPage.vue'

// Vue Router
const router = new VueRouter({
  mode: 'history',
  routes: [
    { name: 'portalHome', path: '/', component: CustomerPortalHome },
    { name: 'tokenInfo', path: '/tokeninfo', component: TokenInfoPage },
    { name: 'tokenPayout', path: '/token-payout', component: TokenPayoutPage },
    { path: '/tokeninfo.php', redirect: to => {
      return { name: 'tokenInfo', query: { uid: Object.keys(to.query)[0] }}
    }},
    { path: '/u', redirect: to => {
      return { name: 'tokenInfo', query: { uid: to.query.uid }}
    }},
    // Since 2024 official redirect url for scanning tags:
    { path: '/t/:uid', redirect: to => {
      return { name: 'tokenInfo', query: { uid: to.params.uid }}
    }},
    { path: '*', component: PageNotFound }
  ]
})

const url = new URL(window.location.href, true)


async function startLoad(){
  if(url.query.code){ // Auth Code ist gesetzt
    try {
      var response = await API.get('inneventAnonym', '/AuthCache/' + url.query.code)

      const keyPrefix = `CognitoIdentityServiceProvider.${Auth.userPool.clientId}.${response.username}`;
      const idTokenKey = `${keyPrefix}.idToken`;
      const accessTokenKey = `${keyPrefix}.accessToken`;
      const refreshTokenKey = `${keyPrefix}.refreshToken`;

      localStorage.setItem(idTokenKey, response.idToken)
      localStorage.setItem(accessTokenKey, response.accessToken)
      localStorage.setItem(refreshTokenKey, response.refreshToken)
      localStorage.setItem(`CognitoIdentityServiceProvider.${Auth.userPool.clientId}` + '.LastAuthUser', response.username)

      await Auth.currentAuthenticatedUser()
      delete url.query.code
      console.log('Vue Router remove code');
			router.replace(url);
      // TODO: deviceKey mit einbauen, wenn Geräte gespeichert werden
    }
    catch(error){
      console.log('Fehler bei Tokenabfrage')
      console.log(error)
      return // Prevent Loop on Auth Error
    }
  }

  // Get Cogntio User Object
  var cognitoUser = await Auth.currentAuthenticatedUser({
    bypassCache: false  // Optional, By default is false. If set to true, this call will send a request to Cognito to get the latest user data
  }).catch(err => {
    // var loginUrl = URL(process.env.URL_INNLOGIN, true)
    // loginUrl.pathname = '/login'
    // loginUrl.query.redirect_uri = window.location.href
    // window.location.replace(loginUrl.toString())
  })

  var app = new Vue({
    router,
    render: (createEl) => createEl(App, {
      props: { cognitoUser },
    })
  }).$mount('#app')
}
startLoad()
