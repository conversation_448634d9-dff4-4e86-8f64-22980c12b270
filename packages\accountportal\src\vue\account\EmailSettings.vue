<template>
  <div>
    <h3>E-Mail Adresse</h3>
    <LoadingSpinner v-if="isLoading" />
    <div
      v-else
      class="mt-3 max-w600"
    >
      <div class="form-label-group">
        <input
          id="inputEmail"
          v-model="emailAdress"
          placeholder="<EMAIL>"
          required
          class="form-control"
          @keyup.enter="clickBtn()"
        >
        <label for="inputEmail">E-Mail Adresse</label>
      </div>
      <b-alert
        :show="successAlert"
        variant="success"
      >
        Wir haben dir einen Bestätigungslink an deine neue E-Mail Adresse gesendet. Bitte checke deine Mails
      </b-alert>
      <SingleSaveButton
        ref="savebtn"
        @save="save"
      />
    </div>
  </div>
</template>
<script>
import { Auth } from 'aws-amplify';
import LoadingSpinner from '../tools/LoadingSpinner.vue';
import SingleSaveButton from '../buttons/SingleSaveButton.vue';
import * as api from '@innevent/webapp-api';

export default {
	components: { LoadingSpinner, SingleSaveButton },
	props: ['selectedVendor', 'events', 'selectedEvent'],
	data() { return {
		isLoading: true,
		emailAdress: '',
		successAlert: false
	};},
	computed: {

	},
	created() {
		this.getUserData();
	},
	methods: {
		async getUserData() {
			this.emailAdress = (await Auth.currentUserInfo()).attributes.email;
			this.isLoading = false;
		},
		clickBtn() {
			this.$refs.savebtn.save();
		},
		async save(btn) {
			this.successAlert = false;
			try {
				await api.createEmailChangeRequest({
					data: {
						newMail: this.emailAdress
					}
				});
				btn.setSuccess();
				this.successAlert = true;
			} catch (error) {
				btn.setError(error.response.data);
			}
		}
	}
};
</script>