<template>
  <div>
    <b-row>
      <b-col><h2>Übersicht angemeldeter Geräte</h2></b-col>
    </b-row>
    <InnEventConnectionDeviceTable />
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { loadConnectionDevices } from '../../../states/connectionDeviceState';
import { currentEventRef } from '../../../states/eventState';
import InnEventConnectionDeviceTable from './elements/InnEventConnectionDeviceTable.vue';


export default defineComponent({
	components: { InnEventConnectionDeviceTable },
	setup() {
		onMounted(async ()  => {
			await loadConnectionDevices(currentEventRef.value!.eventId!); 
		});
	}
});
</script>