<template>
  <div>
    <b-row>
      <b-col><h2>Einstellungen</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="Einstellungen speichern"
          @click="btnSaveSettings"
        />
      </b-col>
    </b-row>
    <b-container>
      <b-row
        v-for="(field, fieldKey) in inputFieldsRef"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col
          sm="6"
        >
          <label
            :for="`field-${fieldKey}`"
          >{{ field.name }}:</label>
        </b-col>
        <b-col
          sm="6"
        >
          <b-form-select
            v-if="field.type == 'dropdown'"
            v-model="field.value"
            :state="isValidOrganizer"
            :options="field.dropdownValues"
            text-field="organizationName"
            value-field="organizerId"
            style="width: 300px"
          />
          <b-form-checkbox
            v-else-if="field.type == 'switch'"
            v-model="field.value"
            name="check-button"
            switch
            size="lg"
          />
          <b-input-group
            v-else-if="field.type == 'decimal'"
            :append="field.currency"
            style="width: 300px"
          >
            <b-form-input
              :id="`field-${fieldKey}`"
              v-model="field.value"
              type="number"
            />
          </b-input-group>
          <b-form-input
            v-else
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            :aria-describedby="`field-${fieldKey}-feedback`"
            :state="field.value.length < 5"
            style="width: 300px"
          />
          <b-form-invalid-feedback :id="`field-${fieldKey}-feedback`">
            {{ field.state.feedback }}
          </b-form-invalid-feedback>
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted, ref } from '@vue/composition-api';
import LoadButton from '../../buttons/LoadButton.vue';
import { currentEventRef } from '../../../states/eventState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { ResetButton } from '@innevent/webapp-types';
import { useEventState } from '../../../states/eventState';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const instance = getCurrentInstance();
		const { updateEvent } = useEventState();

		onMounted(async ()  => {
			try {
				const settings = currentEventRef.value?.settings.innticket;
				Object.keys(inputFieldsRef.value).forEach(key => {
					inputFieldsRef.value[key].value = settings![key];
				});
			} catch (error: any) {
				notifyError({ instance, error });
			}
		});

		async function btnSaveSettings(btn: ResetButton) {
			try {
				let eventSettings = currentEventRef.value!.settings;
				eventSettings.innticket.ticketCodePrefix =  inputFieldsRef.value.ticketCodePrefix.value;

				await updateEvent({
					settings: eventSettings
				});
				notifySuccess({ instance, message: 'Einstellungen gespeichert!' });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		const inputFieldsRef = ref({
			ticketCodePrefix: {
				name: 'Prefix Ticket Code',
				type: 'text',
				value: '',
				state: {
					validation: 'validateLength(field, 5)',
					feedback: 'Maximal 4 Zeichen'
				}
			}
		});

		function validateLength(field: any, length: number) {
			return field.value.length < length;
		}

		return {
			btnSaveSettings,
			inputFieldsRef
		};
	}
});
</script>
<style scoped>

</style>
