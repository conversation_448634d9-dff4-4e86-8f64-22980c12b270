import type { TechnicalInnUser, TechnicalUserReferenceObjectType } from '@innevent/webapp-types';
import { apiInnOrder } from './instances';

export type TechnicalUserCreateOptions = Partial<Pick<TechnicalInnUser, 'firstName' | 'lastName'>> & {
    referenceId: string;
	referenceObjectType: TechnicalUserReferenceObjectType;
};
export async function createTechnicalUser(options: TechnicalUserCreateOptions): Promise<TechnicalInnUser> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	const response = await (await apiInnOrder()).post(`/TechnicalUser/Vendor/${options.referenceId}`, {
		firstName: options.firstName,
		lastName: options.lastName
	}, { params });
	return response.data;
}


export type TechnicalUserDeleteOptions = {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
};
export async function deleteTechnicalUser(options: TechnicalUserDeleteOptions): Promise<void> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	await (await apiInnOrder()).delete(`/TechnicalUser/${options.userSubject}`, { params });
}

export type EmployeeForTechnicalUserCreateOptions = {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
};

export type TechnicalUserUpdateOptions = Partial<Pick<TechnicalInnUser, 'firstName' | 'lastName'>> & {
	referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
 }
export async function updateTechnicalUser(options: TechnicalUserUpdateOptions): Promise<void> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	await (await apiInnOrder()).put(`/TechnicalUser/${options.userSubject}`, {
		firstName: options.firstName,
		lastName: options.lastName
	}, { params });
}


export type TechnicalUserGetOptions =  {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
};
export async function getTechnicalUsers(options: TechnicalUserGetOptions): Promise<TechnicalInnUser[] | never> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	const response = await (await apiInnOrder()).get(`/TechnicalUser/Vendor/${options.referenceId}`, { params });
	return response.data;
}

function getReferenceId(referenceId: string, referenceObjectType: TechnicalUserReferenceObjectType): any {
	return referenceObjectType == 'event' ? { eventId: referenceId } : { vendorId: referenceId };
}

export type TechnicalUserCreateEmployeeOptions = {
    referenceObjectType: TechnicalUserReferenceObjectType;
	referenceId: string;
	userSubject: string;
	firstName: string;
	lastName: string;
};
export async function createEmployeeForTechnicalUser(options: TechnicalUserCreateEmployeeOptions): Promise<string> {
	const params = getReferenceId(options.referenceId, options.referenceObjectType);
	return await (await apiInnOrder()).post(`/TechnicalUser/${options.userSubject}/Vendor/${options.referenceId}`, {
		firstName: options.firstName,
		lastName: options.lastName
	}, { params });
}