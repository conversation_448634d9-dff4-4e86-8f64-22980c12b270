<template>
  <div>
    <b-row
      v-if="!identifierData"
      class="justify-content-center"
    >
      <b-col
        sm="12"
        lg="6"
      >
        <b-card title="Token Info">
          <b-card-sub-title class="mb-2">
            Guthaben und Transaktionen deines Chips anzeigen
          </b-card-sub-title>
          <b-card-text>
            <h5>Option 1: Lese den Chip mit deinem Smartphone</h5>
            <p>
              Die meisten aktuellen Smartphones (Android & iPhone ) haben NFC.
              Wenn dein Handy dazu gehört und die NFC aktiviert hast, kannst du deine Karte bzw. dein Armband einfach an dein Gerät halten.
              Je nach Smartphone ist der NFC-Leser an einer anderen Stelle verbaut,.
            </p>
            <p>
              Bei einer erfolgreichen Übertragung macht dein Smartphone einen kurzen Signalton.
              Daraufhin öffnet sich dein Browser und die Informationen des Chip's werden angezeigt.
            </p>
          </b-card-text>
          <b-card-text>
            <h5>Option 2: Gib die UID-Nummer deines Chip's manuell ein</h5>
            <p>
              Sollte dein Smartphone kein NFC unterstützen kannst du die UID manuell ablesen und in das untere Feld eintragen.
              Die UID findest du auf der Rückseite deiner Karte, bzw. auf der Rückseite deines Armband-Chip's.
              Diese besteht aus 14 Alphanumerischen Zeichen.
            </p>
          </b-card-text>
          <b-input-group
            prepend="Chip UID"
            class="mt-3"
          >
            <b-form-input
              v-model="uid"
              :state="validUID"
              placeholder="Enter your UID"
              :disabled="uidLookupLoading"
              @keyup.enter="uidLookup()"
            />
            <b-input-group-append>
              <b-button
                variant="primary"
                :disabled="!validUID || uidLookupLoading"
                @click="uidLookup()"
              >
                <b-spinner
                  v-if="uidLookupLoading"
                  small
                  class="mr-2"
                />Daten abrufen
              </b-button>
            </b-input-group-append>
          </b-input-group>
          <h5
            v-if="searchHistory.length > 0"
            class="mt-3"
          >
            Letzte Suchanfragen:
          </h5>
          <b-button-group
            v-for="searchQuery in searchHistory"
            :key="searchQuery"
            size="sm"
            class="mr-3"
          >
            <b-button
              variant="outline-primary"
              @click="searchLastResult(searchQuery)"
            >
              {{ searchQuery }}
            </b-button>
            <b-button
              variant="outline-primary"
              @click="removeLastResult(searchQuery)"
            >
              <FontAwesomeIcon icon="trash" />
            </b-button>
          </b-button-group>
          <b-alert
            v-if="errorMessage"
            show
            variant="danger"
            class="mt-3"
          >
            {{ errorMessage }}
          </b-alert>
        </b-card>
      </b-col>
    </b-row>
    <b-card
      v-else
      no-body
    >
      <b-card-body>
        <b-card-title>Token Info</b-card-title>
        <b-card-sub-title class="mb-2">
          Chip {{ identifierData.cashlessIdentifier.uid }}
        </b-card-sub-title>
        <b-list-group class="mb-4 mt-4">
          <b-list-group-item>Veranstaltung: {{ identifierData.cashlessIdentifier.eventName }}</b-list-group-item>
          <b-list-group-item>Guthaben: {{ formatCurrency(identifierData.cashlessIdentifier.tokenCredit) }}</b-list-group-item>
          <b-list-group-item>Status: {{ textStatus }}</b-list-group-item>
          <b-list-group-item>Pfand: {{ formatCurrency(identifierData.cashlessIdentifier.deposit) }}</b-list-group-item>
          <!-- <b-list-group-item>
            Die Online-Auszahlung für das Electrifinity 2025 hat noch nicht begonnen.
          </b-list-group-item> -->
          <!-- <b-list-group-item>
            <b-button
              :to="`/token-payout?uid=${identifierData.cashlessIdentifier.uid}`"
              variant="primary"
            >
              Online Top-Down
            </b-button>
          </b-list-group-item> -->
        </b-list-group>
      </b-card-body>
      <b-card-body
        body-bg-variant="light"
        class="d-flex justify-content-between"
      >
        <h4>Transaktionen</h4>
        <b-form-group>
          <b-form-radio-group
            v-model="selectedSortOption"
            :options="transSortOptions"
            buttons
            button-variant="outline-primary"
          />
        </b-form-group>
      </b-card-body>
      <b-list-group
        flush
        class="mb-4"
      >
        <IdentifierTransaction
          v-for="t of innOrderTransactions"
          :key="t.id"
          :transaction="t"
        />
      </b-list-group>
    </b-card>
  </div>
</template>
<script>

// Frameworks
import { API } from 'aws-amplify';
let validator = require('validator');

// Vue Components
import IdentifierTransaction from './TokenInfo/IdentifierTransaction.vue';

export default {
	components: { IdentifierTransaction },
	props: [],
	data() { return {
		uid: '',
		errorMessage: null,
		uidLookupLoading: false,
		identifierData: null,
		transSortOptions: [
			{ text: 'Neuste oben', value: 'firstNew' },
			{ text: 'Älterste oben', value: 'firstOld' }
		],
		selectedSortOption: 'firstNew',
		searchHistory: []
	};},
	computed: {
		innOrderTransactions() {
			let trans = this.identifierData.innOrderTransactions;
			if (this.selectedSortOption == 'firstNew') {
				trans.sort((a, b) => (a.builtAt < b.builtAt) ? 1 : -1);
			} else if (this.selectedSortOption == 'firstOld') {
				trans.sort((a, b) => (a.builtAt > b.builtAt) ? 1 : -1);
			}
			return trans;
		},
		textStatus() {
			if (!this.identifierData) return '';
			switch (this.identifierData.cashlessIdentifier.status) {
			case 'IN_USE': return 'Im Umlauf';
			case 'PAID_OUT_TOKENSTATION': return 'Ausbezahlt an einer Tokenstation';
			case 'MALFUNCTION': return 'Defekt';
			case 'PAID_OUT_ONLINE': return 'Online Ausbezahlt';
			default: return 'unbekannt';
			}
		},
		validUID() {
			if (!this.uid) return false;
			if (this.uid.length < 12) return false;
			if (!validator.isAlphanumeric(this.uid)) return false;
			return true;
		}
	},
	watch: {
		searchHistory(searchHistory) {
			localStorage.searchHistory = searchHistory.join(',');
		}
	},
	created() {
		if (this.$route.query.uid) {
			this.uid = this.$route.query.uid;
			this.uidLookup();
		}
	},
	mounted() {
		if (localStorage.searchHistory) {
			this.searchHistory = localStorage.searchHistory.split(',');
		}
	},
	methods: {
		searchLastResult(searchQuery) {
			this.uid = searchQuery;
			this.uidLookup();
		},
		removeLastResult(searchQuery) {
			this.searchHistory = this.searchHistory.filter(q => q != searchQuery);
		},
		formatCurrency(val) {
			return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(val);
		},
		async uidLookup() {
			this.errorMessage = null;
			if (!this.validUID) {
				this.errorMessage = 'Die UID ist ungültig!';
				return;
			}
			this.uidLookupLoading = true;
			await API.get('innorderAnonym', '/CashlessIdentifier/getPublicData/uid/' + this.uid).then(response  => {
				if (response.code > 300) throw response;
				this.identifierData = response;
				this.$router.push({ query: { uid: this.uid } }).catch(err => {});
				if (!this.searchHistory.includes(this.uid)) this.searchHistory.push(this.uid);
			}).catch(error => {
				if (error.msg) this.errorMessage = error.msg;
				else {
					this.errorMessage = 'Leider ist ein Fehler aufgetreten, bitte versuche es später erneut';
					console.log(error);
				}
			});
			this.uidLookupLoading = false;
		}
	}
};

</script>
<style scoped>

</style>
