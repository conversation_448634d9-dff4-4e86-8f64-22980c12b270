<template>
  <b-container fluid>
    <b-row align-h="center">
      <b-col cols="1">
        <b-button
          size="sm"
          @click="paginateBack"
        >
          <FontAwesomeIcon
            icon="arrow-left"
          />
        </b-button>
      </b-col>
      <b-col cols="1">
        <b-button
          size="sm"
          @click="paginateNext"
        >
          <FontAwesomeIcon
            icon="arrow-right"
          />
        </b-button>
      </b-col>
    </b-row>
  </b-container>
</template>
<script lang="ts">
import { defineComponent } from '@vue/composition-api';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default defineComponent({
	name: 'InnPagination',
	components: { FontAwesomeIcon },
	emits: ['clickBackward', 'clickForward'],
	setup(props, { emit }) {
		async function paginateBack() {
			emit('clickBackward');
		}

		async function paginateNext() {
			emit('clickForward');
		}
		return { paginateBack, paginateNext };
	}
});
</script>