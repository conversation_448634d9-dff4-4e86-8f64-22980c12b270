<template>
  <div>
    <b-card>
      <h1>Token Auszahlung</h1>

      <b-form @submit.prevent="submitForm">
        <b-form-group label="Vorname:">
          <b-form-input
            v-model="formData.firstName"
            :state="validFirstName"
            placeholder="Dein Vorname"
            required
            @input="formTouched.firstName = true"
          />
        </b-form-group>

        <b-form-group label="Nachname:">
          <b-form-input
            v-model="formData.lastName"
            :state="validLastName"
            placeholder="Dein Nachname"
            required
            @input="formTouched.lastName = true"
          />
        </b-form-group>

        <b-form-group label="Email:">
          <b-form-input
            v-model="formData.email"
            :state="validEmail"
            placeholder="Deine Email"
            required
            @input="formTouched.email = true"
          />
        </b-form-group>

        <b-form-group label="IBAN:">
          <b-form-input
            v-model="formData.iban"
            :state="validIBAN"
            placeholder="DE00 0000 0000 0000 0000 00"
            required
            @input="formTouched.iban = true"
          />
        </b-form-group>

        <b-form-group label="BIC:">
          <b-form-input
            v-model="formData.bic"
            :state="validBIC"
            placeholder="ABCDEFGHXXX"
            required
            @input="formTouched.bic = true"
          />
        </b-form-group>

        <b-form-group label="Tag ID:">
          <b-form-input
            v-model="formData.identifierTagId"
            :state="valididentifierTagId"
            placeholder="Deine Tag ID"
            required
            @input="formTouched.identifierTagId = true"
          />
        </b-form-group>

        <b-form-group label="Tag PIN:">
          <b-form-input
            v-model="formData.identifierTagPin"
            :state="valididentifierTagPin"
            placeholder="Deine Tag PIN"
            required
            @input="formTouched.identifierTagPin = true"
          />
        </b-form-group>

        <b-button
          variant="primary"
          :disabled="!formIsValid"
          @click="submitForm"
        >
          Auszahlung beantragen
        </b-button>
      </b-form>
    </b-card>
  </div>
</template>

<script>
import validator from 'validator';
import { v4 as uuid } from 'uuid';
export default {
	data() {
		return {
			formData: {
				firstName: '',
				lastName: '',
				email: '',
				iban: '',
				bic: '',
				identifierTagId: this.$route.query.uid || '',
				identifierTagPin: ''
			},
			formTouched: {
				firstName: false,
				lastName: false,
				email: false,
				iban: false,
				bic: false,
				identifierTagId: false,
				identifierTagPin: false
			},
			formSubmitted: false
		};
	},
	computed: {
		validFirstName() {
			if (!this.formTouched.firstName && !this.formSubmitted) return null;
			return this.formData.firstName.length > 1;
		},
		validLastName() {
			if (!this.formTouched.lastName && !this.formSubmitted) return null;
			return this.formData.lastName.length > 1;
		},
		validEmail() {
			if (!this.formTouched.email && !this.formSubmitted) return null;
			return validator.isEmail(this.formData.email);
		},
		validIBAN() {
			if (!this.formTouched.iban && !this.formSubmitted) return null;
			return validator.isIBAN(this.formData.iban);
		},
		validBIC() {
			if (!this.formTouched.bic && !this.formSubmitted) return null;
			return /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/.test(this.formData.bic);
		},
		valididentifierTagId() {
			if (!this.formTouched.identifierTagId && !this.formSubmitted) return null;
			return this.formData.identifierTagId.length > 0;
		},
		valididentifierTagPin() {
			if (!this.formTouched.identifierTagPin && !this.formSubmitted) return null;
			return this.formData.identifierTagPin.length > 0;
		},
		formIsValid() {
			return this.formData.firstName.length > 1 &&
             this.formData.lastName.length > 1 &&
             validator.isIBAN(this.formData.iban) &&
             /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/.test(this.formData.bic) &&
             this.formData.identifierTagId.length > 0 &&
             this.formData.identifierTagPin.length > 0;
		}
	},
	methods: {
		async submitForm() {
			this.formSubmitted = true;

			if (!this.formIsValid) {
				this.$bvToast.toast('Bitte fülle alle Felder korrekt aus.', {
					title: 'Validierungsfehler',
					autoHideDelay: 3000,
					variant: 'danger'
				});
				return;
			}

			try {
				const result = await fetch(`${process.env.API_URL_INNEVENT}/identifier-tag-payout-request`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						...this.formData,
						id: uuid()
					})
				});
				if (!result.ok) {
					const body = await result.json();
					this.$bvToast.toast(
						'Bei der Übermittlung ist ein Fehler aufgetreten: ' +
					(body.message ?? 'Unbekannter Fehler'),
						{
							title: 'Fehler',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					return;
				}

				this.$bvToast.toast('Deine Auszahlungsanfrage wurde erfolgreich übermittelt.', {
					title: 'Anfrage erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});

				// Reset form after successful submission
				this.formData = {
					firstName: '',
					lastName: '',
					iban: '',
					bic: '',
					identifierTagId: '',
					identifierTagPin: ''
				};

				// Reset form touched state
				this.formTouched = {
					firstName: false,
					lastName: false,
					iban: false,
					bic: false,
					identifierTagId: false,
					identifierTagPin: false
				};

				this.formSubmitted = false;

			} catch (error) {
				console.error(JSON.stringify(error, null, 2));
				this.$bvToast.toast(
					'Bei der Übermittlung ist ein Fehler aufgetreten: ' +
					(error.response ?? 'Unbekannter Fehler'),
					{
						title: 'Fehler',
						autoHideDelay: 10000,
						variant: 'danger'
					}
				);
			}
		}
	}
};
</script>

<style scoped>
.card {
  max-width: 800px;
  margin: 0 auto;
}
</style>