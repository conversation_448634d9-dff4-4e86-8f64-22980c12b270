<template>
  <b-nav
    vertical
    pills
  >
    <div
      v-for="(navContainer, index) of permittedNavItems"
      ref="navContainer"
      :key="`navItem-${index}`"
    >
      <b-button
        v-b-toggle="'navGroup' + index"
        variant="outline-primary"
        class="mb-2"
        block
      >
        {{ navContainer.name }}
      </b-button>
      <b-collapse
        :id="'navGroup' + index"
        :visible="navContainer.items.some(navItem => navItem.link === $route.name)"
        class="mb-2"
        accordion="accordionGroupNav"
      >
        <b-nav-item
          v-for="navItem of navContainer.items"
          :key="navItem.link"
          exact
          exact-active-class="active"
          :to="{ name: navItem.link }"
        >
          <FontAwesomeIcon
            :icon="navItem.icon"
            fixed-width
          /><span class="ml-3">{{ navItem.name }}</span>
        </b-nav-item>
      </b-collapse>
    </div>
  </b-nav>
</template>
<script lang="ts">

import { computed, defineComponent } from '@vue/composition-api';
import { usePermissionState } from './../../states/permissionState';


export default defineComponent({
	setup(props, context) {
		const { permittedEventPermissions } = usePermissionState();
		const { $route } = context.root;

		const allNavItems = [
			{ name: 'Allgemein', icon: 'building', items: [
				{ name: 'Eventdaten', link: 'eBasedata', icon: 'building' },
				{ name: 'Mitarbeiter', link: 'eEmployee', icon: 'users' },
				{ name: 'Geräte Übersicht', link: 'eConnectionLogs', icon: 'mobile' },
				{ name: 'EC-Terminals', link: 'eECTerminal', icon: 'credit-card' },
				{ name: 'Kassen', link: 'eCashbox', icon: 'cash-register' }
			] },
			{ name: 'Inn//Order', icon: 'th-list', items: [
				{ name: 'Dashboard', link: 'eOrderDashboard', icon: 'chart-line' },
				{ name: 'Tokenstation', link: 'eTokenstation', icon: 'money-bill-1-wave' },
				{ name: 'Verkäufer', link: 'eVendor', icon: 'building' },
				{ name: 'Pfandgruppen', link: 'eDepositgroup', icon: 'money-bill-transfer' },
				{ name: 'Einstellungen', link: 'eOrderSettings', icon: 'cogs' }
			] },
			{ name: 'Inn//Ticket', icon: 'users', items: [
				// { name: 'Dashboard', link: 'eTicketDashboard', icon: 'chart-line' },
				{ name: 'Tickets', link: 'eTicket', icon: 'ticket' },
				{ name: 'Ticketimport', link: 'eTicketImport', icon: 'file-import' },
				{ name: 'Entwertungsgruppen', link: 'eRedemptionGroup', icon: 'qrcode' },
				{ name: 'Bestellungen', link: 'eOrders', icon: 'building' },
				{ name: 'Einstellungen', link: 'eTicketSettings', icon: 'cogs' }
			] },
			{ name: 'Inn//Access', icon: 'calendar-alt', items: [
				{ name: 'AccessAreas', link: 'eAccessArea', icon: 'key' }
			] },
			{ name: 'Inn//Staff', icon: 'calendar-alt', items: [] },
			{ name: 'Inn//Stock', icon: 'calendar-alt', items: [] }
		];

		const permittedNavItems = computed(() => {
			return allNavItems.filter(navItem => {
				if (['Inn//Staff', 'Inn//Stock'].includes(navItem.name)) return false;
				if (navItem.name == 'Allgemein' && permittedEventPermissions.value.some(permittedPermission =>
					[
						'perm:core.event.owner', 
						'perm:core.event.admin', 
						'perm:core.event.edit', 
						'perm:core.event.show'
					].includes(permittedPermission))) return true;
				if (navItem.name == 'Inn//Order' && permittedEventPermissions.value.some(permittedPermission =>
					[
						'perm:core.event.owner', 
						'perm:core.event.admin',
						'perm:innorder.event.edit', 
						'perm:innorder.event.show'
					].includes(permittedPermission))) return true;
				if (navItem.name == 'Inn//Ticket' && permittedEventPermissions.value.some(permittedPermission =>
					[ 
						'perm:core.event.owner', 
						'perm:core.event.admin', 
						'perm:innticket.event.edit', 
						'perm:innticket.event.show'
					].includes(permittedPermission))) return true;
				if (navItem.name == 'Inn//Access' && permittedEventPermissions.value.some(permittedPermission =>
					[ 
						'perm:core.event.owner', 
						'perm:core.event.admin', 
						'perm:innaccess.event.edit', 
						'perm:innaccess.event.show'
					].includes(permittedPermission))) return true;
				return false;
			});
		});

		return {
			permittedNavItems,
			$route
		};
	}
});

</script>
