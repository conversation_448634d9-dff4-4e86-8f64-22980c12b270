import type { EventEmployee, OrganizerEmployee, InnEventQueryOptions, KeyObject } from '@innevent/types';
import * as api from '@innevent/webapp-api';
import type { ComputedRef } from '@vue/composition-api';
import { watch } from '@vue/composition-api';
import { computed, ref } from '@vue/composition-api';
import { currentEventRef } from './eventState';
import { useOrganizerState } from './organizerState';
import { employeeState as log } from '../loglevel';
import Vue from 'vue';

log.debug('INIT employeeState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const eventEmployeesRef = ref<EventEmployee[]>([]); // Only EventEmployees of current Account
const organizerEmployeesRef = ref<OrganizerEmployee[]>([]);
const employeesOfEventRef = ref<EventEmployee[]>([]);

watch(eventEmployeesRef, () => {
	log.debug('WATCH eventEmployeesRef', eventEmployeesRef.value);
});

const { currentOrganizerRef }  = useOrganizerState();

const currentEventEmployee: ComputedRef<EventEmployee | undefined> = computed(() => {
	log.debug('COMPUTED currentEventEmployee START');
	const currentEventEmployee = eventEmployeesRef.value?.find(employee => 
		employee.eventId === currentEventRef.value?.eventId);
	log.debug('COMPUTED currentEventEmployee END', currentEventEmployee);
	return currentEventEmployee;
});

const currentOrganizerEmployee: ComputedRef<OrganizerEmployee | undefined> = computed(() => {
	log.debug('COMPUTED currentOrganizerEmployee START');
	const currentOrganizerEmployee = organizerEmployeesRef.value.find(employee => 
		employee.organizerId === currentOrganizerRef.value?.organizerId);
	log.debug('COMPUTED currentOrganizerEmployee END', currentOrganizerEmployee);
	return currentOrganizerEmployee;
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useEmployeeState() {
	log.debug('useEmployeeState()');

	return {
		eventEmployeesRef,
		organizerEmployeesRef,
		currentEventEmployee,
		currentOrganizerEmployee,
		employeesOfEventRef, 
		isLoadingRef,
		isLoadingInitialRef
	};
}

export async function loadEventEmployee(eventId: string, userSubject: string): Promise<void> {
	log.debug('loadEventEmployee()', arguments);
	isLoadingRef.value = true;
	try {
		const employee = await api.getEventEmployee({
			key: { 
				eventId,
				userSubject
			}
		});
		const existingIndex = eventEmployeesRef.value.findIndex(employee => employee.eventId === eventId);
		existingIndex === -1 ? eventEmployeesRef.value.push(employee) : eventEmployeesRef.value[existingIndex] = employee;
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

export async function loadEventEmployeesOfEvent(eventId: string, queryParameter?: InnEventQueryOptions<EventEmployee>): Promise<void> {
	log.debug('loadEventEmployeesOfEvent()', eventId);
	isLoadingRef.value = true;
	try {
		const eventEmployeeResult = await api.getEventEmployees({
			key: { 
				eventId
			},
			queryParameter
		});
		employeesOfEventRef.value = eventEmployeeResult.items;
		isLoadingRef.value = false;
		isLoadingInitialRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}
export async function deleteEventEmployee(eventId: string, userSubject: string): Promise<void> {
	log.debug('deleteEventEmployee()', arguments);
	await api.deleteEventEmployee({
		key: {
			eventId,
			userSubject
		}
	});
	employeesOfEventRef.value = employeesOfEventRef.value.filter((eventEmployee) => 
		eventEmployee.userSubject !== userSubject);
	
}

type UpdateEventEmployeeOptions = {
	eventId: string;
	userSubject: string;
	data: Partial<Pick<EventEmployee, 'firstName' | 'lastName' | 'position'>>;
}

export async function updateEventEmployee(options: UpdateEventEmployeeOptions): Promise<void> {
	log.debug('updateEventEmployee()', options);
	const updatedEventEmployee = await api.updateEventEmployee({
		key: {
			eventId: options.eventId,
			userSubject: options.userSubject
		},
		data: {
			firstName: options.data.firstName,
			lastName: options.data.lastName,
			position: options.data.position
		}
	});
	const index = employeesOfEventRef.value.findIndex((eventEmployee) => 
		eventEmployee.userSubject == options.userSubject);
	if (index == -1) {
		log.debug('updateEventEmployee()', 'Could not find Employee');
		return;
	}

	Vue.set(employeesOfEventRef.value, index, updatedEventEmployee);
}

export async function addPermissions(eventId: string, userSubject: string, permissions: KeyObject): Promise<void> {
	log.debug('addPermissions()', arguments);
	await api.addPermissionsEventEmployee({
		key: {
			eventId, 
			userSubject
		},
		data: {
			permissions
		}
	});
	const eventEmployee = employeesOfEventRef.value.find((eventEmployee) => 
		eventEmployee.eventId == eventId && eventEmployee.userSubject == userSubject);
	if (!eventEmployee) {
		log.debug('addPermissions()', 'Could not find Employee');
		return;
	}
	Object.keys(permissions).forEach((permission) => {
		if (!eventEmployee.permissions[permission]) eventEmployee.permissions[permission] = true;
	});
}

export async function deletePermissions(eventId: string, userSubject: string, permissions: KeyObject): Promise<void> {
	log.debug('deletePermissions()', arguments);
	await api.deletePermissionsEventEmployee({
		key: {
			eventId, 
			userSubject
		},
		data: {
			permissions
		}
	});
	const eventEmployee = employeesOfEventRef.value.find((eventEmployee) => 
		eventEmployee.eventId == eventId && eventEmployee.userSubject == userSubject);
	if (!eventEmployee) {
		log.debug('addPermissions()', 'Could not find Employee');
		return;
	}
	Object.keys(permissions).forEach((permission) => {
		if (eventEmployee.permissions[permission]) delete eventEmployee.permissions[permission];
	});
}
