<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Verkaufsphasen"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h2>Allgemeines</h2>
      <b-row
        v-for="(field, fieldKey) of inputFields"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col sm="3">
          <label :for="`field-${fieldKey}`">{{ field.name }}:</label>
        </b-col>
        <b-col
          v-if="field.type == 'text'"
          sm="9"
        >
          <b-form-input
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            @keyup.enter="onKeyupEnter()"
          />
        </b-col>
        <b-col
          v-if="field.type == 'datetime'"
          sm="9"
        >
          <DateTimePicker
            :id="`field-${fieldKey}`"
            v-model="field.value"
            format="YYYY-MM-DDThh:mm:ss.SSSZ"
            :no-clear-button="true"
            :no-label="true"
            :no-button-now="true"
            :overlay="true"
          />
        </b-col>
      </b-row>
    </div>
    <LoadButton
      ref="saveButton"
      variant="primary"
      @click="clickOnSave"
    />
  </b-sidebar>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import * as api from '@innevent/webapp-api';
import type { ModalAction, SalesPeriod, ResetButton } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useTicketMapState } from '../../../../states/ticketMap';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';
import { currentEventRef } from '../../../../states/eventState';

export default defineComponent({
	components: {
		LoadButton
	},
	emits: ['salesPeriodChanged'],
	setup(props, { emit }) {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentSalesPeriod = ref<SalesPeriod>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();
		const { ticketMapRef, isLoadingRef } = useTicketMapState();

		async function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function clickOnSave(btn: ResetButton) {
			await saveSalesPeriod();
			btn.reset();
		}

		async function saveSalesPeriod(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					const salesPeriod = await api.createSalesPeriod({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							salesPeriodName: inputFields.value.salesPeriodName.value,
							description: inputFields.value.description.value,
							timeFrom: inputFields.value.timeFrom.value,
							timeTo: inputFields.value.timeTo.value
						}
					});
					ticketMapRef.value.salesPeriods.push(salesPeriod);
					emit('salesPeriodChanged');
				} else {
					if (!currentSalesPeriod.value) throw 'NoValue';
					const salesPeriod = await api.updateSalesPeriod({
						key: {
							eventId: currentSalesPeriod.value?.eventId,
							salesPeriodId: currentSalesPeriod.value?.salesPeriodId
						},
						data: {
							salesPeriodName: inputFields.value.salesPeriodName.value,
							description: inputFields.value.description.value,
							timeFrom: inputFields.value.timeFrom.value,
							timeTo: inputFields.value.timeTo.value
						}
					});
					const index = ticketMapRef.value.salesPeriods.findIndex(element => element.salesPeriodId == salesPeriod.salesPeriodId);
					ticketMapRef.value.salesPeriods.splice(index, 1, salesPeriod);
					emit('salesPeriodChanged');
				}
				notifySuccess({ instance });
				sidebarOpen.value = false;
			} catch (error) {
				if (error.isAxiosError) {
					const axiosError: AxiosError = error;
					const response: InnEventError = axiosError.response?.data;

					if (response.isInnEventError) {
						if (response.errorCode == 'ValidationError') {
							notifyError({ instance, title: 'Validierungsfehler', message: 'Die Felder wurden nicht richtig gefüllt' });
							return;
						}	
					} 
				}
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;
			inputFields.value.salesPeriodName.value = '';
			inputFields.value.description.value = '';
			inputFields.value.timeFrom.value = '';
			inputFields.value.timeTo.value = '';
		}

		function openForEdit(salesPeriod: SalesPeriod) {
			actionRef.value = 'edit';
			sidebarOpen.value = true;
			currentSalesPeriod.value = salesPeriod;
			inputFields.value.salesPeriodName.value = salesPeriod.salesPeriodName;
			inputFields.value.description.value = salesPeriod.description;
			inputFields.value.timeFrom.value = salesPeriod.timeFrom as string;
			inputFields.value.timeTo.value = salesPeriod.timeTo as string;
		}

		const inputFields = ref({
			salesPeriodName: {
				name: 'Name',
				type: 'text',
				value: ''
			},
			description: {
				name: 'Beschreibung',
				type: 'text',
				value: ''
			},
			timeFrom: {
				name: 'Startdatum',
				type: 'datetime',
				value: ''
			},
			timeTo: {
				name: 'Enddatum',
				type: 'datetime',
				value: ''
			}
		});


		return {
			actionRef,
			sidebarOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			openForCreate,
			openForEdit,
			saveSalesPeriod
		};
	}
});
</script>
<style>
</style>