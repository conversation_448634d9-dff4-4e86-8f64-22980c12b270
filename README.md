This monorepository contains some @innevent/*portal an @innevent/webapp-* packages. It's manages by yarn workspaces.

Portale:
- accountportal
- customerportal
- organizerportal
- vendorportal


Weitere Packages:
- webapp-api => enthält alle Netzwerkfunktionen, welche mit dem server kommunizieren
- webapp-components => vue Komponenten
- webapp-types => enthält alle webapp spezifischen types, die globalen types sind unter @innevent/types und werden im innevent-server Git Repo gepflegt
- webapp-utils => enthält nützliche webapp module, wie z.B. notify (Bootstrap Toast)


Git Hooks:
- Add githooks with: git config core.hooksPath .githooks