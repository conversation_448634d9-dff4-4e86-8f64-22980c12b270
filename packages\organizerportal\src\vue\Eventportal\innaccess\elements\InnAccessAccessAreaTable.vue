<template>
  <div>
    <b-row>
      <b-col>
        <h2>Z<PERSON>rittsbereiche</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto mr-2"
            variant="secondary"
            size="sm"
            @click="loadAccessAreas()"
          >
            <FontAwesomeIcon icon="sync" />
          </b-button>
          <b-button
            variant="primary"
            size="sm"
            @click="btnCreateAccessArea()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Neuer Z<PERSON>rittsbereich
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      id="accessAreaTable"
      hover
      :busy="isLoadingRef"
      :items="accessAreasRef"
      :fields="tableFields"
      class="mt-3 table-clickable"
      head-variant="light"
      :show-empty="true"
      empty-text="Keine Zutrittsbereiche vorhanden"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(permittedTickets)="{value}">
        <b-badge
          v-for="(ticketId, key) in Object.keys(value)"
          :key="key"
          variant="secondary"
          class="mr-1"
        >
          {{ getTicketName(ticketId) }}
        </b-badge>
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteAccessArea($event, data)"
          />
        </div>
      </template>
    </b-table>
    <AccessAreaSidebar 
      ref="accessAreaSidebarRef"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import AccessAreaSidebar from './InnAccessAccessAreaSidebar.vue';
import { defineComponent, ref, getCurrentInstance, onMounted } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import { formatDate } from '@innevent/webapp-utils';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BvTableFieldArray } from 'bootstrap-vue';
import { useAccessAreaState, loadAccessAreas, deleteAccessArea } from '../../../../states/accessAreaState';
import type { AccessArea } from '@innevent/types';
import { useTicketMapState, refreshTicketMap } from '../../../../states/';

export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		AccessAreaSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const accessAreaSidebarRef = ref<InstanceType<typeof AccessAreaSidebar>>();
		const { accessAreasRef, isLoadingRef } = useAccessAreaState();
		const { ticketMapRef } = useTicketMapState();

		onMounted(async () => {
			await refreshTicketMap();
		});

		const tableFields: BvTableFieldArray =  [
			{ key: 'accessAreaName', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'code', label: 'Code' },
			{ key: 'permittedTickets', label: 'Berechtigte Tickets' },
			{ key: 'buttons', label: '' }
		];

		async function btnDeleteAccessArea(btn: ResetButton, cellData: BvTableCellData<AccessArea>) {
			try {
				await deleteAccessArea({ key: {
					eventId: cellData.item.eventId, 
					accessAreaId: cellData.item.accessAreaId
				} });
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function getTicketName(ticketId: string) {
			return ticketMapRef.value.tickets.find(ticket => 
				ticket.ticketId == ticketId)?.ticketName ?? 'Ticket nicht gefunden';
		}

		function tableRowClick(accessArea: AccessArea): void {
			const importObj: AccessArea = JSON.parse(JSON.stringify(accessArea));
			accessAreaSidebarRef.value?.openForEdit(importObj);
		}

		function btnCreateAccessArea(): void {
			accessAreaSidebarRef.value?.openForCreate();
		}
		

		return {
			tableFields,
			accessAreaSidebarRef,
			accessAreasRef,
			btnDeleteAccessArea,
			tableRowClick,
			btnCreateAccessArea,
			formatDate,
			loadAccessAreas,
			isLoadingRef,
			getTicketName
		};
	}
});
</script>

<style>
</style>
