<template>
  <div>
    <b-row>
      <b-col>
        <h2>Ticketimports</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto mr-2"
            variant="secondary"
            size="sm"
            @click="refreshTicketImportMap()"
          >
            <FontAwesomeIcon icon="sync" />
          </b-button>
          <b-button
            variant="primary"
            size="sm"
            @click="btnCreateTicketImport()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Neuer Ticketimport
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      id="ticketImportTable"
      hover
      :busy="isLoadingRef"
      :items="ticketImportRef"
      :fields="tableFields"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteImport($event, data)"
          />
        </div>
      </template>
    </b-table>
    <ImportSidebar 
      ref="importSidebarRef"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import ImportSidebar from './InnticketImportSidebar.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import type { TicketImport }  from '@innevent/types';
import { formatDate } from '@innevent/webapp-utils';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BvTableFieldArray } from 'bootstrap-vue';
import { useTicketImportMap, refreshTicketImportMap, deleteTicketImport } from '../../../../states/ticketImport';


export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		ImportSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const importSidebarRef = ref<InstanceType<typeof ImportSidebar>>();
		const { ticketImportRef, isLoadingRef } = useTicketImportMap();

		const tableFields: BvTableFieldArray =  [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'buttons', label: '' }
		];

		async function btnDeleteImport(btn: ResetButton, cellData: BvTableCellData<TicketImport>) {
			try {
				await deleteTicketImport(cellData.item.ticketImportId);
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(ticketImport: TicketImport): void {
			const importObj: TicketImport = JSON.parse(JSON.stringify(ticketImport));
			importSidebarRef.value?.openForEdit(importObj);
		}

		function btnCreateTicketImport(): void {
			importSidebarRef.value?.openForCreate();
		}
		

		return {
			tableFields,
			importSidebarRef,
			ticketImportRef,
			btnDeleteImport,
			tableRowClick,
			btnCreateTicketImport,
			formatDate,
			refreshTicketImportMap,
			isLoadingRef
		};
	}
});
</script>

<style>
</style>
