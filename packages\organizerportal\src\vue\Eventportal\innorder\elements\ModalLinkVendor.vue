<template>
  <b-modal
    id="modalLinkVendor"
    ref="modalLinkVendor"
    title="Verkäufer Verlinken"
    size="lg"
    hide-footer
  >
    <b-list-group>
      <b-list-group-item
        v-for="vendor in vendors"
        :key="vendor.id"
        button
        @click="linkVendorToEvent(vendor)"
      >
        {{ vendor.name }}
      </b-list-group-item>
    </b-list-group>
  </b-modal>
</template>
<script>

// Frameworks
import { API } from 'aws-amplify';


export default {
	components: {  },
	props: ['linkedVendors', 'eventid'],
	data() { return {
		vendors: {}
	};},
	computed: {
		
	},
	created() {
	},
	mounted() {
		this.$root.$on('bv::modal::show', (bvEvent, modalId) => {
			this.loadVendors();
		});
	},
	methods: {
		linkVendorToEvent(vendor) {
			const vendorEventProperty = {
				nextReceiptNumber: 1,
				allowOrganizerAccess: 0,
				VendorId: vendor.id,
				EventId: this.eventid
			};
			API.post('innorder', '/VendorEventProperty', { body: vendorEventProperty }).then(response  => {
				this.$bvToast.toast('Die Verlinkung wurde erfolgreich erstellt', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				this.$emit('vendorLinked');
				this.$bvModal.hide('modalLinkVendor');
			}).catch(error => {
				this.$bvToast.toast('Fehler bei der Verlinkung. Error: ' + JSON.stringify(error), {
					title: 'Ändern Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				this.$bvModal.hide('modalLinkVendor');
			});
		},
		loadVendors() {
			API.get('innorder', '/Vendor').then(response => {
				//Extract Vendor from VendorEventProp
				const linkedVendorIds = this.linkedVendors.map(prop => prop.Vendor.id);
				this.vendors = response.filter(vendor => !linkedVendorIds.includes(vendor.id));
				
			}).catch(error => {
				this.$bvToast.toast('Beim Laden der Vendors ist ein Fehler aufgetreten.', {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		}
	}
};

</script>
