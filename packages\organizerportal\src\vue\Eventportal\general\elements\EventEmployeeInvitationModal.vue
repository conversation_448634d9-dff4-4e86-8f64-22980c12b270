<template>
  <b-modal
    id="eventEmployeeInvitationModal"
    ref="eventEmployeeInvitationModalRef"
    title="Mitarbeitereinladung"
    centered
  >
    <div role="group">
      <label for="inviteEmail">E-Mail Adresse:</label>
      <b-form-input
        id="inviteEmail"
        v-model="invitation.email"
        :state="inviteEmailState"
        aria-describedby="inviteEmail-help inviteEmail-feedback"
        placeholder="<EMAIL>"
      />
      <b-form-invalid-feedback
        id="inviteEmail-feedback"
      >
        Die Email Adresse ist ungültig
      </b-form-invalid-feedback>
      <b-form-text
        id="inviteEmail-help"
      >
        Bitte die Emailadresse der einzuladenden Person eingeben
      </b-form-text>
    </div>
    <div
      role="group"
      class="mt-3"
    >
      <label for="inviteText">Nachricht (optional):</label>
      <b-form-textarea
        id="inviteText"
        v-model="invitation.message"
        placeholder="Gib eine kurze Nachricht ein..."
        rows="3"
        max-rows="6"
      />
    </div>
    <template #modal-footer>
      <div class="d-flex">
        <LoadButton
          class="ml-auto"
          text="Einladen"
          icon="plus-square"
          :disabled="!inviteEmailState"
          @click="btnCreateInvitation"
        />
      </div>
    </template>
  </b-modal>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import { useEventEmployeeInvitationState, createEventEmployeeInvitation } from '../../../../states/eventEmployeeInvitationState';
import type { BModal } from 'bootstrap-vue';
import type { EventEmployeeInvitation } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import isEmail from 'validator/lib/isEmail';
import LoadButton from '../../../buttons/LoadButton.vue';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const instance = getCurrentInstance();
		const { eventEmployeeInvitationsRef, isLoadingRef } = useEventEmployeeInvitationState();
		const eventEmployeeInvitationModalRef = ref<BModal>();
		const invitation = ref<Pick<EventEmployeeInvitation, 'email' | 'message'>>({
			email: '',
			message: ''
		});

		const inviteEmailState = computed(() =>{
			return isEmail(invitation.value?.email ?? '');
		});

		function openModal() {
			eventEmployeeInvitationModalRef.value?.show();
		}

		async function btnCreateInvitation(btn) {
			try {
				await createEventEmployeeInvitation(invitation.value);
				invitation.value = { email: '', message: '' };
				eventEmployeeInvitationModalRef.value?.hide();
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			eventEmployeeInvitationModalRef,
			eventEmployeeInvitationsRef,
			isLoadingRef,
			btnCreateInvitation,
			openModal,
			invitation,
			inviteEmailState
		};
	}
});
</script>