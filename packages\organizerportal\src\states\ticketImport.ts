/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import type { TicketImport }  from '@innevent/types';

import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { currentEventRef } from './eventState';

const ticketImportRef = ref<TicketImport[]>([]);
const isLoadingRef = ref<boolean>(false);
    
export function useTicketImportMap() {
	return {
		ticketImportRef,
		isLoadingRef
	};
}

export async function refreshTicketImportMap(): Promise<void> {
	isLoadingRef.value = true;
	ticketImportRef.value =  await api.getTicketImports({ 
		key: {
			eventId: currentEventRef.value!.eventId!
		}
	});
	isLoadingRef.value = false;
}

export async function deleteTicketImport(ticketImportId: string): Promise<void> {
	await api.deleteTicketImport({
		key: {
			eventId: currentEventRef.value!.eventId!,
			ticketImportId
		}
	});
	ticketImportRef.value = ticketImportRef.value.filter(ticketImport => ticketImport.ticketImportId !== ticketImportId);
}

export async function createTicketImport(options: api.TicketImportCreateOptions): Promise<TicketImport> {
	const createdTicketImport = await api.createTicketImport(options);
	ticketImportRef.value.push(createdTicketImport);
	return createdTicketImport;
}