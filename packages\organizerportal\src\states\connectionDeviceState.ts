import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import type { ConnectionDevice } from '@innevent/types';
import { currentEventRef } from './eventState';

const isLoadingRef = ref<boolean>(false);
const connectionDevicesRef = ref<Partial<ConnectionDevice>[]>([]);
    
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useConnectionDeviceState() {
	return {
		connectionDevicesRef,
		isLoadingRef
	};
}

export async function loadConnectionDevices(): Promise<void> {
	isLoadingRef.value = true;
	connectionDevicesRef.value =  await api.getConnectionDevices({ 
		key: {
			eventId: currentEventRef.value!.eventId!
		}
	});
	isLoadingRef.value = false;
}