<template>
  <div>
    <b-sidebar
      v-if="currentEcTerminalRef"
      v-model="sidebarOpenRef"
      sidebar-class="top-fixed-header"
      body-class="p-3"
      z-index="1"
      :title="currentEcTerminalRef ? currentEcTerminalRef.name : ''"
      width="1000px"
      backdrop
      right
    >
      <h3>Allgemein</h3>
      <label for="ecTerminalInputName">Name</label>
      <b-form-input
        id="ecTerminalInputName"
        v-model="currentEcTerminalRef.name"
        @keyup.enter="onKeyupEnter()"
      />
      <label
        for="ecTerminalInputDescription"
        class="mt-2"
      >Beschreibung</label>
      <b-form-input
        id="ecTerminalInputDescription"
        v-model="currentEcTerminalRef.description"
        @keyup.enter="onKeyupEnter()"
      />
      <LoadButton
        ref="saveButtonRef"
        size="sm"
        icon="save"
        class="mt-3"
        @click="btnSaveEcTerminal($event)"
      />
      <b-row>
        <b-col>
          <h4 class="mt-3">
            Zugewiesene Mitarbeiter
          </h4>
          <p v-if="ecTerminalEmployeesRef.length == 0">
            Derzeit sind keine Mitarbeiter zugeordnet.
          </p>
          <b-list-group v-else>
            <b-list-group-item
              v-for="employee of ecTerminalEmployeesRef"
              :key="employee.userSubject"
              class="d-flex"
            >
              {{ employee.firstName + " " + employee.lastName }}
              <LoadButton
                text=""
                icon="arrow-circle-right"
                size="sm"
                class="ml-auto"
                @click="btnRmoveEcTerminalEmployeeAssign($event, employee)"
              />
            </b-list-group-item>
          </b-list-group>
        </b-col>
        <b-col>
          <h4 class="mt-3">
            Verfügbare Mitarbeiter
          </h4>
          <b-list-group>
            <b-list-group-item
              v-for="employee of unassignedEmployees"
              :key="employee.userSubject"
            >
              <LoadButton
                text=""
                icon="arrow-circle-left"
                size="sm"
                @click="btnAddEcTerminalEmployeeAssign($event, employee)"
              />
              {{ employee.firstName + " " + employee.lastName }}
            </b-list-group-item>
          </b-list-group>
        </b-col>
      </b-row>
      <b-row class="mt-5">
        <b-col><h3>Transaktionen</h3></b-col>
        <b-col class="d-flex">
          <LoadButton
            class="ml-auto"
            style="height: fit-content"
            size="sm"
            text="Transaktion erstellen"
            icon="plus-square"
            @click="createEcTerminalTransactionModal"
          />
        </b-col>
      </b-row>
      <b-table
        ref="ecTerminalTransactionTableRef"
        :items="ecTerminalTransactionsRef"
        :fields="tableFields"
        :busy="isLoadingEcTerminalTransactionsRef"
        sort-by="createdOn"
        :sort-desc="true"
        class="mt-3"
        striped
        hover
        head-variant="light"
        :per-page="paginationTransactions.perPage"
        :current-page="paginationTransactions.currentPage"
      >
        <template #table-busy>
          <TableBusyLoader />
        </template>
        <template #cell(employee)="data">
          {{ data.value.firstName + " " + data.value.lastName }}
        </template>
        <template #cell(createdOn)="data">
          {{ formatDate(data.value) }}
        </template>
        <template #cell(creditBefore)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
        <template #cell(creditChange)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
        <template #cell(creditAfter)="data">
          {{ data.value.toFixed(2) + ' €' }}
        </template>
      </b-table>
      <b-pagination
        v-model="paginationTransactions.currentPage"
        :per-page="paginationTransactions.perPage"
        :total-rows="paginationTransactions.totalRows"
        align="center"
      />
    </b-sidebar>
    <EcTerminalTransactionModal
      ref="ecTerminalTransactionModalRef"
    />
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref } from '@vue/composition-api';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useEcTerminalState } from '../../../../states';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import LoadButton from '../../../buttons/LoadButton.vue';
import type { ModalAction, ResetButton } from '@innevent/webapp-types';
import type { EcTerminal, EcTerminalTransaction, ShortEmployee } from '@innevent/types';
import { useEmployeeState, loadEventEmployeesOfEvent } from '../../../../states/employeeState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { currentEventRef } from '../../../../states/eventState';
import EcTerminalTransactionModal from './EcTerminalTransactionModal.vue';
import { ecterminal as api } from '@innevent/webapp-api';
import { formatDate } from '@innevent/webapp-utils';

export default defineComponent({
	components: {
		TableBusyLoader,
		LoadButton,
		EcTerminalTransactionModal
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpenRef = ref<boolean>(false);
		const ecTerminalTransactionModalRef = ref<InstanceType<typeof EcTerminalTransactionModal>>();
		const instance = getCurrentInstance();
		const { isLoadingRef, updateEcTerminal } = useEcTerminalState();
		const { employeesOfEventRef } = useEmployeeState();
		const ecTerminalTransactionTableRef = ref<BTable>();
		const currentEcTerminalRef = ref<EcTerminal>();
		const saveButtonRef = ref<InstanceType<typeof LoadButton>>();
		const ecTerminalTransactionsRef = ref<EcTerminalTransaction[]>([]);
		const ecTerminalEmployeesRef = ref<ShortEmployee[]>([]);
		const isLoadingEcTerminalTransactionsRef = ref<boolean>(true);


		const tableFields: BvTableFieldArray =  [
			{ key: 'createdOn', label: 'Zeitpunkt', sortable: true },
			{ key: 'creditBefore', label: 'Betrag vorher' },
			{ key: 'creditChange', label: 'Betrag', sortable: true },
			{ key: 'creditAfter', label: 'Betrag nachher' },
			{ key: 'comment', label: 'Kommentar' },
			{ key: 'employee', label: 'Mitarbeiter' }
		];

		const paginationTransactions = ref({
			totalRows: ecTerminalTransactionsRef.value.length,
			perPage: 5,
			currentPage: 1
		});


		const unassignedEmployees = computed(()=> {
			return employeesOfEventRef.value.filter((employee) => {
				return !ecTerminalEmployeesRef.value.some((ecTerminalEmployee) => ecTerminalEmployee.userSubject == employee.userSubject);
			});
		});
		
		async function openForEdit(ecTerminal: EcTerminal) {
			actionRef.value = 'edit';
			currentEcTerminalRef.value = ecTerminal;
			sidebarOpenRef.value = true;

			try {
				const transactions = await api.listTransactions({
					key: {
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId!
					}
				});
				ecTerminalTransactionsRef.value = transactions.items;
				isLoadingEcTerminalTransactionsRef.value = false;

				ecTerminalEmployeesRef.value = await api.listEmployees({
					key: {
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId!
					}
				});

				await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
				paginationTransactions.value.totalRows = ecTerminalTransactionsRef.value.length;	
			} catch (error: any) {
				notifyError({ instance, error });
			}
			
		}

		function onKeyupEnter() {
			saveButtonRef.value?.clickButton();
		}

		function createEcTerminalTransactionModal(btn: ResetButton) {
			const ecTerminalObj = JSON.parse(JSON.stringify(currentEcTerminalRef.value));
			ecTerminalTransactionModalRef.value?.openForCreate(ecTerminalObj, ecTerminalTransactionsRef);
			btn.reset();
		}

		async function btnSaveEcTerminal(btn: ResetButton) {
			try {
				const ecTerminal = currentEcTerminalRef.value!;
				await updateEcTerminal({
					key: {
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId!
					},
					data: {
						name: ecTerminal.name,
						description: ecTerminal.description
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnRmoveEcTerminalEmployeeAssign(btn: ResetButton, employee: ShortEmployee) {
			try {
				const ecTerminal = currentEcTerminalRef.value!;
				await api.removeEmployee({
					key: {
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId!
					},
					data: {
						[employee.userSubject]: true
					}
				});
				ecTerminalEmployeesRef.value = ecTerminalEmployeesRef.value.filter((employeeFilter) => 
					employee.userSubject !== employeeFilter.userSubject);
				
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnAddEcTerminalEmployeeAssign(btn: ResetButton, employee: ShortEmployee) {
			try {
				const ecTerminal = currentEcTerminalRef.value!;
				await api.addEmployee({
					key: {
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId!
					},
					data: {
						[employee.userSubject]: true
					}
				});

				const addedEmployee = employeesOfEventRef.value.find((employeeFind) => employeeFind.userSubject == employee.userSubject);
				if (addedEmployee) ecTerminalEmployeesRef.value.push(addedEmployee);
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}
		
		return {
			ecTerminalTransactionTableRef,
			isLoadingRef, 
			openForEdit,
			sidebarOpenRef,
			currentEcTerminalRef,
			btnSaveEcTerminal,
			onKeyupEnter,
			saveButtonRef,
			ecTerminalTransactionsRef,
			ecTerminalEmployeesRef,
			btnRmoveEcTerminalEmployeeAssign,
			btnAddEcTerminalEmployeeAssign,
			unassignedEmployees,
			tableFields,
			ecTerminalTransactionModalRef,
			createEcTerminalTransactionModal,
			paginationTransactions,
			formatDate,
			isLoadingEcTerminalTransactionsRef
		};
	}
});
</script>

<style>
</style>
