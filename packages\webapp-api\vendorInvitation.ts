import { apiInnOrder } from './instances';
import type { VendorInvitation } from '@innevent/webapp-types';

type ModelType = VendorInvitation;
type PrimaryKey = 'id';


export type VendorInvitationCreateOptions = {
	data: Pick<ModelType, 'mail' | 'status' | 'EventId'> & Partial<Pick<ModelType, 'message'>>;
}

export async function createVendorInvitation(options: VendorInvitationCreateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).post('/VendorInvitation', options.data);
	const newVendorInvitation = {
		id: response.data,
		...options.data
	} as VendorInvitation;
	
	return newVendorInvitation;
}

export type VendorInvitationDeleteOptions = {
	key: Pick<ModelType, PrimaryKey>;
}
export async function deleteVendorInvitation(options: VendorInvitationDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/VendorInvitation/${options.key.id}`);
}

export type VendorInvitationGetOptions =  {
    key: Pick<ModelType, 'EventId'>;
};
export async function getVendorInvitations(options: VendorInvitationGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnOrder()).get(`/VendorInvitation?filterEvent=${options.key.EventId}`);
	return response.data;
}