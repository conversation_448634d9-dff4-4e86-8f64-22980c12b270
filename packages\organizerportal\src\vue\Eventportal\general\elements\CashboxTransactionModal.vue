<template>
  <b-modal
    v-if="cashboxTransactionRef"
    id="cashboxTransactionModalRef"
    v-model="modalOpenRef"
    title="Transaktion hinzufügen"
  >
    <div role="group">
      <label for="input1">Betrag:</label>
      <b-input-group
        id="input1"
        append="€"
      >
        <b-form-input
          v-model.number="cashboxTransactionRef.creditChange"
          type="number"
          @keyup.enter="onKeyupEnter()"
        />
      </b-input-group>
    </div>
    <div
      role="group"
      class="mt-3"
    >
      <label for="input2">Kommentar:</label>
      <b-form-input
        id="input2"
        v-model="cashboxTransactionRef.comment"
        :state="cashboxTransactionRef.comment.length > 2"
        aria-describedby="input2feedback"
        placeholder="Kommentar"
        trim
        @keyup.enter="onKeyupEnter()"
      />
      <b-form-invalid-feedback id="input2feedback">
        Es werden mindestens 3 Buchstaben zur Beschreibung erwartet
      </b-form-invalid-feedback>
    </div>
    <template #modal-footer="{ hide }">
      <LoadButton
        ref="createTransactionButtonRef"
        variant="primary"
        size="sm"
        icon="save"
        text="Transaktion erstellen"
        @click="btnCreateCashboxTransaction($event, hide)"
      />
    </template>
  </b-modal>
</template>

<script lang="ts">
import type { Ref } from '@vue/composition-api';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import { currentEventRef, useCashboxState } from '../../../../states';
import LoadButton from '../../../buttons/LoadButton.vue';
import type { ResetButton, ModalAction } from '@innevent/webapp-types';
import type { Cashbox, CashboxTransaction } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { cashbox as api } from '@innevent/webapp-api';

export default defineComponent({
	components: {
		LoadButton
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const instance = getCurrentInstance();
		const modalOpenRef = ref<boolean>(false);
		const createTransactionButtonRef = ref<InstanceType<typeof LoadButton>>();
		const currentCashbox = ref<Cashbox>();
		const { loadCashboxes } = useCashboxState();
		let cashboxTransactionsRef = ref<CashboxTransaction[]>([]);
		const cashboxTransactionRef = ref<Partial<CashboxTransaction>>({
			comment: '',
			creditChange: 0
		});
		
		function onKeyupEnter() {
			createTransactionButtonRef.value?.clickButton();
		}

		function openForCreate(cashbox: Cashbox, transactionRef: Ref<CashboxTransaction[]>) {
			actionRef.value = 'create';
			cashboxTransactionRef.value!.comment = '';
			cashboxTransactionRef.value!.creditChange = 0;
			currentCashbox.value = cashbox;
			modalOpenRef.value = true;
			cashboxTransactionsRef = transactionRef;
		}
		
		async function btnCreateCashboxTransaction(btn: ResetButton) {
			try {
				const cashboxTransaction = await api.addTransaction({
					key: {
						cashboxId: currentCashbox.value!.cashboxId!,
						eventId: currentCashbox.value!.eventId!
					},
					data: {
						creditChange: cashboxTransactionRef.value!.creditChange!,
						comment: cashboxTransactionRef.value!.comment!
					}
				});
				cashboxTransactionsRef.value.push(cashboxTransaction);
				await loadCashboxes({
					key: {
						eventId: currentEventRef.value!.eventId!
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				console.log(error);
				notifyError({ instance, error });
			}
			btn.reset();
			modalOpenRef.value = false;
		}
	

		return {
			onKeyupEnter,
			modalOpenRef,
			openForCreate,
			cashboxTransactionRef,
			btnCreateCashboxTransaction,
			createTransactionButtonRef
		};
	}
});
</script>
