<template>
  <div>
    <button
      v-if="state == 'created'"
      type="button"
      class="btn btn-primary float-right"
      @click="save"
    >
      {{ text || 'Speichern' }}
    </button>
    <button
      v-if="state == 'loading'"
      type="button"
      class="btn btn-primary float-right"
      disabled
    >
      <span class="spinner-border spinner-border-sm" />
      Loading...
    </button>
    <button
      v-if="state == 'success'"
      type="button"
      class="btn btn-success float-right"
      @click="save"
    >
      <FontAwesomeIcon icon="check" />
      Erfolgreich
    </button>
    <button
      v-if="state == 'error'"
      type="button"
      class="btn btn-danger float-right"
      @click="save"
    >
      <FontAwesomeIcon icon="exclamation-triangle" />
      Es ist ein Fehler aufgetreten! <span v-if="error">({{ error }})</span>
    </button>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export default {
	components: { FontAwesomeIcon },
	props: {
		text: {
			type: String,
			default: 'Speichern'
		}
	},
	emits: ['click'],
	data() {
		return {
			error: null,
			state: 'created'
		};
	},
	methods: {
		save() {
			this.state = 'loading';
			this.$emit('click', this);
		},
		async setSuccess() {
			this.state = 'success';
			await sleep(2000);
			this.state = 'created';
		},
		async setError(error) {
			this.error = error;
			this.state = 'error';
			await sleep(5000);
			this.state = 'created';
			this.error = null;
		}
	}
};
</script>
