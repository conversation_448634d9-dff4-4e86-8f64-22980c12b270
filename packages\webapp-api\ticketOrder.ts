import type { InnEventQueryOptions, InnEventQueryResult, TicketOrder, TicketOrderCreate } from '@innevent/types';
import { apiInnTicket } from './instances';

type ModelType = TicketOrder;
type PrimaryKey = 'ticketOrderId';


export type TicketOrderCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: TicketOrderCreate;
}
export async function createTicketOrder(options: TicketOrderCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/TicketOrder', options.data, { 
		params: { 'eventId': options.key.eventId, 'returnValue': 'ALL_NEW'  } 
	});
	return response.data;
}


export type TicketOrderUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Partial<Pick<ModelType, 'status'>>;
};

export async function updateTicketOrder(options: TicketOrderUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/TicketOrder/${options.key.ticketOrderId}`, 
		{ status: options.data.status }, 
		{ params: { 'eventId': options.key.eventId, 'returnValue': 'ALL_NEW' } }
	);
	return response.data;
}


export type TicketOrderGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
	queryParameter?: InnEventQueryOptions<ModelType>;
};
export async function getTicketOrders(options: TicketOrderGetOptions): Promise<InnEventQueryResult<TicketOrder>> {
	const response = await (await apiInnTicket()).get('/TicketOrder', { 
		params: { 'eventId': options.key.eventId, ...options.queryParameter } 
	});
	return response.data;
}