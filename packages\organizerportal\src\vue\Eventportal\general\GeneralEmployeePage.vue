<template>
  <b-container fluid>
    <EventEmployeeTable />
    <TechnicalUserTable
      ref="technicalUserTable"
      :reference-object-id="currentEventRef.eventId"
      reference-object-type="event"
      class="mt-4"
      @technicalUserChanged="onTechnicalUserChange"
    />
    <EventEmployeeInvitationTable class="mt-4" />
  </b-container>
</template>
<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { currentEventRef } from '../../../states/eventState';
import { loadEventEmployeesOfEvent } from '../../../states/employeeState';
import { loadEventEmployeeInvitations } from '../../../states/eventEmployeeInvitationState';

import EventEmployeeTable from './elements/EventEmployeeTable.vue';
import TechnicalUserTable from '@innevent/webapp-components/user/TechnicalUserTable.vue';
import EventEmployeeInvitationTable from './elements/EventEmployeeInvitationTable.vue';
export default defineComponent({
	components: {
		EventEmployeeTable,
		TechnicalUserTable,
		EventEmployeeInvitationTable
	},
	setup() {
		onMounted(async () => {
			await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
			await loadEventEmployeeInvitations(currentEventRef.value!.eventId!);
		});

		async function onTechnicalUserChange() {
			await loadEventEmployeesOfEvent(currentEventRef.value!.eventId!);
		}

		return { currentEventRef, onTechnicalUserChange };
	}
});
</script>

<style>
#sidebarEventStaff {
  position: absolute;
  top: 66px;
}
</style>
