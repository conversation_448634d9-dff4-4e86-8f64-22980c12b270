import type { KeyObject, Cashbox, CashboxTransaction, InnEventQueryResult, ShortEmployee, InnEventQueryOptions, PartialPick } from '@innevent/types';
import { apiInnEvent } from './instances';
import type { ReferenceObject } from '@innevent/webapp-types';

type ModelType = Cashbox;
type PrimaryKey = 'cashboxId';

export type CashboxesListOptions =  {
    key: ReferenceObject;
};
export async function listCashboxes(options: CashboxesListOptions): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/Cashbox', { 
		params: { 
			eventId: options.key.eventId, 
			vendorId: options.key.vendorId
		} 
	});
	return response.data;
}

export type CashboxCreateOptions = {
	key: ReferenceObject;
	data: PartialPick<ModelType, 'name' | 'description'>;
}
export async function createCashbox(options: CashboxCreateOptions): Promise<Required<ModelType>> {
	const response = await (await apiInnEvent()).post('/Cashbox', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type CashboxDeleteOptions =  {
    key: Pick<ModelType, PrimaryKey> & ReferenceObject;
};
export async function deleteCashbox(options: CashboxDeleteOptions): Promise<void> {
	await (await apiInnEvent()).delete(`/Cashbox/${options.key.cashboxId}`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId
		} 
	});
}

export type CashboxUpdateOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: PartialPick<ModelType, 'name' | 'description'>;
}
export async function updateCashbox(options: CashboxUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/Cashbox/${options.key.cashboxId}`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type CashboxListTransactionsOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	queryParameter?: InnEventQueryOptions<ModelType>;
}
export async function listTransactions(options: CashboxListTransactionsOptions): Promise<InnEventQueryResult<CashboxTransaction>> {
	const response = await (await apiInnEvent()).get(`/Cashbox/${options.key.cashboxId}/Transaction`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			...options.queryParameter,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type CashboxAddTransactionOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: Pick<CashboxTransaction, 'comment' | 'creditChange'>;
}
export async function addTransaction(options: CashboxAddTransactionOptions): Promise<CashboxTransaction> {
	const response = await (await apiInnEvent()).post(`/Cashbox/${options.key.cashboxId}/Transaction`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}


export type CashboxListEmployeesOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
}
export async function listEmployees(options: CashboxListEmployeesOptions): Promise<ShortEmployee[]> {
	const response = await (await apiInnEvent()).get(`/Cashbox/${options.key.cashboxId}/Employee`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type CashboxAddEmployeeOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: KeyObject;
}
export async function addEmployee(options: CashboxAddEmployeeOptions): Promise<ShortEmployee> {
	const response = await (await apiInnEvent()).post(`/Cashbox/${options.key.cashboxId}/Employee`, options.data, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type CashboxRemoveEmployeeOptions = {
	key: Pick<ModelType, PrimaryKey> & ReferenceObject;
	data: KeyObject;
}
export async function removeEmployee(options: CashboxRemoveEmployeeOptions): Promise<ShortEmployee> {
	const response = await (await apiInnEvent()).delete(`/Cashbox/${options.key.cashboxId}/Employee`, { 
		params: { 
			eventId: options.key.eventId,
			vendorId: options.key.vendorId,
			returnValue: 'ALL_NEW'  
		},
		data: options.data
	});
	return response.data;
}