<template>
  <div>
    <b-row>
      <b-col><h2>Pfandgruppen</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="Pfandgruppe erstellen"
          icon="plus-square"
          @click="btnCreateDepositGroup"
        />
      </b-col>
    </b-row>
    <b-table
      hover
      :busy="isLoadingInitialRef"
      :items="depositGroupsRef"
      :fields="tableFields"
      head-variant="light"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell()="data">
        <b-form-input
          v-model="data.item[data.field.key]"
          @input="dataChange(data)"
        />
      </template>
      <template #cell(costGross)="data">
        <b-input-group
          append="€"
          style="width: 130px"
        >
          <b-form-input
            v-model="data.item[data.field.key]"
            @input="dataChange(data)"
          />
        </b-input-group>
      </template>
      <template #cell(vat)="data">
        <b-input-group
          append="%"
          style="width: 100px"
        >
          <b-form-input
            v-model="data.item[data.field.key]"
            @input="dataChange(data)"
          />
        </b-input-group>
      </template>
      <template #cell(active)="data">
        <b-form-checkbox
          v-model="data.item[data.field.key]"
          switch
          size="lg"
          @input="dataChange(data)"
        />
      </template>
      <!-- Buttons -->
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <b-button-group>
            <LoadButton
              v-if="data.item.isChanged"
              :text="Speichern"
              size="sm"
              @click="btnUpdateDepositGroup($event, data.item)"
            />
            <DeleteButton
              size="sm"
              icon="trash"
              text="Entfernen"
              variant="danger"
              @click="btnDeleteDepositGroup($event, data.item)"
            />
          </b-button-group>
        </div>
      </template>
    </b-table>
  </div>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted } from '@vue/composition-api';
import LoadButton from '../../buttons/LoadButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
import { currentEventRef } from '../../../states/eventState';
import { useTokenstationState } from '../../../states/depositGroupState';
import type { BvTableFieldArray } from 'bootstrap-vue';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import Vue from 'vue';
import type { DepositGroup, ResetButton } from '@innevent/webapp-types';

export default defineComponent({
	components: { 
		DeleteButton, 
		LoadButton
	},
	setup() {
		const instance = getCurrentInstance();
		const { isLoadingInitialRef, depositGroupsRef, createDepositGroup, loadDepositGroups, 
			updateDepositGroup, deleteDepositGroup } = useTokenstationState();
		const depositGroupList = [];
		const tableFields: BvTableFieldArray =  [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'active', label: 'Aktiv', sortable: false },
			{ key: 'costGross', label: 'Betrag', sortable: false },
			{ key: 'vat', label: 'Steuern', sortable: false },
			{ key: 'buttons', label: '' }
		];

		onMounted(async ()  => {
			try {
				await loadDepositGroups(currentEventRef.value!.eventId!);
			} catch (error: any) {
				notifyError({ instance, error });
			}			
		});

		function dataChange(data) {
			Vue.set(depositGroupsRef.value[data.index], 'isChanged', true);
		}

		async function btnCreateDepositGroup(btn: ResetButton) {
			try {
				const depositGroup: DepositGroup = {
					name: 'neue Pfandgruppe',
					active: true,
					costGross: '2.00',
					vat: 19,
					EventId: currentEventRef.value!.eventId!
				};
				await createDepositGroup({
					data: depositGroup
				});
				notifySuccess({ instance, message: 'Pfandgruppe erstellt!' });
			} catch (error: any) {
				notifyError({ instance, error });
			}	
			btn.reset();
		}

		async function btnDeleteDepositGroup(btn: ResetButton, depositGroup: DepositGroup) {
			try {
				await deleteDepositGroup({
					key: {
						id: depositGroup.id
					}
				});
				notifySuccess({ instance, message: 'Pfandgruppe erstellt!' });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}


		async function btnUpdateDepositGroup(btn: ResetButton, depositGroup: any) {
			try {
				await updateDepositGroup({
					key: {
						id: depositGroup.id
					},
					data: depositGroup
				});
				Vue.delete(depositGroup, 'isChanged');
				notifySuccess({ instance, message: 'Pfandgruppe geändert!' });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			tableFields,
			isLoadingInitialRef,
			btnDeleteDepositGroup,
			btnCreateDepositGroup,
			depositGroupsRef,
			dataChange,
			btnUpdateDepositGroup
		};
	}
});
</script>
<style scoped>

</style>
