import type { TicketOrderType } from '.';
import type { ChangeHistoryMap, Currency } from '../common';
import type { PermissionRedemptionGroup } from '../permissions';
import type { ShortEmployee } from './innevent';
import type { VatPrice } from './innticket/TicketOrder';

export type TicketGroup = {
    eventId: string;
    ticketGroupId: string;
    ticketGroupName: string;
}

export type Ticket = {
    eventId: string;
    ticketId: string;
    ticketName: string;
    description: string;
    personalizationRequired: boolean;
    ticketGroups: {
        [ticketGroupId: string]: true; // TODo SET & GET TYPES
    };
    ticketSaleProperties: {
        [salesPeriodId: string]: TicketSaleProperty;
    };
    ticketCodePrefix: string;
    changeHistory: ChangeHistoryMap;
    numberOfActiveGeneratedTickets: number;
}

export type ShortTicket = Pick<Ticket, 'ticketId' | 'ticketName'>

export type TicketSaleProperty = {
    salesPeriodId: string;
    contingent: number | null;
    numberOfActiveGeneratedTickets: number;
    visible: VisibleType;
    price: Pick<VatPrice, 'priceGross' | 'vatPercent'>;
}

export type VisibleType = 'SHOW' | 'HIDE' | 'SHOW_IF_SALESPERIOD'

export type SalesPeriod = {
    eventId: string;
    description: string;
    salesPeriodId: string;
    salesPeriodName: string;
    timeFrom: Date | string;
    timeTo: Date | string;
    changeHistory: ChangeHistoryMap;
}

// 
// Generate & Manage Orders
// 

export type GeneratedTicketStatus = 'generated' | 'cancelled'; 
export type GeneratedTicket = {
    changeHistory: ChangeHistoryMap;
    code: string;
    eventId: string;
    generatedTicketId: string; // not the same as ticketcode, because of import
    generatedTicketName: string; // ticketName from Import (or InnTicket)
    personalization: {
        firstName: string;
        lastName: string;
    };
    price: VatPrice;
    currency: Currency;
    redemptions: {
        [redemptionGroupId: string]: Redemption;
    };
    status: GeneratedTicketStatus;
    salesPeriodId: string; 
    ticket: Pick<Ticket, 'ticketId' | 'ticketName' | 'description'>; // ticketName from InnTicket
    ticketOrderType: TicketOrderType;
    ticketOrderId: string; // Index for OrderView - PartitionKey
    ticketOrderPositionKey: string; // Index for OrderView - SortKey
    updatedOn: string; // ISO Date for AppSync, Index
}

export type GeneratedTicketSync = Pick<GeneratedTicket, 'code' | 'updatedOn' | 'eventId' | 'ticket' | 'status' | 
    'redemptions' | 'price' | 'personalization' | 'generatedTicketId' | 'generatedTicketName' | 'currency'>

// 
// Redemption
// 

export type Redemption = {
    eventEmployee: ShortEmployee;
    redemptionGroup: ShortRedemptionGroup;
    createdOn: string;
}

export type RedemptionMethod = 'REDEEM' | 'REDEEM_CONNECT';
export type RedemptionGroup = {
    eventId: string;
    redemptionGroupId: string;
    redemptionGroupName: string;
    description: string;
    method: RedemptionMethod;
    employeePermissions: {
        [eventEmployeeSubject: string]: {
            [ permission in PermissionRedemptionGroup]?: true;
        };
    };
    validTickets: {
        [ticketId: string]: true;
    };
}
export type ShortRedemptionGroup = Pick<RedemptionGroup, 'redemptionGroupId' | 'redemptionGroupName' | 'method'>

export type TicketImport = {
    eventId: string;
    ticketImportId: string;
    ticketOrderId: string;
    name: string;
    description: string;
    columns: TicketImportColumnMap;
    uploadedFiles: {
        [fileNumber: number]: TicketImportFile;
    };
    executions: {
        [executionNumber: number]: TicketImportExecution;
    };
    isFirstLineHeader?: boolean;
    fieldMapping: FieldMappingItem[];
    valueMapping: {
        generatedTicketName: ValueMappingItem[];
        status: ValueMappingItem[];
    };
}

export type ValueMappingItem = {
    importValue: string;
    innTicketIdentifier?: string;
    ticketId?: string;
    salesPeriodId?: string;
}

export type FieldMappingItem = {
    innTicketField: TicketImportMappingAttribute;
    columnNumber: number;
}

export type TicketImportExecutionStatus = 'IMPORT_RUNNING' | 'IMPORT_DONE' | 'IMPORT_ERROR' | 'IMPORT_CREATED';
export type TicketImportExecution = {
    executionDate: string;
    executionNumber: number;
    finishDate?: string;
    importFileId: string;
    status: TicketImportExecutionStatus;
    importError?: string;
    eventEmployee: ShortEmployee;
    stats?: {
        updatedTickets: number;
        existingTickets: number;
        createdTickets: number;
        invalidMappings: number;
        invalidProcessing: number;
        allInvalidTickets: number;
        allTickets: number;
    };
    invalidTickets?: TicketImportInvalidTicket[];
}

export type TicketImportInvalidTicket = {
    errorMessage: string;
    code: string;
}

export type TicketImportFileStatus = 'SIGNED_URL_PROVIDED' | 'FILE_UPLOADED' | 'INSPECTION_DONE' | 'INSPECTION_SKIPPED' | 'INSPECTION_ERROR';
export type TicketImportFile = {
    fileNumber: number;
    s3Path: string;
    fileName: string;
    createdOn: string;
    uploadUrl: string;
    status: TicketImportFileStatus;
    inspectError?: string;
    numberOfRowsWithHeader?: number;
}

export type TicketImportColumnMap = {
	[columnNumber: number]: TicketImportColumn;
}

export type TicketImportColumn = {
    columnNumber: number;
    firstRowValue: string;
    exampleRowValues: string[];
}

// https://innevent.atlassian.net/wiki/spaces/INNEVENT/pages/edit-v2/196665
export type TicketImportMappingAttribute = 
    // 'order.orderNumber' |
    // 'order.status' | // Value Mapping
    // 'order.orderDate' |
    // 'order.employee.fullname' |
    // 'order.employee.firstName' |
    // 'order.employee.lastName' |
    // 'order.generalTermsAndConditionsAccepted' | // Value Mapping
    // 'order.customer.birthday' |
    // 'order.customer.customerType' | // Value Mapping
    // 'order.customer.emailAddress' |
    // 'order.customer.firstName' |
    // 'order.customer.gender' | // Value Mapping
    // 'order.customer.lastName' |
    // 'order.customer.organizationName' |
    // 'order.customer.phoneNumber' |
    'ticket.code' |
    'ticket.generatedTicketName' |
    'ticket.personalization.firstName' |
    'ticket.personalization.lastName' |
    'ticket.price.priceGross' |
    'ticket.price.priceNet' |
    'ticket.price.vatValue' |
    'ticket.price.vatPercent' |
    'ticket.status' // Value Mapping