<template>
  <div class="mt-3">
    <div class="d-flex justify-content-between align-items-end">
      <h2>Verkäufer Übersicht</h2>
      <b-button
        v-b-modal.modalCreateVendor
        variant="primary"
        size="sm"
        style="height: fit-content"
      >
        Verkäuf<PERSON> Erstellen
      </b-button>
    </div>
    <b-table
      striped
      hover
      :items="vendors"
      class="table-clickable mt-3"
      :fields="tableFields"
      head-variant="light"
      show-empty
      empty-text="Es sind keine Verkäufer verfügbar."
      @row-clicked="rowClick"
    />
  </div>
</template>
<script>
import LoadingSpinner from '../LoadingSpinner.vue';

export default {
	components: { LoadingSpinner },
	props: ['vendors'],
	data() { return {
		selectOptions: [
			{ text: 'Alle', value: 'all' },
			{ text: 'Vergangene Events', value: 'past' },
			{ text: 'Kommende Events', value: 'coming' }
		],
		tableFields:[
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'postalcode', label: '<PERSON><PERSON>itzahl', sortable: true },
			{ key: 'city', label: 'Stadt', sortable: true }
		]
	};},
	computed:{

	},
	methods:{
		rowClick(item) {
			this.$router.push({ name: 'vBasedata', params: { vendor: item.id } });
		}
	}
};
</script>
