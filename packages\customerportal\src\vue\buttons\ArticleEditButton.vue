<template>
  <div>
    <b-button-group size="sm" v-if="state == 'initial'">
      <b-button variant="warning" @click="clickEdit"><FontAwesomeIcon icon='edit'></FontAwesomeIcon> Edit</b-button>
      <b-button variant="danger" @click="clickDelete"><FontAwesomeIcon icon='trash'></FontAwesomeIcon> Delete</b-button>
    </b-button-group>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

export default {
  props: ['cellData'],
  data(){ return{
    state: 'initial'
  }},
  components: { FontAwesomeIcon },
  methods: {
    clickEdit(){
      this.cellData.item.editMode = true
      // console.log('ok' + this.cellData.item)
    },
    clickDelete(){
      alert('del');
      // this.cellData.item.editMode = true
      // console.log('ok' + this.cellData.item)
    }
  }
}
</script>
<style scoped>

</style>
