/* eslint-disable @typescript-eslint/naming-convention */
import { ref } from '@vue/composition-api';
import type { CognitoUser } from '@aws-amplify/auth';
import { Auth } from '@aws-amplify/auth';
import { userState as log } from '../loglevel';

log.debug('INIT userState');

export type CustomCognitoUser = CognitoUser & {
	attributes: {
		email: string;
		email_verified: string;
		family_name: string;
		given_name: string;
		sub: string;
	};
	username: string;
}

const currentUserRef = ref<CustomCognitoUser>();

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useUserState() {
	log.debug('useUserState()');

	async function reloadUser() {
		log.debug('reloadUser()');
		currentUserRef.value = await Auth.currentAuthenticatedUser();
	}

	return {
		currentUserRef,
		reloadUser
	};
}