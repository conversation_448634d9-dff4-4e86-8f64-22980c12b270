<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Ticket"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h2>Allgemeines</h2>
      <b-row
        v-for="(field, fieldKey) of inputFields"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col sm="3">
          <label
            v-if="field.type == 'text'"
            :for="`field-${fieldKey}`"
          >{{ field.name }}:</label>
        </b-col>
        <b-col
          sm="9"
        >
          <b-form-input
            v-if="field.type == 'text'"
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            @keyup.enter="onKeyupEnter()"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-personalizationRequired">Personalisierung:</label>
        </b-col>
        <b-col sm="9">
          <b-form-checkbox
            v-model="inputFields.personalizationRequired.value"
            switch
            disabled
            size="lg"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-ticketGroups">Ticketgruppen:</label>
        </b-col>
        <b-col sm="9">
          <Multiselect
            v-model="ticketGroups"
            :options="ticketMapRef.ticketGroups"
            :multiple="true"
            placeholder="Ticketgruppe auswählen"
            label="ticketGroupName"
            track-by="ticketGroupName"
            :allow-empty="true"
            :preselect-first="true"
            @select="addTicketGroup"
            @remove="remTicketGroup"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-ticketSaleProperties">Verkaufsphasen:</label>
        </b-col>
        <b-col sm="9">
          <b-table
            id="ticketTable"
            ref="ticketTable"
            hover
            :items="ticketSaleProperties"
            :fields="tableFields"
            class="mt-3 table-clickable"
            head-variant="light"
            @row-clicked="tableRowClick"
          >
            <template #cell()="data">
              <b-form-input
                v-model="data.item[data.field.key]"
              />
            </template>
            <template #cell(salesperiod)="data">
              {{ getSalesPeriodName(data.item.salesPeriodId) }}
            </template>
            <template #cell(priceGross)="data">
              <b-input-group
                append="€"
                style="width: 100px"
              >
                <b-form-input
                  v-model="data.item.price.priceGross"
                />
              </b-input-group>
            </template>
            <template #cell(vatPercent)="data">
              <b-input-group
                append="%"
                style="width: 100px"
              >
                <b-form-input
                  v-model="data.item.price.vatPercent"
                />
              </b-input-group>
            </template>
            <template #table-busy>
              <TableBusyLoader />
            </template>
            <template #cell(buttons)="data">
              <div class="d-flex justify-content-end">
                <DeleteButton
                  size="sm"
                  icon="trash"
                  text=""
                  variant="danger"
                  @click="remTicketSalesProperty($event, data)"
                />
              </div>
            </template>
            <!-- Custom Footer -->
            <template #custom-foot>
              <b-tr>
                <b-th
                  colspan="5"
                >
                  <b-dropdown
                    squared
                    class="float-right"
                    size="sm"
                    variant="outline-secondary"
                    :disabled="unusedSalesPeriods.length == 0"
                  >
                    <template #button-content>
                      <FontAwesomeIcon :icon="['fas', 'plus-square']" /> Verkaufsphase hinzufügen
                    </template>
                    <b-dropdown-item
                      v-for="salesPeriod in unusedSalesPeriods"
                      :key="salesPeriod.salesPeriodId"
                      @click="addSalesPeriod(salesPeriod)"
                    >
                      {{ salesPeriod.salesPeriodName }}
                    </b-dropdown-item>
                  </b-dropdown>
                </b-th>
              </b-tr>
            </template>
          </b-table>
        </b-col>
      </b-row>
    </div>
    <LoadButton
      ref="saveButton"
      variant="primary"
      @click="clickOnSave"
    /> 
  </b-sidebar>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import * as api from '@innevent/webapp-api';
import type { BvTableCellData, ModalAction, ResetButton, SalesPeriod, Ticket, TicketGroup, TicketSaleProperty } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useTicketMapState } from '../../../../states/ticketMap';
import Multiselect from 'vue-multiselect';
import Vue from 'vue';
import type { BvTableFieldArray } from 'bootstrap-vue';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';
import { currentEventRef } from '../../../../states/eventState';

type TicketSaleProperties = {
    [salesPeriodId: string]: Partial<TicketSaleProperty>;
};

export default defineComponent({
	components: {
		LoadButton,
		Multiselect,
		DeleteButton
	},
	emits: ['ticketChanged'],
	setup(props, { emit }) {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentTicket = ref<Ticket>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();
		const { ticketMapRef, isLoadingRef } = useTicketMapState();
		
		const tableFields: BvTableFieldArray =  [
			{ key: 'salesperiod', label: 'Verkaufsphase', sortable: true },
			{ key: 'contingent', label: 'Kontingent' },
			{ key: 'priceGross', label: 'Preis' },
			{ key: 'vatPercent', label: 'Steuern' },
			{ key: 'buttons', label: '' }
		];
		
		async function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function clickOnSave(btn: ResetButton) {
			await saveTicket();
			btn.reset();
		}

		async function saveTicket(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					const ticket = await api.createTicket({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							ticketName: inputFields.value.ticketName.value,
							description: inputFields.value.description.value,
							ticketCodePrefix: inputFields.value.ticketCodePrefix.value,
							personalizationRequired: inputFields.value.personalizationRequired.value,
							ticketGroups: inputFields.value.ticketGroups.value,
							ticketSaleProperties: inputFields.value.ticketSaleProperties.value
						}
					});
					ticketMapRef.value.tickets.push(ticket);

					emit('ticketChanged');
				} else {
					if (!currentTicket.value) throw 'NoValue';
					const ticket = await api.updateTicket({
						key: {
							eventId: currentTicket.value?.eventId,
							ticketId: currentTicket.value?.ticketId
						},
						data: {
							ticketName: inputFields.value.ticketName.value,
							description: inputFields.value.description.value,
							ticketCodePrefix: inputFields.value.ticketCodePrefix.value, 
							//personalizationRequired: inputFields.value.personalizationRequired.value,
							ticketGroups: inputFields.value.ticketGroups.value,
							ticketSaleProperties: inputFields.value.ticketSaleProperties.value
						}
					});
					const index = ticketMapRef.value.tickets.findIndex(element => element.ticketId == ticket.ticketId);
					ticketMapRef.value.tickets.splice(index, 1, ticket);
					emit('ticketChanged');
				}
				notifySuccess({ instance });
				sidebarOpen.value = false;
			} catch (error: any) {
				if (error.isAxiosError) {
					const axiosError: AxiosError = error;
					const response: InnEventError = axiosError.response?.data;

					if (response.isInnEventError) {
						if (response.errorCode == 'ValidationError') {
							notifyError({ instance, title: 'Validierungsfehler', message: 'Die Felder wurden nicht richtig gefüllt' });
							return;
						}	
					} 
				}
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;
			inputFields.value.ticketName.value = '';
			inputFields.value.description.value = '';
			inputFields.value.ticketCodePrefix.value = '';
			inputFields.value.ticketGroups.value = {};
			inputFields.value.ticketSaleProperties.value = {};
		}

		function openForEdit(ticket: Ticket) {
			actionRef.value = 'edit';
			sidebarOpen.value = true;
			currentTicket.value = ticket;
			inputFields.value.ticketName.value = ticket.ticketName;
			inputFields.value.description.value = ticket.description;
			inputFields.value.ticketCodePrefix.value = ticket.ticketCodePrefix;
			inputFields.value.personalizationRequired.value = ticket.personalizationRequired,
			inputFields.value.ticketGroups.value = ticket.ticketGroups;

			//Mapping TicketSaleProperties
			const newTicketSaleProperties = Object.values(ticket.ticketSaleProperties).reduce((ticketSaleProperties, ticketSalesProperty) => {
				const newTicketSaleProperty: Partial<TicketSaleProperty> = {
					salesPeriodId: ticketSalesProperty.salesPeriodId,
					contingent: ticketSalesProperty.contingent,
					price: ticketSalesProperty.price
				};
				ticketSaleProperties[ticketSalesProperty.salesPeriodId] = newTicketSaleProperty;
				return ticketSaleProperties;
			}, {} as TicketSaleProperties);
			inputFields.value.ticketSaleProperties.value = newTicketSaleProperties;

		}

		const inputFields = ref({
			ticketName: {
				name: 'Name',
				type: 'text',
				value: ''
			},
			description: {
				name: 'Beschreibung',
				type: 'text',
				value: ''
			},
			ticketCodePrefix: {
				name: 'Ticketpräfix',
				type: 'text',
				value: ''
			},
			personalizationRequired: {
				name: 'Personalisierung',
				type: 'switch',
				value: false
			},
			ticketGroups: {
				name: 'Ticketgruppen',
				type: 'multiselect',
				value: {}
			},
			ticketSaleProperties: {
				name: 'Verkaufsphasen',
				type: 'ticketSaleProperties',
				value: {} 
			}
		});

		const ticketGroups = computed(() => {
			const ticketGroupIds = Object.keys(inputFields.value.ticketGroups.value);
			return ticketMapRef.value.ticketGroups.filter(((ticketGroup) => ticketGroupIds.includes(ticketGroup.ticketGroupId)));
		});

		const ticketSaleProperties = computed(() => {
			return Object.values(inputFields.value.ticketSaleProperties.value);
		});

		const unusedSalesPeriods = computed(() => {
			if (inputFields.value.ticketSaleProperties.value) {
				const usedSalesPeriodIds = Object.values(inputFields.value.ticketSaleProperties.value as TicketSaleProperties).map((saleProperty) => 
					saleProperty.salesPeriodId);
				return ticketMapRef.value.salesPeriods.filter(salesPeriod => !usedSalesPeriodIds.includes(salesPeriod.salesPeriodId));
			} else {
				return ticketMapRef.value.salesPeriods;
			}
		});

		function getSalesPeriodName(salesPeriodId: string): string {
			return ticketMapRef.value.salesPeriods.find(salesPeriod => salesPeriod.salesPeriodId == salesPeriodId)?.salesPeriodName ?? '';
		}

		function remTicketGroup(value: TicketGroup) {
			Vue.delete(inputFields.value.ticketGroups.value, value.ticketGroupId);
		}

		function addTicketGroup(value: TicketGroup) {
			Vue.set(inputFields.value.ticketGroups.value, value.ticketGroupId, true);
		}

		function addSalesPeriod(salesPeriod: SalesPeriod) {
			const ticketSalesProperty: TicketSaleProperty = {
				salesPeriodId: salesPeriod.salesPeriodId,
				contingent: 10,
				price: {
					priceGross: 10.00,
					vatPercent: 19
				}
			};
			Vue.set(inputFields.value.ticketSaleProperties.value, salesPeriod.salesPeriodId, ticketSalesProperty);
		}

		function remTicketSalesProperty(btn: ResetButton, cellData: BvTableCellData<TicketSaleProperty>) {
			Vue.delete(inputFields.value.ticketSaleProperties.value, cellData.item.salesPeriodId);
			btn.reset();
		}

		return {
			actionRef,
			sidebarOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			openForCreate,
			openForEdit,
			saveTicket,
			ticketMapRef,
			ticketGroups,
			addTicketGroup,
			remTicketGroup,
			currentTicket,
			tableFields,
			ticketSaleProperties,
			getSalesPeriodName,
			addSalesPeriod,
			unusedSalesPeriods,
			remTicketSalesProperty
		};
	}
});
</script>
<style>
</style>