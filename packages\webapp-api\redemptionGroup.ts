import { apiInnTicket } from './instances';
import type { RedemptionGroup }  from '@innevent/types';

type ModelType = RedemptionGroup;
type PrimaryKey = 'redemptionGroupId';


export type RedemptionGroupCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'redemptionGroupName' | 'description' | 'method' | 'employeePermissions' | 'validTickets'>;
}
export async function createRedemptionGroup(options: RedemptionGroupCreateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).post('/RedemptionGroup', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type RedemptionGroupDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteRedemptionGroup(options: RedemptionGroupDeleteOptions): Promise<void> {
	await (await apiInnTicket()).delete(`/RedemptionGroup/${options.key.redemptionGroupId}`, { 
		params: { eventId: options.key.eventId } 
	});
}


export type RedemptionGroupUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Pick<ModelType, 'redemptionGroupName' | 'description' | 'method' | 'employeePermissions' | 'validTickets'>;
};

export async function updateRedemptionGroup(options: RedemptionGroupUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnTicket()).patch(`/RedemptionGroup/${options.key.redemptionGroupId}`, 
		{
			redemptionGroupName: options.data.redemptionGroupName,
			description: options.data.description,
			method: options.data.method,
			employeePermissions: options.data.employeePermissions,
			validTickets: options.data.validTickets
		}, 
		{ params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW' 
		} 
		});
	return response.data;
}


export type RedemptionGroupGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function getRedemptionGroups(options: RedemptionGroupGetOptions): Promise<ModelType[]> {
	const response = await (await apiInnTicket()).get('/RedemptionGroup', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}