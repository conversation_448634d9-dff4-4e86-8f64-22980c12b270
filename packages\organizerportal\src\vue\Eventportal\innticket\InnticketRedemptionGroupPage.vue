<template>
  <InnticketRedemptionGroup />
</template>
<script lang="ts">
import InnticketRedemptionGroup from './elements/InnticketRedemptionGroupTable.vue';
import { onMounted, defineComponent } from '@vue/composition-api';
import { refreshRedemptionGroups } from '../../../states/redemptionGroup';
import { currentEventRef } from '../../../states/eventState';


export default defineComponent({
	components: {
		InnticketRedemptionGroup
	},
	setup() {
		onMounted(async () => {
			await refreshRedemptionGroups(currentEventRef.value!.eventId!);
		});
		return {  };
	}
	
});
</script>
