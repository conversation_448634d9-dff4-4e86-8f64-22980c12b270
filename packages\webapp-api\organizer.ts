import type { Organizer } from '@innevent/types';
import { apiInnEvent } from './instances';

type ModelType = Organizer;
type PrimaryKey = 'eventId';


export type GetPermittedOrganizersOptions =  {
    
};
export async function getPermittetOrganizers(): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/Organizer');
	return response.data;
}

export type OrganizerCreateOptions = {
	data: Pick<ModelType, 'organizationName' | 'organizationType'>;
}
export async function createOrganizer(options: OrganizerCreateOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).post('/Organizer', options.data, { 
		params: { 
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}