<template>
  <div>
    <Header :cognitoUser="cognitoUser"></Header>
    <b-container fluid class="max-w1600">
      <div class="d-flex flex-column flex-md-row" v-if="cognitoUser">
        <div class="p-3 shadow sidebar-nav">
          <b-nav vertical pills id="accountNavigation">
            <b-nav-item exact exact-active-class="active" v-for="item of navItems" :to="item.link" :key="item.link">
              <FontAwesomeIcon :icon='item.icon' fixed-width></FontAwesomeIcon><span class="ml-3">{{ item.name }}</span>
            </b-nav-item>
          </b-nav>
        </div>
        <div class="flex-fill p-3">
          <router-view :cognitoUser="cognitoUser" />
        </div>
      </div>
      <b-alert v-else show variant="danger" class="mt-5">404 User Not Found</b-alert>
    </b-container>
  </div>
</template>
<script>

import { Auth, API } from 'aws-amplify'
import Header from './Header.vue'
var URL = require('url-parse')

export default {
  components: { Header },
  async created(){
    this.url = new URL(window.location.href, true)
    await Auth.currentAuthenticatedUser().then(user => {
      this.cognitoUser = user
      console.log("User " + user.username + " logged in")
    }).catch(err => {
      console.log("User Unauthorized")
      this.$router.push('/login?redirect_uri=' + this.url.toString())
    })
  },
  data() { return{
    cognitoUser: null,
    navItems: [
      { name: 'Übersicht', link: 'overview', icon: 'user-circle' },
      { name: 'Persönliche Daten', link: 'personaldata', icon: 'id-card' },
      { name: 'Sicherheit', link: 'security', icon: 'lock' }
      //{ name: 'Benachrichtigungen', link: 'notification', icon: 'envelope' },
      //{ name: 'Zahlungsarten', link: 'payment', icon: 'credit-card' }
    ]
  }},
  computed: {

  },
  methods: {

  }
}
</script>
