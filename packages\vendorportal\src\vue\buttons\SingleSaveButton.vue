<template>
  <div>
    <button
      v-if="state == 'created'"
      type="button"
      class="btn btn-primary float-right"
      @click="save"
    >
      Speichern
    </button>
    <button
      v-if="state == 'loading'"
      type="button"
      class="btn btn-primary float-right"
      disabled
    >
      <span class="spinner-border spinner-border-sm" />
      Loading...
    </button>
    <button
      v-if="state == 'success'"
      type="button"
      class="btn btn-success float-right"
      @click="save"
    >
      <FontAwesomeIcon icon="check" />
      Erfolgreich
    </button>
    <button
      v-if="state == 'error'"
      type="button"
      class="btn btn-danger float-right"
      @click="save"
    >
      <FontAwesomeIcon icon="exclamation-triangle" />
      Es ist ein Fehler aufgetreten! ({{ error }})
    </button>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export default {
	components: { FontAwesomeIcon },
	data() { return {
		error: null,
		state: 'created'
	};},
	methods:{
		save() {
			this.state = 'loading';
			this.$emit('save', this);
		},
		async setSuccess(v) {
			v.state = 'success';
			await sleep(2000);
			v.state = 'created';
		},
		async setError(v, error) {
			v.error = error;
			v.state = 'error';
			await sleep(5000);
			v.state = 'created';
			v.error = null;
		}
	}
};
</script>
