// Vue Components
import './installCompositionApi.ts';
import Vue from 'vue';

import { app as log } from './src/loglevel';

Vue.config.silent = true;
import { router } from './router';
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
Vue.component('Multiselect', Multiselect);

// Vue DateTimePicker https://www.npmjs.com/package/vue-ctk-date-time-picker
import VueCtkDateTimePicker from 'vue-ctk-date-time-picker';
import 'vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css';

Vue.component('DateTimePicker', VueCtkDateTimePicker);

// Bootstrap
import { BootstrapVue } from 'bootstrap-vue';
import 'bootstrap'; //Importing JavaScript https://getbootstrap.com/docs/4.1/getting-started/webpack/
import './src/css/custom.scss';
Vue.use(BootstrapVue, {
	breakpoints: ['xs', 'sm', 'md', 'lg', 'xl', 'xxl', 'xxxl']
});

// CSS
import './src/css/global.css';

// Fontawesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
library.add(fas, far);
Vue.component('FontAwesomeIcon', FontAwesomeIcon);

// AWS
import Amplify, { Hub, Logger } from '@aws-amplify/core';
import { Auth } from 'aws-amplify';
import awsconfig from './src/js/AmplifyConfig';
Amplify.configure(awsconfig);
Auth.configure(awsconfig);

// Other Modules
import URL from 'url-parse';

// Vue Pages
import App from './src/vue/App.vue';
import * as api from '@innevent/webapp-api';

const url = new URL(window.location.href, true);

async function startLoad() {
	if (url.query.code) { // Auth Code ist gesetzt
		try {
			const authCache = await api.getAuthCache({
				key: {
					authCacheId: url.query.code
				}
			});
			const keyPrefix = `CognitoIdentityServiceProvider.${process.env.COGNITO_WEBCLIENT_ID}.${authCache.username}`;
			const idTokenKey = `${keyPrefix}.idToken`;
			const accessTokenKey = `${keyPrefix}.accessToken`;
			const refreshTokenKey = `${keyPrefix}.refreshToken`;

			localStorage.setItem(idTokenKey, authCache.idToken);
			localStorage.setItem(accessTokenKey, authCache.accessToken);
			localStorage.setItem(refreshTokenKey, authCache.refreshToken);
			localStorage.setItem(`CognitoIdentityServiceProvider.${process.env.COGNITO_WEBCLIENT_ID}` + '.LastAuthUser', authCache.username);

			await Auth.currentAuthenticatedUser();
			delete url.query.code;
			router.replace(url);
			// TODO: deviceKey mit einbauen, wenn Geräte gespeichert werden
		}
		catch (error) {
			log.warn('Fehler bei Tokenabfrage', error);
			// TODO: show Error Message and Link to Fresh Login
			return; // Prevent Loop on Auth Error
		}
	}

	// Get Cogntio User Object
	Auth.currentAuthenticatedUser({
		bypassCache: false  // Optional, By default is false. If set to true, this call will send a request to Cognito to get the latest user data
	}).then(async cognitoUser => {
		new Vue({
			router,
			render: (createEl) => createEl(App, {
				props: { cognitoUser }
			})
		}).$mount('#app');
	}).catch((error) => {
		console.log('Not Authenticaded, Redirect to Login', error);
		const loginUrl = URL(process.env.URL_INNLOGIN, true);
		loginUrl.pathname = '/login';
		loginUrl.query.redirect_uri = window.location.href;
		window.location.replace(loginUrl.toString());
	});
}

const logger = new Logger('My-Logger', 'DEBUG');

const listener = (data) => {
	console.log(data);
	switch (data.payload.event) {
	case 'signIn':
		logger.info('user signed in');
		break;
	case 'signUp':
		logger.info('user signed up');
		break;
	case 'signOut':
		logger.info('user signed out');
		break;
	case 'signIn_failure':
		logger.error('user sign in failed');
		break;
	case 'tokenRefresh':
		logger.info('token refresh succeeded');
		break;
	case 'tokenRefresh_failure':
		logger.error('token refresh failed');
		break;
	case 'configured':
		logger.info('the Auth module is configured');
	}
};

Hub.listen('auth', listener);


startLoad();


// async function startLoad(){
//   if(appCognitoUser && portal == "eventportal"){
//     app = new Vue({
//       render: (createEl) => createEl(Eventportal, {
//         props: {
//           cognitoUser: appCognitoUser,
//           portal: portal
//         }
//       })
//     }).$mount('#site')
//   }else if(appCognitoUser && portal == "organizerportal"){
//     app = new Vue({
//       render: (createEl) => createEl(Eventportal, {
//         props: {
//           cognitoUser: appCognitoUser,
//           portal: portal
//         }
//       })
//     }).$mount('#site')
//   }else{
//     app = new Vue({
//       render: (createEl) => createEl(Login, {
//         props: {
//           reloadOnSuccess: true
//         }
//       })
//     }).$mount('#site')
//   }
//
// }
// startLoad()
