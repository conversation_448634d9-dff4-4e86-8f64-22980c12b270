<template>
  <div>
    <h2>Artikelverwaltung</h2>
    <LoadingSpinner v-if="loading" />
    <ArticleGroup
      v-for="ag in eventArticleGroups"
      :key="ag.id"
      :ag="ag"
      :article-groups="eventArticleGroups"
      :ag-bus="agBus"
      :deposit-groups="depositGroups"
      @delArticleGroup="delArticleGroup"
    />
    <div class="d-flex">
      <LoadButton
        class="ml-auto"
        size="sm"
        text="Eventspezifische Artikelgruppe erstellen"
        icon="plus-square"
        @click="addArticleGroup"
      />
      <b-button
        v-b-modal="'copyGlobalArticleGroupModal'"
        class="ml-2"
        variant="primary"
        size="sm"
      >
        Globale Artikelgruppe kopieren
      </b-button>
    </div>
    <b-modal
      :id="'copyGlobalArticleGroupModal'"
      title="Globale Artikelgruppen kopieren"
      centered
      cancel-title="Abbruch"
      @ok="copySelectedGlobalAGs"
    >
      <b-table
        selectable
        :items="globalArticleGroups"
        :fields="tableGlobalArticleGroups.fields"
        @row-selected="(rows) => (tableGlobalArticleGroups.selectedRows = rows)"
      >
        <!-- Selected Field -->
        <template #cell(selected)="{ rowSelected }">
          <template v-if="rowSelected">
            <FontAwesomeIcon
              :icon="['far', 'check-square']"
              size="lg"
            />
            <span class="sr-only">Selected</span>
          </template>
          <template v-else>
            <FontAwesomeIcon
              :icon="['far', 'square']"
              size="lg"
            />
            <span class="sr-only">Not selected</span>
          </template>
        </template>
      </b-table>
      <template #modal-footer="{ hide }">
        <LoadButton
          text="In dieses Event Kopieren"
          icon="plus-square"
          @click="copySelectedGlobalAGs($event, hide)"
        />
      </template>
    </b-modal>
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadingSpinner from '../LoadingSpinner.vue';
import LoadButton from '../buttons/LoadButton.vue';
const uuid = require('uuid/v4');
import Vue from 'vue';
import ArticleGroup from '../ArticleGroup.vue';

export default {
	components: { LoadingSpinner, LoadButton, ArticleGroup },
	props: ['selectedVendor', 'selectedEvent'],
	data() {
		return {
			eventArticleGroups: [],
			globalArticleGroups: [],
			depositGroups: [],
			loading: true,
			agBus: new Vue(),
			tableGlobalArticleGroups: {
				selectedRows: [],
				fields: [
					{
						key: 'selected',
						label: ''
					},
					{
						key: 'name',
						label: 'Name',
						sortable: true
					},
					{
						key: 'description',
						label: 'Beschreibung'
					}
				]
			}
		};
	},
	computed: {},
	watch: {
		selectedEvent() {
			this.loading = true;
			this.loadEventArticleGroups();
			this.loadDepositGroups();
			this.loadGlobalArticleGroups();
		}
	},
	created() {
		this.loadEventArticleGroups();
		this.loadGlobalArticleGroups();
		this.loadDepositGroups();
	},
	methods: {
		copySelectedGlobalAGs(btn, hide) {
			API.post(
				'innorder',
				'/ArticleGroup/copy?vendorId=' +
          this.selectedVendor.id +
          '&eventId=' +
          this.selectedEvent.id +
          '&articleGroupIds=' +
          this.tableGlobalArticleGroups.selectedRows
          	.map((globalAg) => globalAg.id)
          	.join(',')
			)
				.then((values) => {
					this.$bvToast.toast(
						'Die Artikelgruppen wurde erfolgreich erstellt.',
						{
							title: 'Erstellen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					this.loadEventArticleGroups();
					btn.reset();
					hide();
				})
				.catch((error) => {
					this.$bvToast.toast('Beim Kopieren ist ein Fehler aufgetreten.', {
						title: 'Kopieren Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
		},
		loadDepositGroups() {
			API.get(
				'innorder',
				'/DepositGroup?filterVendor=' +
          this.selectedVendor.id +
          '&filterEvent=' +
          this.selectedEvent.id
			)
				.then((response) => {
					let tempdepositGroups = response.map((x) => {
						return { value: x.id, text: x.name + ` (${x.costGross} €)` };
					});
					tempdepositGroups.splice(0, 0, { value: null, text: 'Kein Pfand' });
					this.depositGroups = tempdepositGroups;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der Pfandgruppen ist ein Fehler aufgetreten. ErrorCode: ' +
              error.toString(),
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		loadEventArticleGroups() {
			API.get(
				'innorder',
				'/ArticleGroup?filterVendor=' +
          this.selectedVendor.id +
          '&filterEvent=' +
          this.selectedEvent.id
			)
				.then((response) => {
					this.eventArticleGroups = response;
					this.loading = false;
				})
				.catch((error) => {
					console.log(error);
					this.$bvToast.toast(
						'Beim Laden der Artikelgruppen ist ein Fehler aufgetreten.',
						{
							title: 'Löschen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		loadGlobalArticleGroups() {
			API.get(
				'innorder',
				'/ArticleGroup?filterVendor=' +
          this.selectedVendor.id +
          '&filterGlobal=true'
			)
				.then((response) => {
					this.globalArticleGroups = response;
				})
				.catch((error) => {
					console.log(error);
					this.$bvToast.toast(
						'Beim Laden der globalen Artikelgruppen ist ein Fehler aufgetreten.',
						{
							title: 'Löschen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		addArticleGroup(btn) {
			let item = {
				id: uuid(),
				VendorId: this.selectedVendor.id,
				active: true,
				color: null,
				description: '',
				EventId: this.selectedEvent.id,
				name: 'Neue Artikelgruppe'
			};
			API.post('innorder', '/ArticleGroup/', { body: item })
				.then((response) => {
					this.$bvToast.toast(
						'Die Artikelgruppe "' + item.name + '" wurde erfolgreich erstellt.',
						{
							title: 'Erstellen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					this.eventArticleGroups.push(item);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Erstellen von "' +
              item.name +
              '" ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		delArticleGroup(articleGroup) {
			this.$bvToast.toast(
				'Die ArtikelGruppe "' +
          articleGroup.name +
          '" wurde erfolgreich gelöscht.',
				{
					title: 'Löschen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				}
			);
			// Remove from Local Array
			for (let i = 0; i < this.eventArticleGroups.length; i++) {
				if (this.eventArticleGroups[i].id == articleGroup.id) {
					this.$delete(this.eventArticleGroups, i);
				}
			}
		}
	}
};
</script>
