<template>
  <b-sidebar
    v-model="sidebarOpen"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Ticketgruppen"
    width="1000px"
    backdrop
    right
  >
    <div class="mb-4">
      <h2>Allgemeines</h2>
      <b-row
        v-for="(field, fieldKey) of textFields"
        :key="fieldKey"
        class="mt-2"
      >
        <b-col
          sm="3"
        >
          <label
            :for="`field-${fieldKey}`"
          >{{ field.name }}:</label>
        </b-col>
        <b-col
          sm="9"
        >
          <b-form-input
            :id="`field-${fieldKey}`"
            v-model="field.value"
            :type="field.type"
            @keyup.enter="onKeyupEnter()"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-method">Methode:</label>
        </b-col>
        <b-col sm="9">
          <b-form-select
            v-model="inputFields.method.value"
            :options="redemptionMethods"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-validTickets">Gültige Tickets:</label>
        </b-col>
        <b-col sm="9">
          <Multiselect
            v-model="validTickets"
            :options="ticketMapRef.tickets"
            :multiple="true"
            placeholder="Ticket auswählen"
            label="ticketName"
            track-by="ticketName"
            :allow-empty="true"
            :preselect-first="true"
            :close-on-select="false"
            @select="addTicket"
            @remove="removeTicket"
          />
        </b-col>
      </b-row>
      <b-row class="mt-2">
        <b-col sm="3">
          <label for="field-validTickets">Mitarbeiter:</label>
        </b-col>
        <b-col sm="9">
          <Multiselect
            :options="selectableEmployees"
            :multiple="false"
            placeholder="Mitarbeiter auswählen"
            :custom-label="getEmployeeName"
            track-by="id"
            :allow-empty="true"
            :preselect-first="true"
            @select="addEmployee"
          />
          <b-table
            id="employeePermissionsTable"
            ref="employeePermissionsTable"
            hover
            :items="employeePermissions"
            :fields="tableFields"
            class="mt-3 table-clickable"
            head-variant="light"
            @row-clicked="tableRowClick"
          >
            <template #cell(name)="{item}">
              {{ `${item.firstName} ${item.lastName}` }}
            </template>
            <template #cell(permission)="{item}">
              <b-form-select
                :value="getEmployeePermission(item)"
                :options="permissionOptions"
                @input="addEmployeePermission(item, $event)"
              />
            </template>
            <template #table-busy>
              <TableBusyLoader />
            </template>
            <template #cell(buttons)="data">
              <div class="d-flex justify-content-end">
                <DeleteButton
                  size="sm"
                  icon="trash"
                  text=""
                  variant="danger"
                  @click="removeEventEmployee($event, data)"
                />
              </div>
            </template>
          </b-table>
        </b-col>
      </b-row>
    </div>
    <LoadButton
      ref="saveButton"
      variant="primary"
      @click="clickOnSave"
    />
  </b-sidebar>
</template>
<script lang="ts">
import type { ComputedRef, Ref } from '@vue/composition-api';
import { defineComponent, ref, getCurrentInstance, computed, onMounted } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import * as api from '@innevent/webapp-api';
import type { BvTableCellData, ModalAction, ResetButton } from '@innevent/webapp-types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { AxiosError } from 'axios';
import type { InnEventError } from '@innevent/types';
import type { RedemptionGroup, RedemptionMethod, Ticket, EventEmployee as Employee } from '@innevent/types';
import Vue from 'vue';
import type { BvTableFieldArray } from 'bootstrap-vue';
import { useRedemptionGroupMap, useEmployeeState, useTicketMapState, currentEventRef, refreshTicketMap } from '../../../../states';

type InputField = {
	name: string;
	type: string;
	value: any;
}

export default defineComponent({
	components: {
		LoadButton,
		DeleteButton
	},
	props: {
		eventId: {
			type: String,
			required: true
		}		
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentRedemptionGroupRef = ref<RedemptionGroup>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();
		const { redemptionGroupMapRef: redemptionGroupMap } = useRedemptionGroupMap();
		const { employeesOfEventRef } = useEmployeeState();
		const { ticketMapRef } = useTicketMapState();

		onMounted(async () => {
			await refreshTicketMap();
		});

		const redemptionMethods = [
			{ value: 'REDEEM', text: 'Entwerten' },
			{ value: 'REDEEM_CONNECT', text: 'Entwerten und Verknüpfen' }
		];
		const permissionOptions = [
			{ value: 'perm:innticket.redemptiongroup.use', text: 'Mitarbeiter' },
			{ value: 'perm:innticket.redemptiongroup.admin', text: 'Admin' }
		];
		const tableFields: BvTableFieldArray =  [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'permission', label: 'Berechtigung' },
			{ key: 'buttons', label: '' }
		];

		function getEmployeePermission(data: Employee) {
			const redemptionGroup =  redemptionGroupMap.value.redemptionGroups.find(redemptionGroup => 
				redemptionGroup.redemptionGroupId == currentRedemptionGroupRef.value?.redemptionGroupId);
			const employeePermissions = Object.keys(redemptionGroup?.employeePermissions[data.userSubject] ?? []);

			return (employeePermissions.includes('perm:innticket.redemptiongroup.admin')) ? 
				'perm:innticket.redemptiongroup.admin' : 
				'perm:innticket.redemptiongroup.use';
		}
		
		function addEmployeePermission(data: Employee, inputEvent) {
			Vue.set(inputFields.value.employeePermissions.value, data.userSubject, { [inputEvent]: true });
		}
		

		function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function clickOnSave(btn: ResetButton) {
			await saveTicketGroup();
			btn.reset();
		}

		const validTickets = computed(() => {
			const ticketIds = Object.keys(inputFields.value.validTickets.value);
			return ticketMapRef.value.tickets.filter((ticket) => ticketIds.includes(ticket.ticketId));

		});

		const textFields: ComputedRef<{ [fieldKey: string]: string; }> = computed(() => {
			return Object.entries(inputFields.value).reduce((fields, [key, value]) => {
				if (value.type == 'text') {
					fields[key] = value;
				}
				return fields;
			}, {});
		});

		const employeePermissions = computed(() => {
			const employeePermissionIds = Object.keys(inputFields.value.employeePermissions.value);
			return employeesOfEventRef.value.filter((employee) => employeePermissionIds.includes(employee.userSubject));
		});

		const selectableEmployees = computed(() => {
			const employeePermissionIds = Object.keys(inputFields.value.employeePermissions.value);
			return employeesOfEventRef.value.filter((employee) => !employeePermissionIds.includes(employee.userSubject));
		});

		function removeTicket(value: Ticket) {
			Vue.delete(inputFields.value.validTickets.value, value.ticketId);
		}

		function addTicket(value: Ticket) {
			Vue.set(inputFields.value.validTickets.value, value.ticketId, true);
		}

		function getEmployeeName({ firstName, lastName }) {
			return `${firstName}  ${lastName}`;
		}

		function removeEventEmployee(btn: ResetButton, cellData: BvTableCellData<Employee>) {
			Vue.delete(inputFields.value.employeePermissions.value, cellData.item.userSubject);
		}
		function addEmployee(value: Employee) {
			Vue.set(inputFields.value.employeePermissions.value, value.userSubject, { 'perm:innticket.redemptiongroup.use': true });
		}

		async function saveTicketGroup(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					const redemptionGroup = await api.createRedemptionGroup({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							redemptionGroupName: inputFields.value.redemptionGroupName.value,
							description: inputFields.value.description.value,
							method: inputFields.value.method.value as RedemptionMethod,
							employeePermissions: inputFields.value.employeePermissions.value,
							validTickets: inputFields.value.validTickets.value
						}
					});
					redemptionGroupMap.value.redemptionGroups.push(redemptionGroup);
				} else {
					if (!currentRedemptionGroupRef.value) throw 'NoValue';
					const redemptionGroup = await api.updateRedemptionGroup({
						key: {
							eventId: currentRedemptionGroupRef.value?.eventId,
							redemptionGroupId: currentRedemptionGroupRef.value?.redemptionGroupId
						},
						data: {
							redemptionGroupName: inputFields.value.redemptionGroupName.value,
							description: inputFields.value.description.value,
							method: inputFields.value.method.value as RedemptionMethod,
							employeePermissions: inputFields.value.employeePermissions.value,
							validTickets: inputFields.value.validTickets.value
						}
					});
					const index = redemptionGroupMap.value.redemptionGroups.findIndex(element => 
						element.redemptionGroupId == redemptionGroup.redemptionGroupId);
					redemptionGroupMap.value.redemptionGroups.splice(index, 1, redemptionGroup);
				}
				notifySuccess({ instance });
				sidebarOpen.value = false;
			} catch (error: any) {
				if (error?.isAxiosError) {
					const axiosError: AxiosError = error;
					const response: InnEventError = axiosError.response?.data;

					if (response.isInnEventError) {
						if (response.errorCode == 'ValidationError') {
							notifyError({ instance, title: 'Validierungsfehler', message: 'Die Felder wurden nicht richtig gefüllt' });
							return;
						}	
					} 
				}
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpen.value = true;
			inputFields.value.redemptionGroupName.value = '';
			inputFields.value.description.value = '';
			inputFields.value.method.value = 'REDEEM';
			inputFields.value.employeePermissions.value = {};
			inputFields.value.validTickets.value = {};
		}

		function openForEdit(redemptionGroup: RedemptionGroup) {
			actionRef.value = 'edit';
			sidebarOpen.value = true;
			currentRedemptionGroupRef.value = redemptionGroup;
			inputFields.value.redemptionGroupName.value = redemptionGroup.redemptionGroupName;
			inputFields.value.description.value = redemptionGroup.description;
			inputFields.value.method.value = redemptionGroup.method;
			inputFields.value.employeePermissions.value = redemptionGroup.employeePermissions;
			inputFields.value.validTickets.value = redemptionGroup.validTickets;
		}

		const inputFields: Ref<{ [fieldName: string]: InputField; }> = ref({
			redemptionGroupName: {
				name: 'Name',
				type: 'text',
				value: ''
			},
			description: {
				name: 'Beschreibung',
				type: 'text',
				value: ''
			},
			method: {
				name: 'Methode',
				type: 'select',
				value: ''
			},
			employeePermissions: {
				name: 'Mitarbeiter',
				type: 'individual',
				value: {}
			},
			validTickets: {
				name: 'Gültige Tickets',
				type: 'individual',
				value: {}
			}
		});


		return {
			actionRef,
			sidebarOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			openForCreate,
			openForEdit,
			saveTicketGroup,
			redemptionMethods,
			currentRedemptionGroupRef,
			redemptionGroupMap,
			validTickets, 
			removeTicket,
			addTicket,
			getEmployeeName,
			employeePermissions,
			tableFields,
			removeEventEmployee,
			selectableEmployees,
			addEmployee,
			permissionOptions,
			getEmployeePermission,
			addEmployeePermission,
			textFields,
			ticketMapRef
		};
	}
});
</script>
<style>
</style>