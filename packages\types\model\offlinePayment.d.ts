import type { ShortEmployee } from '.';

export type OfflinePaymentObject = {
    name: string;
    description: string;
    currentCredit: number;
} & (ReferenceEvent | ReferenceVendor)

export type ReferenceEvent = {
    eventId: string;
    referenceObject?: 'event';
    vendorId?: never;
}
export type ReferenceVendor = {
    vendorId: string;
    referenceObject?: 'vendor';
    eventId?: never;
}

export type Cashbox = OfflinePaymentObject & {
    cashboxId: string;
}

export type EcTerminal = OfflinePaymentObject & {
    ecTerminalId: string;
}


export type OfflinePaymentTransaction = {
    createdOn_transactionId: string;
    transactionId: string;
    createdOn: string;
    creditBefore: number;
    creditChange: number;
    creditAfter: number;
    comment: string;
    employee: ShortEmployee;
}

export type CashboxTransaction = OfflinePaymentTransaction & {
    cashboxId: string;
}

export type EcTerminalTransaction = OfflinePaymentTransaction & {
    ecTerminalId: string;
}

