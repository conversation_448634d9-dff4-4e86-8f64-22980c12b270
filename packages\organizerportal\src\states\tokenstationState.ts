import { tokenstation as api } from '@innevent/webapp-api';
import { watch } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { tokenstationState as log } from '../loglevel';
import Vue from 'vue';
import type { Tokenstation } from '@innevent/webapp-types';

log.debug('INIT tokenstationState');

const isLoadingRef = ref<boolean>(false);
const isLoadingInitialRef = ref<boolean>(true);
const tokenstationsRef = ref<Tokenstation[]>([]);

watch(tokenstationsRef, () => {
	log.debug('WATCH tokenstationsRef', tokenstationsRef.value);
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useTokenstationState() {
	log.debug('useTokenstationState()');

	return {
		isLoadingRef,
		isLoadingInitialRef,
		tokenstationsRef,
		loadTokenstations,
		createTokenstation,
		updateTokenstation,
		deleteTokenstation
	};
}

// type LoadTokenstationsStateOption = {
//     key: Pick<Tokenstation, 'EventId'>;
// }
async function loadTokenstations(eventId: string): Promise<void> {
	log.debug('loadTokenstations()', eventId);
	isLoadingRef.value = true;
	try {
		tokenstationsRef.value = await api.getTokenstations({
			key: {
				EventId: eventId
			}
		});
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
	isLoadingInitialRef.value = false;
}

type CreateTokenstationStateOption = {
	data: Pick<Tokenstation, 'EventId' | 'name'> & Partial<Pick<Tokenstation, 'description'>>;
}
async function createTokenstation(options: CreateTokenstationStateOption): Promise<void> {
	log.debug('createTokenstation()', options);
	isLoadingRef.value = true;
	try {
		const newTokenstation = await api.createTokenstation(options);
		tokenstationsRef.value.push(newTokenstation);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type UpdateTokenstationStateOption = {
	key: Pick<Tokenstation, 'id'>;
	data: Partial<Pick<Tokenstation, 'name' | 'description'>>;
}
async function updateTokenstation(options: UpdateTokenstationStateOption): Promise<void> {
	log.debug('updateTokenstation()', options);
	isLoadingRef.value = true;
	try {
		const updatedTokenstation = await api.updateTokenstation(options);
		const index = tokenstationsRef.value.findIndex((oldTokenstation) =>oldTokenstation.id == updatedTokenstation.id);
		if (index == -1) {
			log.debug('updateTokenstation()', 'Could not find Tokenstation');
			return;
		}
		Vue.set(tokenstationsRef.value, index, updatedTokenstation);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}

type DeleteTokenstationStateOption = {
	key: Pick<Tokenstation, 'id'>;
}
async function deleteTokenstation(options: DeleteTokenstationStateOption): Promise<void> {
	log.debug('deleteTokenstation()', options);
	isLoadingRef.value = true;
	try {
		await api.deleteTokenstation(options);
		tokenstationsRef.value = tokenstationsRef.value.filter((tokenstation) => tokenstation.id !== options.key.id);
		isLoadingRef.value = false;
	} catch (error) {
		isLoadingRef.value = false;
		throw error;
	}
}