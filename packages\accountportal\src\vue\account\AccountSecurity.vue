<template>
  <div>
    <b-card class="mb-4">
        <PWDSettings v-if="cognitoUser" :cognitoUser="cognitoUser"></PWDSettings>
    </b-card>
    <b-card>
        <MFASettings v-if="cognitoUser" :cognitoUser="cognitoUser"></MFASettings>
    </b-card>
  </div>
</template>
<script>

import MFASettings from './MFASettings.vue'
import PWDSettings from './PWDSettings.vue'

export default {
  props: ['cognitoUser'],
  data() { return {
    cardTitle: 'Meine Details'
  }},
  components: { MFASettings, PWDSettings },
  computed:{

  },
}
</script>
<style scoped>

</style>
