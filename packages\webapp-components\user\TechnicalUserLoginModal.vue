<template>
  <b-modal
    v-model="modalOpen"
    centered
    title="Technischer User Login"
    hide-footer
  >
    <b-row class="mt-2">
      <b-col>Login mit QR-Code:</b-col>
      <b-col>
        <QRCode
          :value="qrCodeValue"
          size="250"
          level="H"
        />
      </b-col>
    </b-row>
    <b-row
      v-if="currentTechnicalUser"
      class="mt-2"
    >
      <b-col>Login mit Passwort: </b-col>
      <b-col>{{ currentTechnicalUser.technicalPassword }}</b-col>
    </b-row>
  </b-modal>
</template>
<script lang="ts">
import { defineComponent, ref, computed } from '@vue/composition-api';
import type { TechnicalInnUser } from '@innevent/webapp-types';
import QRCode from 'qrcode.vue';


export default defineComponent({
	components: {
		QRCode
	},
	setup(props, { emit }) {
		const modalOpen = ref<boolean>(false);
		const currentTechnicalUser = ref<TechnicalInnUser>();
		
		function open(technicalUser: TechnicalInnUser) {
			modalOpen.value = true;
			currentTechnicalUser.value = technicalUser;

			//Generate QR-Code
		}

		const qrCodeValue = computed(() => {
			return JSON.stringify({
				email: currentTechnicalUser.value?.email, 
				password: currentTechnicalUser.value?.technicalPassword
			});
		});


		return {
			modalOpen,
			currentTechnicalUser,
			open,
			qrCodeValue
		};
	}
});
</script>
<style>
</style>

