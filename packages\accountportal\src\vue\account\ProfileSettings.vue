<template>
  <div>
    <h3>Profil</h3>
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
    <form class="mt-3 max-w600" v-else>
      <div class="form-row mb-3">
        <div class="col-6">
          <label>Titel</label>
          <b-form-select v-model="userData['custom:titel']" :options="titelSelection"></b-form-select>
        </div>
        <div class="col-6">
          <label>Geschlecht</label>
          <b-form-select v-model="userData.gender" :options="genderSelection"></b-form-select>
        </div>
      </div>
      <div class="form-row">
        <div class="form-label-group col-6">
          <input v-model="userData.given_name" type="text" id="inputGivenname" placeholder="Max" class="form-control">
          <label for="inputGivenname">Vorname</label>
        </div>
        <div class="form-label-group col-6">
          <input v-model="userData.family_name" type="text" id="inputFamilyname" placeholder="Mustermann" class="form-control">
          <label for="inputFamilyname">Nachname</label>
        </div>
      </div>
      <div class="form-label-group">
        <input v-model="userData['custom:phonenumber']" type="text" id="inputMobileNumber" class="form-control">
        <label for="inputMobileNumber">Handynummer</label>
      </div>
      <SingleSaveButton @save="save"></SingleSaveButton>
    </form>
  </div>
</template>
<script>
import { Auth } from 'aws-amplify'
import LoadingSpinner from '../tools/LoadingSpinner.vue'
import SingleSaveButton from '../buttons/SingleSaveButton.vue'
var validator = require('validator');

export default {
  props: ['selectedVendor', 'events', 'selectedEvent'],
  created(){
    this.getUserData()
  },
  data(){ return{
    isLoading: true,
    userData:{},
    genderSelection:[
      { value: 'null', text: "Nicht ausgewählt" },
      { value: 'male', text: "Männlich" },
      { value: 'female', text: "Weiblich" },
      { value: 'divers', text: "Divers" }
    ],
    titelSelection:[
      { value: 'null', text: "Kein Titel" },
      { value: 'Dr.', text: "Dr." },
      { value: 'Prof.', text: "Prof." }
    ]
  }},
  components: { LoadingSpinner, SingleSaveButton},
  computed:{

  },
  methods:{
    async getUserData(){
      this.userData = (await Auth.currentUserInfo()).attributes
      this.isLoading = false
    },
    async save(btn){
      //Validate Phonenumber
      if(this.userData['custom:phonenumber'] && !validator.isMobilePhone(this.userData['custom:phonenumber'])){
        this.$bvToast.toast('Bitte gebe deine richtige Handynummer ein', {
          title: 'Handynummer fehlerhaft',
          autoHideDelay: 3000,
          variant: 'danger'
        })
        btn.setError(btn, "Eingabe fehlerhaft")
        return
      }

      let user = await Auth.currentAuthenticatedUser();

      Auth.updateUserAttributes(user, this.userData).then((data) => {
        btn.setSuccess(btn)
      }).catch(e => {
          btn.setError(btn, "Eingabe fehlerhaft")
      })
    }
  }
}
</script>
<style scoped>

</style>
