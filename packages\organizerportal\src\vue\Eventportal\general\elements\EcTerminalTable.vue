<template>
  <div>
    <b-row>
      <b-col><h2>EC-Terminal Übersicht</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="EC-Terminal erstellen"
          icon="plus-square"
          @click="btnCreateEcTerminal"
        />
      </b-col>
    </b-row>
    <b-table
      id="ecTerminalTableRef"
      ref="ecTerminalTableRef"
      hover
      :busy="isLoadingInitialRef"
      :items="ecTerminalsRef"
      :fields="tableFields"
      class="mt-3 table-clickable"
      striped
      :show-empty="true"
      empty-text="Keine EC Terminals vorhanden"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(currentCredit)="data">
        {{ `${data.value} €` }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteEcTerminal($event, data)"
          />
        </div>
      </template>
    </b-table>
    <EcTerminalSidebar 
      ref="ecTerminalSidebarRef"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import { useEcTerminalState } from '../../../../states';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import LoadButton from '../../../buttons/LoadButton.vue';
import DeleteButton from '../../../buttons/DeleteButton.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import type { EcTerminal } from '@innevent/types';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { currentEventRef } from '../../../../states/eventState';
import EcTerminalSidebar from './EcTerminalSidebar.vue';
export default defineComponent({
	components: {
		TableBusyLoader,
		LoadButton,
		DeleteButton,
		EcTerminalSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const { ecTerminalsRef, isLoadingInitialRef, deleteEcTerminal, createEcTerminal } = useEcTerminalState();
		const ecTerminalTableRef = ref<BTable>();
		const ecTerminalSidebarRef = ref<InstanceType<typeof EcTerminalSidebar>>();
		const tableFields: BvTableFieldArray =  [
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'currentCredit', label: 'Aktueller Betrag', sortable: true },
			{ key: 'buttons', label: '' }
		];

		async function btnCreateEcTerminal(btn: ResetButton) {
			try {
				await createEcTerminal({
					key: {
						eventId: currentEventRef.value!.eventId!
					},
					data: {
						name: 'Neues EC-Terminal'
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		async function btnDeleteEcTerminal(btn: ResetButton, cellData: BvTableCellData<EcTerminal>) {
			try {
				const ecTerminal = cellData.item;
				await deleteEcTerminal({
					key: { 
						ecTerminalId: ecTerminal.ecTerminalId,
						eventId: ecTerminal.eventId! 
					}
				});
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(ecTerminal: EcTerminal) {
			const ecTerminalObj: EcTerminal = JSON.parse(JSON.stringify(ecTerminal));
			ecTerminalSidebarRef.value!.openForEdit(ecTerminalObj);
		}


		return {
			tableFields,
			ecTerminalTableRef,
			isLoadingInitialRef, 
			ecTerminalsRef,
			btnCreateEcTerminal,
			btnDeleteEcTerminal,
			ecTerminalSidebarRef,
			tableRowClick
		};
	}
});
</script>

<style>
</style>
