<template>
  <b-card class="mt-3">
    <b-modal
      :id="'saModal_' + salesArea.id"
      title="Verkaufszone bearbeiten"
    >
      <div class="form-label-group">
        <input
          id="input1"
          v-model="salesArea.name"
          type="text"
          placeholder="Name"
          required="required"
          class="form-control"
        >
        <label for="input1">Name</label>
      </div>
      <div class="form-label-group">
        <b-form-input
          id="input2"
          v-model="salesArea.description"
          placeholder="Beschreibung"
        />
        <label for="input2">Beschreibung</label>
      </div>
      <template #modal-footer="{ hide }">
        <DeleteButton
          text="Verkaufszone löschen"
          @click="deleteSalesArea($event, hide)"
        />
        <SaveButton @click="updateSalesArea($event, hide)" />
      </template>
    </b-modal>
    <template #header>
      <b-row>
        <b-col>
          <h4>{{ salesArea.name }}</h4>
        </b-col>
        <b-col class="d-flex">
          <b-button
            v-b-modal="'saModal_' + salesArea.id"
            class="ml-auto"
            variant="warning"
            size="sm"
            squared
            style="height: fit-content"
          >
            <FontAwesomeIcon :icon="['fas', 'edit']" /> Verkaufszone bearbeiten
          </b-button>
        </b-col>
      </b-row>
    </template>
    <b-row align-h="between">
      <b-col cols="2">
        <h6>Verfügbare Artikelgruppen:</h6>
      </b-col>
      <b-col cols="10">
        <multiselect
          v-model="salesArea.ArticleGroups"
          placeholder="Artikelgruppen auswählen"
          label="name"
          track-by="id"
          :options="availableArticleGroups"
          :multiple="true"
          :hide-selected="true"
          :searchable="false"
          @select="assignArticleGroup"
          @remove="removeArticleGroup"
        />
      </b-col>
    </b-row>
    <b-row align-h="between">
      <b-col cols="2">
        <h6>Verfügbare Pfandrückgaben:</h6>
      </b-col>
      <b-col cols="10">
        <multiselect
          v-model="salesArea.DepositGroups"
          placeholder="Pfandgruppen auswählen"
          label="name"
          track-by="id"
          :options="availableDepositGroups"
          :multiple="true"
          :hide-selected="true"
          :searchable="false"
          @select="assignDepositGroup"
          @remove="removeDepositGroup"
        />
      </b-col>
    </b-row>

    <b-row class="mt-4">
      <b-col><h4>Mitarbeiter</h4></b-col>
      <b-col class="d-flex">
        <b-button
          v-b-modal="&quot;assignEmployeeModal&quot; + salesArea.id"
          class="ml-auto"
          variant="primary"
          size="sm"
        >
          <FontAwesomeIcon icon="plus-square" /> Mitarbeiter hinzufügen
        </b-button>
      </b-col>
    </b-row>
    <b-table
      hover
      :items="salesAreaEmployees"
      :fields="tableFieldsPerms"
      class="mt-3 table-clickable"
      head-variant="light"
    >
      <template #cell(employee)="data">
        {{ data.item.firstname + " " + data.item.lastname }}
      </template>
      <template #cell(permissions)="data">
        <LoadButton
          v-for="perm in availableSalesAreaPermissions"
          :key="perm.urn"
          size="sm"
          class="mr-1"
          icon="random"
          :text="perm.badge"
          :variant="permissionMatch(data.item, perm) ? 'primary':'outline-secondary'"
          @click="changePermission($event, data.item, perm)"
        />
      </template>
    </b-table>
    <b-modal
      :id="&quot;assignEmployeeModal&quot; + salesArea.id"
      ref="modal"
      title="Mitarbeiter hinzufügen"
      hide-footer
      centered
    >
      <b-list-group>
        <b-list-group-item
          v-for="u in selectableEmployeeModal"
          :key="u.id"
          button
          @click="addUserToSalesArea('perm:innorder.salesarea.use', u, salesArea.id)"
        >
          {{ u.firstname + " " + u.lastname }}
        </b-list-group-item>
      </b-list-group>
    </b-modal>
  </b-card>
</template>
<script>
import { API } from 'aws-amplify';
import LoadingSpinner from '../../LoadingSpinner.vue';
import LoadButton from '../../buttons/LoadButton.vue';
import SaveButton from '../../buttons/SaveButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
const uuid = require('uuid/v4');


export default {
	components: { LoadingSpinner, LoadButton, DeleteButton, SaveButton },
	props: ['selectedVendor', 'selectedEvent', 'salesArea', 'vendorEmployeeList', 'availableArticleGroups', 'availableDepositGroups'],
	data() { return {
		selectedArticleGroups: null,
		permSalesArea: null,
		salesAreaEmployees: [],
		cognitoUsers: [],
		tableFieldsPerms: [
			{ key: 'employee', label: 'Mitarbeiter', sortable: true },
			{ key: 'permissions', label: 'Berechtigungen' },
			{ key: 'buttons', label: '' }
		],
		availableSalesAreaPermissions:[
			{ value: false, urn: 'perm:innorder.salesarea.use', badge: 'SalesAreaUse', name: 'Verkaufszone benutzenn', description: 'Mitarbeiter darf in dieser Verkaufszone arbeiten' },
			{ value: false, urn: 'perm:innorder.salesarea.admin', badge: 'SalesAreaAdmin', name: 'Verkaufszonen Admin', description: 'Mitarbeiter mit allen Berechtigungen in einer Verkaufszone' }
		]
	};},
	computed:{
		selectableEmployeeModal() {
			let selectable = [];
			if (!this.vendorEmployeeList) {
				return selectable;
			}
			for (var ve of this.vendorEmployeeList) {
				let s = this.salesAreaEmployees.some(function(el) {
					return el.id === ve.id;
				});
				if (!s) {
					selectable.push(ve);
				}
			}
			return selectable;
		}
	},
	created() {
		this.reloadPermSalesArea();
	},
	methods:{
		assignArticleGroup(option, id) {
			let createdSAAG = {
				ArticleGroupId: option.id,
				SalesAreaId: this.salesArea.id
			};

			API.put('innorder', '/SalesArea/'+ this.salesArea.id +'/ArticleGroup/' + option.id, { body: createdSAAG }).then(response => {
				this.$bvToast.toast('Die Zuweisung wurde erfolgreich hinzugefügt.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 2000,
					variant: 'success'
				});
			}).catch(error => {
				this.$bvToast.toast('Beim Hinzufügen der Zuweisung ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		removeArticleGroup(option, id) {
			let removeID = null;
			let filtered = this.salesArea.ArticleGroups.filter(function(assignedA, index, arr) {
				if (assignedA.id == option.id) {
					removeID = option.id;
					return true;
				} else {
					return false;
				}
			});
			if (removeID) {
				API.del('innorder', '/SalesArea/'+ this.salesArea.id +'/ArticleGroup/' + option.id).then(response => {
					this.$bvToast.toast('Die Zuweisung wurde erfolgreich gelöscht.', {
						title: 'Ändern Erfolgreich',
						autoHideDelay: 2000,
						variant: 'success'
					});
				}).catch(error => {
					this.$bvToast.toast('Beim Löschen der Zuweisung ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
						title: 'Laden Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
				});
			}
		},

		assignDepositGroup(option, id) {
			let createdSAAG = {
				DepositGroupId: option.id,
				SalesAreaId: this.salesArea.id
			};

			API.put('innorder', '/SalesArea/'+ this.salesArea.id +'/DepositGroupReturn/' + option.id, { body: createdSAAG }).then(response => {
				this.$bvToast.toast('Die Zuweisung wurde erfolgreich hinzugefügt.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 2000,
					variant: 'success'
				});
			}).catch(error => {
				this.$bvToast.toast('Beim Hinzufügen der Zuweisung ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		removeDepositGroup(option, id) {
			let removeID = null;
			let filtered = this.salesArea.DepositGroups.filter(function(assignedA, index, arr) {
				if (assignedA.id == option.id) {
					removeID = option.id;
					return true;
				} else {
					return false;
				}
			});
			if (removeID) {
				API.del('innorder', '/SalesArea/'+ this.salesArea.id +'/DepositGroupReturn/' + option.id).then(response => {
					this.$bvToast.toast('Die Zuweisung wurde erfolgreich gelöscht.', {
						title: 'Ändern Erfolgreich',
						autoHideDelay: 2000,
						variant: 'success'
					});
				}).catch(error => {
					this.$bvToast.toast('Beim Löschen der Zuweisung ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
						title: 'Laden Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
				});
			}
		},

		reloadPermSalesArea() {
			this.salesAreaEmployees = [];
			for (var perm of this.salesArea.Permissions) {
				let s = this.salesAreaEmployees.some(function(el) {
					return el.id === perm.VendorEmployee.id;
				});
				if (!s) {
					this.salesAreaEmployees.push(perm.VendorEmployee);
				}

			}
		},
		permissionMatch(dataItem, perm) {
			let vendorEmployeeId = dataItem.id;
			for (let p of this.salesArea.Permissions) {
				if (p.permission == perm.urn && p.VendorEmployee.id == vendorEmployeeId) {
					dataItem[perm.urn] = true;
					return true;
				}
			}
			dataItem[perm.urn] = false;
			return false;
		},
		addUserToSalesArea(permURN, u, salesAreaID) {
			this.$refs.modal.hide();
			this.createPermRequest(permURN, u.id, salesAreaID).then(createdPerm => {
				delete createdPerm.VendorEmployeeId;
				createdPerm.VendorEmployee = u;
				this.salesArea.Permissions.push(createdPerm);
				this.reloadPermSalesArea();
			});
		},
		async createPermRequest(permURN, employeeID, salesAreaID) {
			return new Promise((resolve, reject) => {
				let createdPerm = {
					id: uuid(),
					permission: permURN,
					VendorEmployeeId: employeeID,
					SalesAreaId: salesAreaID
				};
				API.post('innorder', '/SalesArea/'+ salesAreaID +'/Permission', { body: createdPerm }).then(response => {
					this.$bvToast.toast('Die Berechtigung wurde erfolgreich gesetzt.', {
						title: 'Berechtigung setzen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});

					resolve(createdPerm);
				}).catch(error => {
					this.$bvToast.toast('Beim Setzen der Berechtigung ist ein Fehler aufgetreten...', {
						title: 'Berechtigung setzen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					reject(error);
				});
			});
		},
		changePermission(btn, dataItem, perm) {
			let currentlyAssigned = dataItem[perm.urn];
			let newAssigned = !currentlyAssigned;
			if (newAssigned) { // Create Permission
				this.createPermRequest(perm.urn, dataItem.id, this.salesArea.id).then(createdPerm => {
					delete createdPerm.VendorEmployeeId;
					createdPerm.VendorEmployee = dataItem;
					this.salesArea.Permissions.push(createdPerm);
					btn.reset();
				}).catch(error => {
					btn.reset();
				});
			} else { // Remove Permission
				let removeID = null;
				let filtered = this.salesArea.Permissions.filter(function(assignedP, index, arr) {
					if (dataItem.id == assignedP.VendorEmployee.id && assignedP.permission == perm.urn) {
						removeID = assignedP.id;
						return false;
					} else {
						return true;
					}
				});
				API.del('innorder', '/SalesArea/Permission/' + removeID).then(response  => {
					this.$bvToast.toast('Die Berechtigung wurde erfolgreich gelöscht.', {
						title: 'Löschen Erfolgreich',
						autoHideDelay: 3000,
						variant: 'success'
					});
					this.salesArea.Permissions.splice(this.salesArea.Permissions.findIndex(function(i) {
						return i.id === removeID;
					}), 1);

					let userHasPermInSalesArea = this.salesArea.Permissions.filter(perm => {
						return perm.VendorEmployee.id == dataItem.id;
					});
					if (userHasPermInSalesArea.length == 0) {
						this.salesAreaEmployees.splice(this.salesAreaEmployees.findIndex(function(i) {
							return i.id === dataItem.id;
						}), 1);
					}

					btn.reset();
				}).catch(error => {
					this.$bvToast.toast('Beim Löschen der Berechtigung ist ein Fehler aufgetreten. StatusCode: ' + httpStatus[error.response.status], {
						title: 'Löschen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
			}
			dataItem[perm.urn] = newAssigned;
		},
		deleteSalesArea(btn, hideModal) {
			API.del('innorder', '/SalesArea/' + this.salesArea.id).then(response  => {
				btn.reset();
				hideModal();
				this.$emit('deleted');
			}).catch(error => {
				this.$bvToast.toast('Beim Löschen von "' + this.salesArea.name + '" ist ein Fehler aufgetreten.', {
					title: 'Löschen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		updateSalesArea(btn, hideModal) {
			API.put('innorder', '/SalesArea/' + this.salesArea.id, { body: this.salesArea }).then(response  => {
				this.$bvToast.toast('Die SalesArea "' + this.salesArea.name + '" wurde erfolgreich geändert.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
				hideModal();
			}).catch(error => {
				this.$bvToast.toast('Beim Ändern von "' + this.salesArea.name + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Ändern Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		}
	}
};
</script>
