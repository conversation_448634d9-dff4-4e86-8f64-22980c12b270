<template>
  <b-navbar
    toggleable="xl"
    type="dark"
    variant="dark"
    sticky
  >
    <b-container
      fluid
      :class="containerClass"
    >
      <b-navbar-brand to="/">
        <img
          height="40"
          src="../img/logo-transparent.png"
        >{{ headerText }}
      </b-navbar-brand>

      <b-navbar-toggle target="nav-collapse" />

      <b-collapse
        id="nav-collapse"
        is-nav
      >
        <b-navbar-nav>
          <b-nav-item :href="urlVendorPortal">
            Verkäuferportal
          </b-nav-item>
          <b-nav-item :href="urlCustomerPortal">
            Kundenportal
          </b-nav-item>
        </b-navbar-nav>

        <b-navbar-nav
          class="ml-auto"
          pills
        >
          <b-nav-form class="mr-4">
            <b-button-group>
              <b-dropdown
                id="portalObjectSelect"
                right
                :text="selectText"
                variant="outline-light"
              >
                <b-dropdown-group>
                  <b-dropdown-header>
                    <FontAwesomeIcon
                      icon="calendar-day"
                      fixed-width
                    /><span
                      class="ml-3"
                    >Events</span>
                  </b-dropdown-header>
                  <b-dropdown-item
                    v-for="event of selectableEvents"
                    :key="event.eventId"
                    :to="{ name: 'eBasedata', params: { event: event.eventId } }"
                  >
                    {{ event.name }}
                  </b-dropdown-item>
                  <b-dropdown-item
                    v-b-modal.modalCreateEvent
                    variant="primary"
                  >
                    <FontAwesomeIcon
                      icon="plus-circle"
                      fixed-width
                    /><span
                      class="ml-3"
                    >Event erstellen</span>
                  </b-dropdown-item>
                </b-dropdown-group>
                <b-dropdown-divider />
                <b-dropdown-group>
                  <b-dropdown-header>
                    <FontAwesomeIcon
                      icon="building"
                      fixed-width
                    /><span
                      class="ml-3"
                    >Veranstalter</span>
                  </b-dropdown-header>
                  <b-dropdown-item-button
                    v-for="organizer of selectableOrganizers"
                    :key="organizer.organizerId"
                    :to="{ name: 'oDashboard', params: { organizer: organizer.organizerId } }"
                  >
                    {{ organizer.organizationName }}
                  </b-dropdown-item-button>
                  <b-dropdown-item-button
                    v-b-modal.modalCreateOrganizer
                    variant="primary"
                  >
                    <FontAwesomeIcon
                      icon="plus-circle"
                      fixed-width
                    /><span
                      class="ml-3"
                    >Veranstalter erstellen</span>
                  </b-dropdown-item-button>
                </b-dropdown-group>
              </b-dropdown>
              <b-button
                :to="{ name: 'portalHome', params: {} }"
                variant="outline-light"
              >
                Übersicht
              </b-button>
            </b-button-group>
          </b-nav-form>
          <b-nav-item-dropdown right>
            <template #button-content>
              <FontAwesomeIcon
                icon="user-circle"
                fixed-width
              /><span
                class="ml-1 mr-1"
              >{{ btnAccountText }}</span>
            </template>
            <b-dropdown-text class="text-center">
              {{ accountName }}
            </b-dropdown-text>
            <b-dropdown-text>{{ accountEmail }}</b-dropdown-text>
            <b-dropdown-divider />
            <b-dropdown-item :href="urlVendorPortal + '/account'">
              <FontAwesomeIcon
                icon="user-cog"
                fixed-width
              /><span
                class="ml-3"
              >Kontoeinstellungen</span>
            </b-dropdown-item>
            <b-dropdown-item @click="logout()">
              <FontAwesomeIcon
                icon="power-off"
                fixed-width
              /><span
                class="ml-3"
              >Abmelden</span>
            </b-dropdown-item>
          </b-nav-item-dropdown>
        </b-navbar-nav>
      </b-collapse>
    </b-container>
  </b-navbar>
</template>
<script lang="ts">

import { computed, defineComponent } from '@vue/composition-api';
import { Auth } from 'aws-amplify';
import { useOrganizerState } from '../states/organizerState';
import { useEventState } from '../states/eventState';
import { useUserState } from '../states/userState';

export default defineComponent({
	props: {
		containerClass: { type: String, default: 'max-w1600' }
	},
	setup() {
		const { permittedOrganizersRef, currentOrganizerRef } = useOrganizerState();
		const { permittedEventsRef, currentEventRef } = useEventState();
		const { currentUserRef } = useUserState();

		const headerText = `Inn//Event Manage${ process.env.STAGE == 'prod' ? '' : '__' + process.env.STAGE }`;
		const btnAccountText = 'Mein Konto';
		const urlAccountPortal = process.env.URL_INNLOGIN;
		const urlCustomerPortal = process.env.URL_CUSTOMERPORTAL;
		const urlVendorPortal = process.env.URL_VENDORPORTAL;

		const selectableEvents = computed(() => {
			return permittedEventsRef.value.filter(event => event.eventId !== currentEventRef.value?.eventId);
		});
		const selectableOrganizers = computed(() => {
			return permittedOrganizersRef.value.filter(organizer => organizer.organizerId !== currentOrganizerRef.value?.organizerId);
		});

		const accountEmail = computed(() => {
			const user = currentUserRef.value;
			if (!user) return '';
			return user.attributes.email;
		});

		const accountName = computed(() => {
			const user = currentUserRef.value;
			if (!user) { return ''; }
			return `${user.attributes.given_name ?? ''} ${user.attributes.family_name ?? ''}`;
		});

		const selectText = computed(() => {
			return currentEventRef.value?.name ?? currentOrganizerRef.value?.organizationName ?? 'Event oder Veranstalter auswählen';
		});
    
		async function logout() {
			await Auth.signOut();
			window.location.href = urlAccountPortal + '/logout';
		}

		return {
			urlCustomerPortal,
			urlAccountPortal,
			urlVendorPortal,
			accountEmail,
			btnAccountText,
			headerText,
			logout,
			selectText,
			selectableEvents,
			selectableOrganizers,
			accountName
		};
	}
});
</script>
<style scoped>
#portalObjectSelect {
  min-width: 250px;
  cursor: pointer;
}
</style>
