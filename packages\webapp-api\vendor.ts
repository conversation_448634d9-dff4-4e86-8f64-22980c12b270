import type { Vendor } from '@innevent/webapp-types';
import { apiInnOrder } from './instances';

type ModelType = Vendor;
type PrimaryKey = 'id';

export async function getPermittetVendors(): Promise<ModelType[]> {
	const response = await (await apiInnOrder()).get('/Vendor');
	return response.data;
}

export async function getVendor(vendorId: string): Promise<Required<ModelType>> {
	const response = await (await apiInnOrder()).get(`/getVendorPortalData/Vendor/${vendorId}`);
	return response.data;
}

export type VendorCreateOptions = {
	data: Pick<ModelType, 'name' | 'organizationType' | 'organizationName' | 'postalcode' | 'city' | 'housenumber' | 'street'>;
}
export async function createVendor(options: VendorCreateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).post('/Vendor', options.data);
	const newVendor = {
		id: response.data,
		...options.data
	} as Vendor;
	return newVendor;
}

export type VendorDeleteOptions = {
	key: Pick<ModelType, 'id'>;
}
export async function deleteVendor(options: VendorDeleteOptions): Promise<void> {
	await (await apiInnOrder()).delete(`/Vendor/${options.key.id}`);
}

export type EventUpdateOptions = {
	key: Pick<ModelType, 'id'>;
	data: Pick<ModelType, 'name' | 'organizationType' | 'organizationName' | 'postalcode' | 'city' | 'housenumber' | 'street'>;
}
export async function updateVendor(options: EventUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnOrder()).put(`/Vendor/${options.key.id}`, options.data);
	return response.data;
}