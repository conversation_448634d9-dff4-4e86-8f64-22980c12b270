<template>
  <div>
    <h2>Eventübersicht</h2>
    <b-row>
      <b-col cols="3">
        <b-form-select
          v-model="selectedOption"
          :options="selectOptions"
        />
      </b-col>
    </b-row>
    <b-table
      id="eventsOverviewTable"
      hover
      :items="tableItems"
      :fields="tableFields"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="rowClick"
    />
  </div>
</template>
<script>

export default {
	components: { },
	props: ['selectedVendor'],
	data() { return {
		selectedOption: 'coming',
		selectOptions: [
			{ text: 'Alle', value: 'all' },
			{ text: 'Vergangene Events', value: 'past' },
			{ text: 'Kommende Events', value: 'coming' }
		],
		tableFields:[
			{ key: 'name', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'OrganizerId', label: 'Veranstalter', sortable: true }
		]
	};},
	computed:{
		tableItems() {
			let tableArray = [];
			for (let eventProp of this.selectedVendor.VendorEventProperties) {
				let ev = eventProp.Event;
				let now = new Date();
				let evDate = new Date(ev.dateTo);
				if (this.selectedOption == 'coming' && ev.dateTo == null || evDate > now) {
					tableArray.push(ev);
				}
				else if (this.selectedOption == 'past' && ev.dateTo != null && evDate < now) {
					tableArray.push(ev);
				}
				else if (this.selectedOption == 'all') {
					tableArray.push(ev);
				}
			}
			return tableArray;
		}
	},
	methods:{
		rowClick(row) {
			this.$router.push({ name: 'eDashboard', params: { event: row.id } });
		}
	}
};
</script>
