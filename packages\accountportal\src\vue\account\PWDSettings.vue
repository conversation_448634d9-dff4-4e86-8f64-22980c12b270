<template>
  <div>
    <h3>Passwort</h3>
    <form class="mt-3 max-w300">
      <h4>Änderung</h4>
      <div class="form-label-group">
        <input v-model="data.oldPassword" type="password" id="inputOldPass"  placeholder="Aktuelles Passwort" required="required" class="form-control">
        <label for="inputOldPass">Aktuelles Passwort</label>
      </div>
      <div class="form-label-group">
        <input v-model="data.newPassword" type="password" id="inputNewPass" placeholder="Neues Passwort" required="required" class="form-control">
        <label for="inputNewPass">Neues Passwort</label>
      </div>
      <div class="form-label-group">
        <input v-model="data.newPasswordRepeat" type="password" id="inputNewPassRe" placeholder="Neues Passwort wiederholen"  required="required" class="form-control">
        <label for="inputNewPassRe">Neues Passwort wiederholen</label>
      </div>
      <div class="d-flex justify-content-end">
        <SaveButton class="text-right" @click="save($event)">Passwort ändern!</SaveButton>
      </div>
    </form>
  </div>
</template>
<script>
import { Auth } from 'aws-amplify'
import LoadingSpinner from '../tools/LoadingSpinner.vue'
import SaveButton from '../buttons/SaveButton.vue'
const uuid = require('uuid/v4')
var validator = require('validator');


export default {
  props: ['selectedVendor', 'events', 'selectedEvent'],
  created(){

  },
  data(){ return{
    data:{}
  }},
  components: { LoadingSpinner, SaveButton},
  computed:{

  },
  methods:{
    reset(){
      this.data.oldPassword = ''
      this.data.newPassword = ''
      this.data.newPasswordRepeat = ''
    },
    async save(btn){
      //Validate
      if(this.data.oldPassword == null || this.data.newPassword == null || this.data.newPasswordRepeat == null ||
      this.data.oldPassword == ''|| this.data.newPassword == '' || this.data.newPasswordRepeat == ''){
        this.$bvToast.toast('Die Felder dürfen nicht leer sein', {
          title: 'Fehler bei Passwortänderung',
          autoHideDelay: 3000,
          variant: 'danger'
        })
        this.reset()
        btn.reset()
        return
      }
      if(this.data.newPassword !== this.data.newPasswordRepeat){
        this.$bvToast.toast('Deine neuen Passwörter stimmen nicht mit einander überein', {
          title: 'Fehler bei Passwortänderung',
          autoHideDelay: 3000,
          variant: 'danger'
        })
        this.reset()
        btn.reset()
        return
      }


      Auth.currentAuthenticatedUser()
      .then(user => {
          return Auth.changePassword(user, this.data.oldPassword, this.data.newPassword);
      })
      .then(data => {
        this.$bvToast.toast('Dein Passwort wurde erfolgreich geändert', {
          title: 'Passwortänderung',
          autoHideDelay: 3000,
          variant: 'success'
        })
        this.reset()
        btn.reset()
      })
      .catch(err => {
        this.$bvToast.toast(err.message, {
          title: 'Fehler bei Passwortänderung',
          autoHideDelay: 3000,
          variant: 'danger'
        })
        btn.reset()
      });
    }
  }
}
</script>
