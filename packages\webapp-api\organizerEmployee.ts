import { apiInnEvent } from './instances';
import type { OrganizerEmployee } from '@innevent/types';

type ModelType = OrganizerEmployee;

export type GetOrganizerEmployeeOptions =  {
    key: Pick<ModelType, 'organizerId'>;
};
export async function getEventEmployee(options: GetOrganizerEmployeeOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).get('/OrganizerEmployee', { 
		params: { 
			organizerId: options.key.organizerId 
		} 
	});
	return response.data;
}