export declare type ValidationDetails = {
    message?: string;
    path?: string[];
    type?: string;
    context?: {
        label?: string;
        key?: string;
    };
};
export declare type InnEventError = {
    isInnEventError: true;
    name: 'InnEventError';
    errorCode: string;
    statusCode: 400 | 401 | 403 | 404 | 409 | 500 | 501;
    message: string;
    fields?: string[];
    headers?: string[];
    queryParameters?: string[];
    validationDetails?: ValidationDetails[];
};