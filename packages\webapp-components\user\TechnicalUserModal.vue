<template>
  <b-modal
    v-model="modalOpen"
    centered
    :title="`Technischen User ${actionRef === 'create' ? 'erstellen' : 'bearbeiten'}`"
  >
    <b-row
      v-for="(field, fieldKey) of inputFields"
      :key="fieldKey"
      class="mt-2"
    >
      <b-col sm="3">
        <label :for="`field-${fieldKey}`">{{ field.name }}:</label>
      </b-col>
      <b-col
        sm="9"
      >
        <b-form-input
          :id="`field-${fieldKey}`"
          v-model="field.value"
          :type="field.type"
          @keyup.enter="onKeyupEnter()"
        />
      </b-col>
    </b-row>
    <template #modal-footer="{ cancel }">
      <b-button
        class="mr-auto"
        @click="cancel()"
      >
        Abbruch
      </b-button>
      <LoadButton
        ref="saveButton"
        variant="primary"
        :disabled="!attributesAreValid"
        @click="clickOnSave"
      />
    </template>
  </b-modal>
</template>
<script lang="ts">
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import LoadButton from '../button/LoadButton.vue';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { technicalUserVendor as technicalUserVendorApi } from '@innevent/webapp-api';
import type { ResetButton, ModalAction, TechnicalInnUser, TechnicalUserReferenceObjectType } from '@innevent/webapp-types';
import type { PropType } from '@vue/composition-api';


export default defineComponent({
	components: {
		LoadButton
	},
	props: {
		referenceObjectId: {
			type: String,
			required: true
		},
		referenceObjectType: {
			type: String as PropType<TechnicalUserReferenceObjectType>,
			required: true
		}
	},
	emits: ['technicalUserChanged'],
	setup(props, { emit }) {
		const actionRef = ref<ModalAction>('create');
		const modalOpen = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentTechnicalUser = ref<TechnicalInnUser>();
		const saveButton = ref<InstanceType<typeof LoadButton>>();

		// nur vorname wird kein user erstellt
		// ResetButton VS <InstanceType<typeof LoadButton


		async function clickOnSave(btn: ResetButton) {
			await saveTechnicalUser();
			btn.reset();
		}

		async function onKeyupEnter() {
			saveButton.value?.clickButton();
		}

		async function saveTechnicalUser(): Promise<void> {
			try {
				if (actionRef.value == 'create') {
					if (props.referenceObjectType == 'event') {
						await api.createTechnicalUser({
							referenceId: props.referenceObjectId,
							referenceObjectType: props.referenceObjectType,
							firstName: inputFields.value.firstName.value,
							lastName: inputFields.value.lastName.value
						});
					} else {
						await technicalUserVendorApi.createTechnicalUser({
							referenceId: props.referenceObjectId,
							referenceObjectType: props.referenceObjectType,
							firstName: inputFields.value.firstName.value,
							lastName: inputFields.value.lastName.value
						});
					}
					
					emit('technicalUserChanged');
				} else {
					if (props.referenceObjectType == 'event') {
						await api.updateTechnicalUser({
							userSubject: currentTechnicalUser.value!.userSubject,
							referenceObjectType: props.referenceObjectType,
							referenceId: props.referenceObjectId,
							firstName: inputFields.value.firstName.value,
							lastName: inputFields.value.lastName.value
						});
					} else {
						await technicalUserVendorApi.updateTechnicalUser({
							userSubject: currentTechnicalUser.value!.userSubject,
							referenceObjectType: props.referenceObjectType,
							referenceId: props.referenceObjectId,
							firstName: inputFields.value.firstName.value,
							lastName: inputFields.value.lastName.value
						});
					
					}
					
					emit('technicalUserChanged');
				}
				notifySuccess({ instance });
				modalOpen.value = false;
			} catch (error) {
				notifyError({ instance });
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			modalOpen.value = true;
			inputFields.value.firstName.value = '';
			inputFields.value.lastName.value = '';
		}

		function openForEdit(technicalUser: TechnicalInnUser) {
			actionRef.value = 'edit';
			modalOpen.value = true;
			currentTechnicalUser.value = technicalUser;
			inputFields.value.firstName.value = technicalUser.firstName;
			inputFields.value.lastName.value = technicalUser.lastName;
		}

		const inputFields = ref({
			firstName: {
				name: 'Vorname',
				type: 'text',
				value: ''
			},
			lastName: {
				name: 'Nachname',
				type: 'text',
				value: ''
			}
		});

		const attributesAreValid = computed(() => {
			return inputFields.value.firstName.value.length > 2 && inputFields.value.lastName.value.length > 2;
		});


		return {
			actionRef,
			modalOpen,
			inputFields,
			clickOnSave,
			saveButton,
			onKeyupEnter,
			attributesAreValid,

			openForCreate,
			openForEdit,
			saveTechnicalUser
		};
	}
});
</script>
<style>
</style>

