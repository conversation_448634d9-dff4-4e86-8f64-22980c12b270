import { apiInnAccess } from './instances';
import type { AccessArea } from '@innevent/types';

type ModelType = AccessArea;
type PrimaryKey = 'accessAreaId';


export type AccessAreaCreateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Pick<ModelType, 'accessAreaName' | 'description' | 'permittedTickets'>;
}
export async function createAccessArea(options: AccessAreaCreateOptions): Promise<ModelType> {
	const response = await (await apiInnAccess()).post('/AccessArea', options.data, { 
		params: { 
			eventId: options.key.eventId, 
			returnValue: 'ALL_NEW'  
		} 
	});
	return response.data;
}

export type AccessAreaDeleteOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
}
export async function deleteAccessArea(options: AccessAreaDeleteOptions): Promise<void> {
	await (await apiInnAccess()).delete(`/AccessArea/${options.key.accessAreaId}`, { 
		params: { eventId: options.key.eventId } 
	});
}


export type AccessAreaUpdateOptions = {
	key: Pick<ModelType, 'eventId' | PrimaryKey>;
	data: Pick<ModelType, 'accessAreaName' | 'description' | 'permittedTickets'>;
};

export async function updateAccessArea(options: AccessAreaUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnAccess()).patch(`/AccessArea/${options.key.accessAreaId}`, 
		{
			accessAreaName: options.data.accessAreaName,
			description: options.data.description,
			permittedTickets: options.data.permittedTickets
		}, 
		{ 
			params: { 
				eventId: options.key.eventId, 
				returnValue: 'ALL_NEW' 
			} 
		}
	);
	return response.data;
}


export type AccessAreaGetOptions =  {
    key: Pick<ModelType, 'eventId'>;
};
export async function listAccessAreas(options: AccessAreaGetOptions): Promise<ModelType[] | never> {
	const response = await (await apiInnAccess()).get('/AccessArea', { 
		params: { eventId: options.key.eventId } 
	});
	return response.data;
}