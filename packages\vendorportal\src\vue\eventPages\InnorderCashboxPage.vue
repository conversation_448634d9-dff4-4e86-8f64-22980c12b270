<template>
  <div>
    <b-row>
      <b-col><h2><PERSON><PERSON></h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="<PERSON>sse erstellen"
          icon="plus-square"
          @click="createCashbox"
        />
      </b-col>
    </b-row>
    <b-table
      hover
      :busy="!cashboxes"
      :items="cashboxes"
      :fields="cashboxesFields"
      class="mt-3 table-clickable"
      striped
      :show-empty="true"
      empty-text="Keine Kassen vorhanden"
      head-variant="light"
      @row-clicked="openSidebar"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(cash)="data">
        {{ formatCurrency(data.value) }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            :data="data"
            size="sm"
            class="ml-auto"
            @click="deleteCashbox"
          />
        </div>
      </template>
    </b-table>
    <CashboxSidebar
      ref="cashboxSidebar"
      v-model="sidebarVisible"
      :cashbox="selectedCashbox"
      :employees="vendorEmployees"
      @reloadData="reloadCashboxes"
    />
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';
import DeleteButton from '../buttons/DeleteButton.vue';
import CashboxSidebar from './elements/cashbox/CashboxSidebar.vue';

const uuid = require('uuid/v4');

export default {
	components: { LoadButton, DeleteButton, CashboxSidebar },
	props: ['selectedEvent', 'selectedVendor'],
	data() {
		return {
			cashboxes: [],
			cashboxesFields: [
				{
					key: 'name',
					label: 'Name',
					sortable: true
				},
				{
					key: 'description',
					label: 'Beschreibung'
				},
				{
					key: 'cash',
					label: 'Credit',
					sortable: true
				},
				{
					key: 'buttons',
					label: ''
				}
			],
			selectedCashbox: null,
			sidebarVisible: false,
			eventEmployees: [],
			vendorEmployees: []
		};
	},
	computed: {},
	watch: {
		sidebarVisible(sidebarVisible) {
			if (!sidebarVisible) this.selectedCashbox = null;
		}
	},
	created() {
		this.reloadCashboxes();
		this.reloadVendorEmployees();
		//this.reloadEventEmployees()
	},
	methods: {
		formatCurrency(val) {
			return new Intl.NumberFormat('de-DE', {
				style: 'currency',
				currency: 'EUR'
			}).format(val);
		},
		openSidebar(cashbox) {
			this.selectedCashbox = cashbox;
			this.sidebarVisible = true;
		},
		async deleteCashbox(btn) {
			let item = btn.data.item;
			await this.$refs.cashboxSidebar.deleteCashbox(btn);
			this.cashboxes = this.cashboxes.filter(
				(cashbox) => cashbox.id != item.id
			);
		},
		createCashbox(btn) {
			let item = {
				id: uuid(),
				name: 'Neue Kasse',
				VendorId: this.selectedVendor.id,
				cash: '0.00',
				VendorEmployees: []
			};
			API.post('core', '/Cashbox', { body: item })
				.then((response) => {
					this.$bvToast.toast(
						'Die Kasse "' + item.name + '" wurde erfolgreich erstellt.',
						{
							title: 'Erstellen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					this.cashboxes.push(item);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Erstellen von "' +
              item.name +
              '" ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		reloadCashboxes() {
			this.cashboxes = null;
			API.get('core', '/Cashbox?filterVendor=' + this.selectedVendor.id)
				.then((response) => {
					this.cashboxes = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der Kassen ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadEventEmployees() {
			API.get(
				'core',
				'/EventEmployee?include=["PermEventUser"]&filterEvent=' +
          this.selectedEvent.id
			)
				.then((response) => {
					this.eventEmployees = response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der EventEmployees ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadVendorEmployees() {
			API.get(
				'innorder',
				'/VendorEmployee?include=["PermVendorUser"]&filterVendor=' +
          this.selectedVendor.id
			)
				.then((response) => {
					this.vendorEmployees = response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der VendorEmployees ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		}
	}
};
</script>
<style scoped>
</style>
