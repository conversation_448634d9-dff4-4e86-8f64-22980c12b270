<template>
  <b-container
    class="mb-4"
    fluid
  >
    <b-modal
      :id="'agModal_' + ag.id"
      title="Artikelgruppe bearbeiten"
    >
      <div class="form-label-group">
        <input
          id="input1"
          v-model="ag.name"
          type="text"
          placeholder="Name"
          required="required"
          class="form-control"
        >
        <label for="input1">Name</label>
      </div>
      <div class="form-label-group">
        <b-form-input
          id="input2"
          v-model="ag.description"
          placeholder="Beschreibung"
        />
        <label for="input2">Beschreibung</label>
      </div>
      <div class="form-label-group">
        <b-form-input
          id="input3"
          v-model="ag.sequence"
          placeholder="Reihenfolge"
        />
        <label for="input3">Reihenfolge</label>
      </div>
      <b-form-row>
        <b-col>
          Aktiv <b-form-checkbox
            v-model="ag.active"
            switch
            size="lg"
          />
        </b-col>
        <b-col>Color <ColorPicker v-model="ag.color" /></b-col>
      </b-form-row>
      <template #modal-footer="{ hide }">
        <DeleteButton
          text="Artikelgruppe löschen"
          @click="deleteArticleGroup($event, hide)"
        />
        <SaveButton @click="saveArticleGroup($event, hide)" />
      </template>
    </b-modal>
    <b-row>
      <h3>{{ ag.name }}</h3>
      <span class="ml-2 mt-auto mb-2">{{ ag.description }}</span>
      <b-badge
        class="mt-auto mb-2 ml-2 mr-auto"
        :variant="ag.active ? 'success':'warning'"
        style="height: fit-content"
      >
        {{ ag.active ? 'Aktiv':'Inaktiv' }}
      </b-badge>
      <b-button
        v-b-modal="'agModal_' + ag.id"
        variant="warning"
        size="sm"
        squared
        right
        style="height: fit-content"
      >
        <FontAwesomeIcon :icon="['fas', 'edit']" /> Artikelgruppe bearbeiten
      </b-button>
    </b-row>
    <b-row>
      <b-table
        ref="selectableTable"
        hover
        selectable
        :items="articles"
        :fields="fields"
        :busy="loading"
        head-variant="light"
        @row-selected="onRowSelected"
      >
        <!-- Busy Table -->
        <template #table-busy>
          <div class="text-center text-primary my-2">
            <b-spinner class="align-middle" />
            <strong>Loading...</strong>
          </div>
        </template>
        <!-- Buttons -->
        <template #cell(buttons)="data">
          <b-button-group>
            <b-button
              squared
              variant="outline-secondary"
              size="sm"
              @click="copyArticle(data)"
            >
              <FontAwesomeIcon icon="copy" />
            </b-button>
            <DeleteButton
              :data="data"
              size="sm"
              @click="deleteArticle"
            />
            <CreateButton
              v-if="data.item.inCreation"
              :data="data"
              size="sm"
              @click="createArticle"
            />
            <SaveButton
              v-if="data.item.isChanged"
              :data="data"
              size="sm"
              @click="saveArticle"
            />
          </b-button-group>
        </template>
        <!-- Custom Data Rendering -->
        <template #cell(costNet)="data">
          {{ calcNet(data.item.costGross, data.item.vat) }}
        </template>
        <template #cell(vat)="data">
          <b-input-group
            append="%"
            style="width: 100px"
          >
            <b-form-input
              v-model="data.item[data.field.key]"
              @input="dataChange(data)"
            />
          </b-input-group>
        </template>
        <template #cell(costGross)="data">
          <b-input-group
            append="€"
            style="width: 130px"
          >
            <b-form-input
              v-model="data.item[data.field.key]"
              @input="dataChange(data)"
            />
          </b-input-group>
        </template>
        <template #cell(DepositGroupId)="data">
          <b-form-select
            v-model="data.item[data.field.key]"
            :options="depositGroups"
            @change="dataChange(data)"
          />
        </template>
        <template #cell(active)="data">
          <b-form-checkbox
            v-model="data.item[data.field.key]"
            switch
            size="lg"
            @change="dataChange(data)"
          />
        </template>
        <template #cell(sequence)="data">
          <b-form-input
            v-model="data.item[data.field.key]"
            style="width: 50px"
            @input="dataChange(data)"
          />
        </template>
        <!-- Color Picker -->
        <template #cell(color)="data">
          <ColorPicker
            v-model="data.item[data.field.key]"
            @change="dataChange(data)"
          />
        </template>
        <!-- Default Text Data Editing -->
        <template #cell()="data">
          <b-form-input
            v-model="data.item[data.field.key]"
            @input="dataChange(data)"
          />
        </template>
        <!-- Custom Footer -->
        <template #custom-foot>
          <b-tr>
            <b-th colspan="4">
              <b-button
                squared
                size="sm"
                @click="selectAllRows"
              >
                <FontAwesomeIcon
                  :icon="['far', 'check-square']"
                  size="lg"
                />
              </b-button>
              <b-button
                squared
                size="sm"
                @click="clearSelected"
              >
                <FontAwesomeIcon
                  :icon="['far', 'square']"
                  size="lg"
                />
              </b-button>
              <DeleteButton
                size="sm"
                text="Ausgewählte Löschen"
                :disabled="selected.length == 0"
                @click="deleteSelected"
              />
              <b-dropdown
                squared
                size="sm"
                variant="outline-secondary"
                :disabled="selected.length == 0"
              >
                <template #button-content>
                  <FontAwesomeIcon :icon="['fas', 'arrows-alt-v']" /> Ausgewählte verschieben
                </template>
                <b-dropdown-item
                  v-for="articleGroup in availableMoveArticleGroups"
                  :key="articleGroup.id"
                  :article-group="articleGroup"
                  @click="moveSelected(articleGroup)"
                >
                  {{ articleGroup.name }}
                </b-dropdown-item>
              </b-dropdown>
            </b-th>
            <b-th colspan="7">
              <b-button
                squared
                size="sm"
                class="float-right ml-1"
                variant="warning"
                @click="loadArticles"
              >
                <FontAwesomeIcon :icon="['fas', 'redo']" /> Änderungen verwerfen
              </b-button>
              <SaveButton
                squared
                size="sm"
                class="float-right ml-1"
                text="Alle Speichern/Erstellen"
                @click="saveAllArticles"
              />
              <b-button
                squared
                size="sm"
                class="float-right ml-1"
                variant="primary"
                @click="addArticle"
              >
                <FontAwesomeIcon :icon="['fas', 'plus-square']" /> Artikel hinzufügen
              </b-button>
            </b-th>
          </b-tr>
        </template>
        <!-- Selected Field -->
        <template #cell(selected)="{ rowSelected }">
          <template v-if="rowSelected">
            <FontAwesomeIcon
              :icon="['far', 'check-square']"
              size="lg"
            />
            <span class="sr-only">Selected</span>
          </template>
          <template v-else>
            <FontAwesomeIcon
              :icon="['far', 'square']"
              size="lg"
            />
            <span class="sr-only">Not selected</span>
          </template>
        </template>
      </b-table>
    </b-row>
  </b-container>
</template>
<script>
import { API } from 'aws-amplify';
import DeleteButton from './buttons/DeleteButton.vue';
import CreateButton from './buttons/CreateButton.vue';
import SaveButton from './buttons/SaveButton.vue';
import ColorPicker from './ColorPicker.vue';
import httpStatus from 'http-status';
import vatCalc from '../js/vat-calc.js';
// import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
const uuid = require('uuid/v4');
const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export default {
	components: { DeleteButton, CreateButton, SaveButton, ColorPicker },
	props: ['ag', 'articleGroups', 'agBus', 'depositGroups'],
	data() { return {
		articles: [],
		loading: true,
		selectMode: 'multi',
		selected: [],
		editMode: true
	};},
	computed:{
		availableMoveArticleGroups() {
			if (!this.articleGroups) return [];
			return this.articleGroups.filter(ag => this.ag.id != ag.id);
		},
		isGlobalAG() {
			if (this.ag.EventId) return false;
			return true;
		},
		isEventAG() {
			if (this.ag.EventId) return true;
			return false;
		},
		fields() {
			let f = [
				{
					key: 'selected',
					label: ''
				}, {
					key: 'name',
					sortable: true
				}, {
					key: 'description',
					sortable: false,
					label: 'Beschreibung'
				}, {
					key: 'costNet',
					sortable: false,
					label: 'Preis netto'
				}, {
					key: 'vat',
					sortable: true,
					label: 'Mwst'
				}, {
					key: 'costGross',
					sortable: true,
					label: 'Preis brutto'
				}, {
					key: 'active',
					sortable: true,
					label: 'Aktiv'
				}, {
					key: 'color',
					sortable: false,
					label: 'Farbe'
				}, {
					key: 'sequence',
					sortable: true,
					label: 'Order'
				}, {
					key: 'buttons',
					label: '',
					sortable: false
				}];
			if (this.isEventAG) {
				f.splice(6, 0, {
					key: 'DepositGroupId',
					sortable: true,
					label: 'Pfand'
				});
			}

			return f;
		}
	},
	created() {
		this.loadArticles();
	},
	mounted() {
		this.agBus.$on('moveArticle', this.moveArticle);
	},
	methods: {
		loadArticles() {
			this.loading = true;
			API.get('innorder', '/Article?filterArticleGroup=' + this.ag.id).then(response => {
				this.articles = response;
				this.loading = false;
			}).catch(error => {
				this.$bvToast.toast('Beim Laden der Artikel der Artikelgruppe"' + this.ag.name + '" ist ein Fehler aufgetreten. ErrorCode: ' + JSON.stringify(error), {
					title: 'Laden Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			});
		},
		deleteArticle(btn) {
			this.delArticleByItem(btn.data.item, btn);
		},
		saveArticle(btn) {
			let item = btn.data.item;
			API.put('innorder', '/Article/' + item.id, { body: item }).then(response  => {
				this.$bvToast.toast('Der Artikel "' + item.name + '" wurde erfolgreich geändert.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				this.$delete(this.articles[btn.data.index], 'isChanged');
				btn.reset();
			}).catch(error => {
				this.$bvToast.toast('Beim Ändern von "' + item.name + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Ändern Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		deleteSelected(btn) {
			this.selected.forEach(item => {
				this.delArticleByItem(item, btn);
			});
		},
		dataChange(data) {
			if (!data.item.inCreation) {
				this.$set(this.articles[data.index], 'isChanged', true);
			}
		},
		delArticleByItem(item, btn) {
			if (item.inCreation) {
				// Remove from Local Array
				for (let i = 0; i < this.articles.length; i++) {
					if (this.articles[i].id == item.id) {
						this.$delete(this.articles, i);
					}
				}
				btn.reset();
				return;
			}
			API.del('innorder', '/Article/' + item.id).then(response  => {
				this.$bvToast.toast('Der Artikel "' + item.name + '" wurde erfolgreich gelöscht.', {
					title: 'Löschen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				// Remove from Local Array
				for (let i = 0; i < this.articles.length; i++) {
					if (this.articles[i].id == item.id) {
						this.$delete(this.articles, i);
					}
				}
				btn.reset();
			}).catch(error => {
				this.$bvToast.toast('Beim Löschen von "' + item.name + '" ist ein Fehler aufgetreten. StatusCode: ' + httpStatus[error.response.status], {
					title: 'Löschen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		calcNet(costGross, vat) {
			let costNet = vatCalc.calcNet(costGross, vat);
			if (!null) {
				return costNet + ' €';
			} else {
				return '';
			}
		},
		onRowSelected(items) {
			this.selected = items;
		},
		selectAllRows() {
			this.$refs.selectableTable.selectAllRows();
		},
		clearSelected() {
			this.$refs.selectableTable.clearSelected();
		},
		copyArticle(data) {
			let newItem = Object.assign({}, data.item);
			newItem.id = uuid();
			delete(newItem.isChanged);
			newItem.inCreation = true;
			this.articles.push(newItem);
		},
		addArticle() {
			this.articles.push({
				'id':uuid(),
				'name':'Neuer Artikel',
				'active':true,
				'costGross':'0.00',
				'vat':'19',
				'ArticleGroupId':this.ag.id,
				'DepositGroupId': null,
				// "DepositGroup":{"id":"6097df8f-d4d1-11e9-9e8b-06ba1e206a58",
				'eventId':this.ag.eventId,
				inCreation: true
			});
		},
		createArticle(btn) {
			let item = btn.data.item;
			API.post('innorder', '/Article/', { body: item }).then(response  => {
				this.$bvToast.toast('Der Artikel "' + item.name + '" wurde erfolgreich erstellt.', {
					title: 'Erstellen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				this.$delete(this.articles[btn.data.index], 'inCreation');
				btn.reset();
			}).catch(error => {
				this.$bvToast.toast('Beim Erstellen von "' + item.name + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Erstellen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		moveSelected(toArticleGroup) {
			this.selected.forEach(item => {
				this.agBus.$emit('moveArticle', item, toArticleGroup);
			});
		},
		moveArticle(item, toArticleGroup) {
			let moveItem = Object.assign({}, item);
			//Add in new AG
			if (toArticleGroup.id == this.ag.id) {
				moveItem.isChanged = true;
				moveItem.ArticleGroupId = this.ag.id;
				this.articles.push(moveItem);
			}
			// Remove from Local Array
			if (item.ArticleGroupId == this.ag.id) {
				for (let i = 0; i < this.articles.length; i++) {
					if (this.articles[i].id == item.id) {
						this.$delete(this.articles, i);
					}
				}
			}
		},
		async deleteArticleGroup(btn) {
			// Alle Artikel löschen
			this.articles.forEach(item => {
				this.delArticleByItem(item, btn);
			});
			// Warten bis alle gelöscht sind, Timeout nach 15 Sekunden
			let i = 0;
			while (this.articles.length > 0 && i <= 30) {
				await sleep(500);
				i++;
			}
			if (i < 30) {
				let item = this.ag;
				API.del('innorder', '/ArticleGroup/' + item.id).then(response  => {
					this.$emit('delArticleGroup', this.ag);
				}).catch(error => {
					this.$bvToast.toast('Beim Löschen von "' + item.name + '" ist ein Fehler aufgetreten.', {
						title: 'Löschen Fehlgeschlagen',
						autoHideDelay: 10000,
						variant: 'danger'
					});
					btn.reset();
				});
			} else {
				this.$bvToast.toast('Beim Löschen der Artikelgruppe "' + this.ag.name + '" ist ein Fehler aufgetreten.', {
					title: 'Löschen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
			}
		},
		saveArticleGroup(btn, hide) {
			let item = this.ag;
			API.put('innorder', '/ArticleGroup/' + item.id, { body: item }).then(response  => {
				this.$bvToast.toast('Die Artikelgruppe "' + item.name + '" wurde erfolgreich geändert.', {
					title: 'Ändern Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
				hide();
			}).catch(error => {
				this.$bvToast.toast('Beim Ändern von "' + item.name + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Ändern Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		saveAllArticles(btn) {
			let countRequests = 0;
			this.articles.forEach((article, index) => {
				let vbtn = {
					data: {
						item: article,
						index: index
					},
					reset() {
						countRequests--;
						if (countRequests == 0) {
							btn.reset();
						}
					}
				};
				if (article.inCreation) {
					countRequests++;
					this.createArticle(vbtn);
				} else if (article.isChanged) {
					countRequests++;
					this.saveArticle(vbtn);
				}
			});
		}
	}
};
</script>
<style scoped>

</style>
