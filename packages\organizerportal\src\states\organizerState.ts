import type { Organizer, Optional } from '@innevent/types';
import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { organizerState as log } from '../loglevel';

log.debug('INIT organizerState');

const isLoadingRef = ref<boolean>(false);
const permittedOrganizersRef = ref<Organizer[]>([]);
const currentOrganizerRef = ref<Organizer | undefined>(undefined);

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useOrganizerState() {
	log.debug('useOrganizerState()');

	async function createOrganizer(newOrganizer: Optional<Organizer, 'organizerId'>): Promise<Organizer> {
		log.debug('createOrganizer()', newOrganizer);
		isLoadingRef.value = true;
		try {
			const createdOrganizer = await api.createOrganizer({
				data: newOrganizer
			});
			isLoadingRef.value = false;
			permittedOrganizersRef.value.push(createdOrganizer);
			return createdOrganizer;
		} catch (error) {
			isLoadingRef.value = false;
			throw error;
		}
	}

	async function reloadPermittedOrganizers() {
		log.debug('reloadPermittedOrganizers()');
		isLoadingRef.value = true;
		try {
			permittedOrganizersRef.value = await api.getPermittetOrganizers();
			isLoadingRef.value = false;
		} catch (error) {
			isLoadingRef.value = false;
			throw error;
		}
	}


	return {
		createOrganizer,
		isLoadingRef,
		reloadPermittedOrganizers,
		permittedOrganizersRef,
		currentOrganizerRef
	};
}