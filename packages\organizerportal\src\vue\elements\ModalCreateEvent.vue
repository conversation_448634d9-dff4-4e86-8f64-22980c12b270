<template>
  <b-modal
    id="modalCreateEvent"
    ref="modalCreateEventRef"
    title="Event erstellen"
    size="lg"
  >
    <b-tabs
      v-model="activeTabRef"
      content-class="mt-3"
      justified
    >
      <b-tab
        title="Allgemein"
        active
      >
        <b-form>
          <b-form-group label="Event Name">
            <b-form-input
              v-model="newEventRef.name"
              placeholder="Gib einen klaren, aussagekräftigen Namen ein"
              :state="isValidEventName"
            />
          </b-form-group>
          <b-form-group label="Event Beschreibung">
            <b-form-input
              v-model="newEventRef.description"
              placeholder="Beschreibe das Event kurz in einem Satz"
            />
          </b-form-group>
          <div class="form-row mb-3">
            <div class="col-6">
              <label for="inputTimeFrom">Event-Beginn</label>
              <DateTimePicker
                id="inputTimeFrom"
                v-model="newEventRef.timeFrom"
                :error="!isValidTimeFrom"
                label="Wähle einen Startzeitpunkt"
                :no-clear-button="true"
                :no-label="true"
                format="YYYY-MM-DD HH:mm:ss"
                :no-button-now="true"
                :overlay="true"
                :min-date="new Date().toISOString()"
              />
            </div>
            <div class="col-6">
              <label for="inputTimeTo">Event-Ende</label>
              <DateTimePicker
                id="inputTimeTo"
                v-model="newEventRef.timeTo"
                :error="!isValidTimeTo"
                label="Wähle einen Endzeitpunkt"
                :no-clear-button="true"
                :no-label="true"
                format="YYYY-MM-DD HH:mm:ss"
                :no-button-now="true"
                :overlay="true"
                :min-date="newEventRef.timeFrom ? newEventRef.timeFrom : new Date().toISOString()"
              />
            </div>
          </div>
          <b-form-group label="Veranstalter">
            <b-input-group>
              <b-form-select
                v-model="newEventRef.organizerId"
                :state="isValidOrganizer"
                :options="permittedOrganizersRef"
                text-field="organizationName"
                value-field="organizerId"
              />
              <b-input-group-append>
                <b-button
                  v-b-modal.modalCreateOrganizer
                  variant="outline-primary"
                >
                  Veranstalter erstellen
                </b-button>
              </b-input-group-append>
            </b-input-group>
          </b-form-group>
        </b-form>
      </b-tab>
    </b-tabs>
    <template #modal-footer="{ cancel }">
      <b-button
        class="mr-auto"
        @click="cancel()"
      >
        Abbrechen
      </b-button>
      <LoadButton
        variant="primary"
        :disabled="!isGeneralValid"
        :loading="isLoadingRef"
        text="Event erstellen"
        @click="btnClickCreate"
      />
    </template>
  </b-modal>
</template>
<script lang="ts">

// Frameworks
import moment from 'moment';

// Vue Components
import LoadButton from '@innevent/webapp-components/button/LoadButtonV2.vue';
import { computed, defineComponent, getCurrentInstance, ref } from '@vue/composition-api';
import type { Event, PartialPick } from '@innevent/types';
import { useEventState } from '../../states/eventState';
import { useOrganizerState } from '../../states/organizerState';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BModal } from 'bootstrap-vue';

type NewEvent = Pick<Event, 'name' | 'organizerId' | 'timeFrom' | 'timeTo' | 'description'> & PartialPick<Event, | 'settings' | 'eventId'>

export default defineComponent({
	components: { LoadButton },
	setup(props, { root }) {
		const { createEvent, isLoadingRef } = useEventState();
		const { permittedOrganizersRef } = useOrganizerState();
		const instance = getCurrentInstance();
		const modalCreateEventRef = ref<InstanceType<typeof BModal>>();

		const activeTabRef = ref<number>(0);
		const newEventRef = ref<NewEvent>({
			name: '',
			timeFrom: new Date(),
			timeTo: new Date(),
			organizerId: '',
			description: ''
		});

		async function btnClickCreate() {
			if (!isGeneralValid.value) return;
			try {
				const createdEvent = await createEvent(newEventRef.value);
				notifySuccess({ instance });
				modalCreateEventRef.value?.hide();
				root.$router.push({ name: 'eBasedata', params: { event: createdEvent.eventId as string } });
			} catch (error) {
				notifyError({ instance, error });
			}
		}

		const isValidEventName = computed(() => {
			if (!newEventRef.value.name) return null;
			if (newEventRef.value.name.length < 5) return false;
			if (newEventRef.value.name.length > 60) return false;
			return true;
		});

		const isValidTimeFrom = computed(() => {
			if (!newEventRef.value.timeFrom) return undefined;
			if (!moment(newEventRef.value.timeFrom).isValid()) return false;
			return true;
		});

		const isValidTimeTo = computed(() => {
			if (!newEventRef.value.timeTo) return undefined;
			if (!moment(newEventRef.value.timeTo).isValid()) return false;
			if (!moment(newEventRef.value.timeTo).isAfter(moment(newEventRef.value.timeFrom))) return false;
			return true;
		});

		const isValidOrganizer = computed(() => {
			if (!newEventRef.value.organizerId) return null;
			return newEventRef.value.organizerId ? true : false;
		});

		const isGeneralValid = computed(() => {
			return isValidEventName.value  && isValidTimeFrom.value && isValidTimeTo.value && isValidOrganizer.value;
		});

		return {
			isLoadingRef,
			activeTabRef,
			newEventRef,
			modalCreateEventRef,
			permittedOrganizersRef,
			isValidEventName,
			isValidTimeFrom,
			isValidTimeTo,
			isValidOrganizer,
			isGeneralValid,
			btnClickCreate
		};
	}
});

</script>
