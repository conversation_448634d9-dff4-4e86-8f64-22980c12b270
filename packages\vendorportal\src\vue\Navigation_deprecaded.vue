<template>
  <div>
    <div
      v-if="currentNav == 'settings'"
      id="navMainContainer"
      class="col-12 col-md-3 col-xl-2"
    >
      <b-nav
        id="navSettings"
        pills
        vertical
      >
        <b-nav-item
          :active="mainContent == 'settingsInfo'"
          @click="$emit('navigateSettings', 'settingsInfo')"
        >
          Kontoinformationen
        </b-nav-item>
        <b-nav-item
          :active="mainContent == 'settingsChangeMail'"
          @click="$emit('navigateSettings', 'settingsChangeMail')"
        >
          Email-Adresse ändern
        </b-nav-item>
        <b-nav-item
          :active="mainContent == 'settingsPassword'"
          @click="$emit('navigateSettings', 'settingsPassword')"
        >
          Kennwort ändern
        </b-nav-item>
      </b-nav>
    </div>
    <div
      v-if="currentNav == 'main'"
      id="navMainContainer"
      class="col-12 col-md-3 col-xl-2"
    >
      <b-nav
        id="navMain"
        pills
        vertical
      >
        <b-nav-item
          :active="mainContent == 'mainBaseData'"
          @click="$emit('navigate', 'mainBaseData')"
        >
          Stammdaten
        </b-nav-item>
        <b-nav-item
          :active="mainContent == 'mainArticles'"
          @click="$emit('navigate', 'mainArticles')"
        >
          Artikelverwaltung
        </b-nav-item>
        <b-nav-item
          :active="mainContent == 'mainStaff'"
          @click="$emit('navigate', 'mainStaff')"
        >
          Mitarbeiter
        </b-nav-item>
        <b-nav-item
          :active="mainContent == 'mainEvents'"
          @click="$emit('navigate', 'mainEvents')"
        >
          Eventübersicht
        </b-nav-item>
        <b-nav-item
          v-for="ev in vendorEventProps"
          :key="ev.Event.id"
          class="ml-3"
          @click="$emit('navigate', 'subArticles', ev.Event)"
        >
          {{ ev.Event.name }}
        </b-nav-item>
      </b-nav>
    </div>
    <div
      v-if="currentNav == 'sub'"
      id="navSubContainer"
      class="col-12 col-md-3 col-xl-2"
    >
      <b-button
        variant="outline-secondary"
        block
        @click="$emit('navigate', 'mainBaseData')"
      >
        <FontAwesomeIcon
          :icon="['fas', 'arrow-left']"
          size="lg"
        /> Zurück
      </b-button>
      <h4 class="mt-3">
        {{ selectedEvent.name }}
      </h4>
      <nav
        id="navSub"
        class="nav nav-pills flex-column"
      >
        <a
          href="#"
          :class="{ active: mainContent == 'subArticles' }"
          class="nav-link"
          @click="$emit('navigate', 'subArticles')"
        >Artikel</a>
        <a
          href="#"
          :class="{ active: mainContent == 'subSalesAreas' }"
          class="nav-link"
          @click="$emit('navigate', 'subSalesAreas')"
        >Verkaufszonen</a>
        <a
          href="#"
          :class="{ active: mainContent == 'subOrders' }"
          class="nav-link"
          @click="$emit('navigate', 'subOrders')"
        >Bestellungen</a>
        <a
          href="#"
          :class="{ active: mainContent == 'subAnalysis' }"
          class="nav-link"
          @click="$emit('navigate', 'subAnalysis')"
        >Auswertungen</a>
      </nav>
    </div>
  </div>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default {
	components: { FontAwesomeIcon },
	props: ['currentNav', 'mainContent', 'currentPortalObjectId', 'selectedEvent', 'vendorEventProps']
};

</script>
<style>
#navMainContainer, #navSubContainer{
  position: fixed;
  top: 66px;
  bottom: 0;
  left: 0;
  z-index: 1000;
  padding: 20px;
  overflow-x: hidden;
  overflow-y: auto;
  border-right: 1px solid #eee;
}
</style>
