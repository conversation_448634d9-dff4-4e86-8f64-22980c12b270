<template>
  <form
    id="loginForm"
    class="form-signin"
  >
    <div class="text-center mb-4">
      <img
        v-if="!hideLogo"
        class="mb-4"
        src="../../img/logo-transparent.png"
        alt=""
        width="200"
        height="200"
      >
      <h1 class="h3 mb-3 font-weight-normal">
        {{ title }}
      </h1>
    </div>
    <div class="form-label-group">
      <input
        id="inputEmail"
        v-model="email"
        type="email"
        class="form-control"
        :class="classEmail"
        :placeholder="fieldNameEmail"
        required=""
        autofocus=""
      >
      <label for="inputEmail">{{ fieldNameEmail }}</label>
    </div>
    <div class="form-label-group">
      <input
        id="inputPassword"
        v-model="password"
        type="password"
        class="form-control"
        :placeholder="fieldNamePassword"
        required=""
      >
      <label for="inputPassword">{{ fieldNamePassword }}</label>
    </div>
    <LoadButton
      block
      size="lg"
      :text="btnTextSignIn"
      icon="sign-in-alt"
      @click="checkForm"
    />
    <div
      v-if="signInError"
      class="alert alert-danger mt-3"
      role="alert"
    >
      {{ signInError }}
    </div>
    <span class="mt-5 mb-3 text-muted">Passwort vergessen?</span>
    <p class="mt-5 mb-3 text-muted text-center">
      {{ copyrightText }}
    </p>
  </form>
</template>
<script>
import { Auth } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';

function isEmailValid(email) {
	const mailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
	if (!mailRegex.test(email)) {
		return false;
	}
	return true;
}

async function amplifySignIn(username, password, v, btn) {
	try {
		const user = await Auth.signIn(username, password);
		if (user.challengeName === 'SMS_MFA' || user.challengeName === 'SOFTWARE_TOKEN_MFA') {
			// You need to get the code from the UI inputs
			// and then trigger the following function with a button click
			const code = getCodeFromUserInput();
			// If MFA is enabled, sign-in should be confirmed with the confirmation code
			const loggedUser = await Auth.confirmSignIn(
				user,   // Return object from Auth.signIn()
				code,   // Confirmation code
				mfaType // MFA Type e.g. SMS_MFA, SOFTWARE_TOKEN_MFA
			);
		} else if (user.challengeName === 'NEW_PASSWORD_REQUIRED') {
			const { requiredAttributes } = user.challengeParam; // the array of required attributes, e.g ['email', 'phone_number']
			// You need to get the new password and required attributes from the UI inputs
			// and then trigger the following function with a button click
			// For example, the email and phone_number are required attributes
			const { username, email, phone_number } = getInfoFromUserInput();
			const loggedUser = await Auth.completeNewPassword(
				user,              // the Cognito User Object
				newPassword,       // the new password
				// OPTIONAL, the required attributes
				{
					email,
					phone_number
				}
			);
		} else if (user.challengeName === 'MFA_SETUP') {
			// This happens when the MFA method is TOTP
			// The user needs to setup the TOTP before using it
			// More info please check the Enabling MFA part
			Auth.setupTOTP(user);
		} else {
			// The user directly signs in
			if (v.reloadOnSuccess) {
				location.reload();
			} else {
				v.$emit('successfulLogin');
			}
		}
		btn.reset();
	} catch (err) {
		self.signInError = err.message;
		console.log(err.code);
		if (err.code === 'UserNotConfirmedException') {
			// The error happens if the user didn't finish the confirmation step when signing up
			// In this case you need to resend the code and confirm the user
			// About how to resend the code and confirm the user, please check the signUp part
		} else if (err.code === 'PasswordResetRequiredException') {
			// The error happens when the password is reset in the Cognito console
			// In this case you need to call forgotPassword to reset the password
			// Please check the Forgot Password part.
		} else if (err.code === 'NotAuthorizedException') {
			// The error happens when the incorrect password is provided
		} else if (err.code === 'UserNotFoundException') {
			// The error happens when the supplied username/email does not exist in the Cognito user pool
		} else {

		}
		btn.reset();
	}
}

export default {
	components: { LoadButton },
	props: {
		hideLogo: {
			type: Boolean,
			default: false
		},
		reloadOnSuccess: {
			type: Boolean,
			default: false
		}
	},
	data() { return {
		title: 'INN//SYSTEMS Login',
		fieldNameEmail: 'Email Adresse',
		fieldNamePassword: 'Passwort',
		btnTextSignIn: 'Anmelden',
		email: '',
		password: '',
		signInError: null
	};},
	computed: {
		copyrightText: function() {
			return 'Copyright © ' + new Date().getFullYear() + ' Inn-Event UG (Haftungsbeschränkt)';
		},
		classEmail: function() {
			if (!isEmailValid(this.email) && this.email.length > 0) {
				return 'is-invalid';
			}
		}
	},
	methods:{
		checkForm: function(btn) {
			let self = this;
			if (!this.password) {
				this.signInError = 'Please insert a password';
				btn.reset();
			}
			if (isEmailValid(this.email) && this.password) {
				amplifySignIn(this.email, this.password, this, btn);
			}
		}
	}
};
</script>
<style scoped>
html,
body {
  height: 100%;
}

body {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding-top: 40px;
  padding-bottom: 40px;
  background-color: #f5f5f5;
}

.form-signin {
  width: 100%;
  max-width: 420px;
  padding: 15px;
  margin: auto;
}
</style>
