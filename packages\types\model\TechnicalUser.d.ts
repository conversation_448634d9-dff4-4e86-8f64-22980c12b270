import type { EventEmployee } from './innevent';

export type InnUser = {
    firstName: string;
    lastName: string;
    email: string;
    userSubject?: string;
}

export type TechnicalUserReferenceObject = 'event' | 'vendor'

export type TechnicalInnUser = InnUser & {
    technicalPassword: string;
    technicalRefObject: TechnicalUserReferenceObject;
    technicalRefId: string;
    eventEmployees?: EventEmployee[];
    vendorEmployees?: any[]; //TODO
}

export type CognitoUserAttributes = {
    technicalPassword?: string;
    firstName?: string;
    lastName?: string;
    emailVerified?: boolean;
    email?: string;
}
