<template>
  <div>
    <h3><PERSON>wei-Faktor-Authentifizierung</h3>
    <p>Aktueller Status: {{ printMfaStatus }}</p>
    <b-button v-if="mfaStatus == 'SOFTWARE_TOKEN_MFA'" size="sm" @click="disableMFA()">Zweiten Faktor deaktivieren</b-button>
    <b-button v-if="mfaStatus == 'NOMFA'" size="sm" @click="setupMFA()">Zweiten Faktor einrichten</b-button>
    <div v-if="setupCode">
      <p>Bitte laden dir die "Google Authentication App herunter und scanne folgenden Code:</p>
      <QRCode :value="qrcode" size="300" level="H"></QRCode>
      <p>Nach erfolgreichen hinzufügen zeigt dir die Authenticator App einen Token an.<br />
      Bitte gib diesen in folgendes Feld ein, um die Einrichtugn abzuschließen.</p>
      <b-input-group prepend="Token" class="mt-3">
        <b-form-input ref="tokenInput" v-model="totpToken" placeholder="123456" @keyup.enter="verifySetup()"></b-form-input>
        <b-input-group-append>
          <b-button variant="primary" @click="verifySetup()">Bestätigen</b-button>
        </b-input-group-append>
      </b-input-group>
    </div>
    <b-alert class="mt-3" v-if="feedback.msg" :variant="feedback.variant" show>{{ feedback.msg }}</b-alert>
  </div>
</template>
<script>

import Amplify, { Auth, API } from 'aws-amplify'
import QRCode from 'qrcode.vue'

export default {
  components: { QRCode },
  props: ['cognitoUser'],
  data() { return{
    mfaStatus: null,
    setupCode: null,
    issuer: 'Inn//Systems',
    totpToken: null,
    feedback: {
      variant: 'success',
      msg: null
    }
  }},
  computed: {
    qrcode(){
      return "otpauth://totp/" + this.cognitoUser.attributes.email + "?secret=" + this.setupCode + "&issuer=" + this.issuer
    },
    printMfaStatus(){
      if (this.mfaStatus == 'NOMFA') return 'Kein zweiter Faktor'
      else if (this.mfaStatus == 'SOFTWARE_TOKEN_MFA') return 'Einmal-Passwort per App'
      else return this.mfaStatus
    }
  },
  async created(){
    this.loadStatus()
  },
  methods: {
    loadStatus(){
      Auth.getPreferredMFA(this.cognitoUser).then(data => {
        this.mfaStatus = data
      }).catch(err => {
        console.log(err)
      })
    },
    disableMFA(){
      Auth.setPreferredMFA(this.cognitoUser, 'NOMFA')
      this.setFeedback('Die Deaktivierung war erfolgreich')
      this.mfaStatus = 'NOMFA'
    },
    setupMFA(){
      Auth.setupTOTP(this.cognitoUser).then(code => {
        this.setupCode = code
        this.$nextTick(() => this.$refs.tokenInput.focus())
      })
    },
    setFeedback(msg, variant='success'){
      this.feedback.variant = variant
      this.feedback.msg = msg
    },
    verifySetup(){
      Auth.verifyTotpToken(this.cognitoUser, this.totpToken).then(() => {
        Auth.setPreferredMFA(this.cognitoUser, 'TOTP')
        this.setFeedback('Die Einrichtung war erfolgreich')
        this.mfaStatus = 'SOFTWARE_TOKEN_MFA'
        this.setupCode = null
      }).catch( err => {
        this.setFeedback('Leider ist ein Fehler aufgetreten', 'danger')
        console.log(err)
      })
    }
  }
}
</script>
<style scoped>

</style>
