import type { TicketMap } from '@innevent/webapp-types';
import { ref } from '@vue/composition-api';
import * as api from '@innevent/webapp-api';
import { currentEventRef } from './eventState';

const isLoadingRef = ref<boolean>(false);
const ticketMapRef = ref<TicketMap>({ salesPeriods: [], ticketGroups: [], tickets: [] });

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useTicketMapState() {
	return {
		ticketMapRef,
		isLoadingRef
	};
}


export async function refreshTicketMap(): Promise<void> {
	if (!currentEventRef.value) {
		return;
	}
	isLoadingRef.value = true;
	ticketMapRef.value = await api.getTicketMap({ 
		key: {
			eventId: currentEventRef.value.eventId!
		}
	});
	isLoadingRef.value = false;
}