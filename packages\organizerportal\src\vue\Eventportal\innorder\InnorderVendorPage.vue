<template>
  <div>
    <b-row
      align-h="between"
    >
      <b-col>
        <h2>Übersicht Verkäufer</h2>
      </b-col>
      <b-col class="text-right">
        <b-button-group>
          <b-button
            v-b-modal.modalCreateVendor
            class="ml-auto"
            variant="primary"
            size="sm"
          >
            <FontAwesomeIcon icon="plus-square" /> <PERSON><PERSON><PERSON><PERSON>
          </b-button>
          <b-button
            v-b-modal.modalLinkVendor
            class="ml-auto"
            variant="secondary"
            size="sm"
          >
            <FontAwesomeIcon icon="envelope" /> Verlinken
          </b-button>
          
          <b-button
            v-b-modal.inviteModal
            class="ml-auto"
            variant="info"
            size="sm"
          >
            <FontAwesomeIcon icon="envelope" /> Einladen
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <b-table
      hover
      :busy="isLoadingVendorEventPropsRef"
      :items="vendorEventPropertyRef"
      :fields="tableFieldsVendors"
      class="mt-3 table-clickable"
      show-empty
      empty-text="Es sind keine Verkäufer vorhanden."
      head-variant="light"
      @row-clicked="onRowClick"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(name)="data">
        {{ data.item.Vendor.name }}
      </template>
      <template #cell(address)="data">
        {{ data.item.Vendor.street + " " + data.item.Vendor.housenumber + ", "
          + data.item.Vendor.postalcode + " " + data.item.Vendor.city }}
      </template>
      <template #cell(buttons)="{item}">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            class=""
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteVendorEventProperty($event, item)"
          />
        </div>
      </template>
    </b-table>
    <div class="d-flex">
      <h2>Einladungen: </h2>
    </div>
    <label class="typo__label">Filter nach Status</label>
    <Multiselect
      v-model="filterSelection"
      :options="filterOptions"
      :multiple="true"
      :close-on-select="false"
      :clear-on-select="false"
      :preserve-search="true"
      placeholder="Auswählen"
      label="name"
      track-by="name"
      :allow-empty="false"
      :preselect-first="true"
    />
    <b-table
      hover
      :busy="isLoadingVendorInvitationsRef"
      :items="filteredVendorInvitations"
      :fields="tableFieldsInvitations"
      class="mt-3"
      show-empty
      empty-text="Es sind keine Einladungen vorhanden."
      head-variant="light"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(createdAt)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(status)="data">
        <b-badge
          v-if="data.value == 'ACCEPTED'"
          variant="success"
        >
          Akzeptiert
        </b-badge>
        <b-badge
          v-else-if="data.value == 'CREATED'"
          variant="secondary"
        >
          Versendet
        </b-badge>
        <b-badge
          v-else-if="data.value == 'DECLINED'"
          variant="danger"
        >
          Abgelehnt
        </b-badge>
      </template>
      <template #cell(buttons)="{item}">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            class=""
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteVendorInvitation($event, item)"
          />
        </div>
      </template>
    </b-table>
    <b-modal
      id="inviteModal"
      title="Vendor einladen"
      centered
    >
      <div role="group">
        <label for="inviteEmail">E-Mail Adresse:</label>
        <b-form-input
          id="inviteEmail"
          v-model="vendorInvitation.mail"
          type="email"
          aria-describedby="inviteEmail-help inviteEmail-feedback"
          placeholder="<EMAIL>"
          @keyup.enter="onKeyupEnter()"
        />
        <b-form-invalid-feedback id="inviteEmail-feedback">
          Die Email Adresse ist ungültig
        </b-form-invalid-feedback>
        <b-form-text id="inviteEmail-help">
          Bitte die Emailadresse der einzuladenden Person eingeben
        </b-form-text>
      </div>
      <div
        role="group"
        class="mt-3"
      >
        <label for="inviteText">Nachricht (optional):</label>
        <b-form-textarea
          id="inviteText"
          v-model="vendorInvitation.message"
          placeholder="Gib eine kurze Nachricht ein..."
          rows="3"
          max-rows="6"
          @keyup.enter="onKeyupEnter()"
        />
      </div>
      <template #modal-footer="{ hide }">
        <div class="d-flex">
          <LoadButton
            ref="saveButtonInvitationRef"
            class="ml-auto"
            text="Einladen"
            icon="plus-square"
            @click="btnCreateVendorInvitation($event, hide)"
          />
        </div>
      </template>
    </b-modal>
    <ModalCreateVendor
      linkvendorevent="true"
      :eventid="currentEventRef.eventId"
      @organizationCreated="reloadVendorEventProperties"
    />
    <ModalLinkVendor
      :linked-vendors="vendorEventPropertyRef"
      :eventid="currentEventRef.eventId"
      @vendorLinked="reloadVendorEventProperties"
    />
  </div>
</template>
<script lang="ts">
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import LoadButton from '../../buttons/LoadButton.vue';
import DeleteButton from '../../buttons/DeleteButton.vue';
import Multiselect from 'vue-multiselect';
import ModalCreateVendor from '@innevent/webapp-components/modal/ModalCreateVendor.vue';
import ModalLinkVendor from './elements/ModalLinkVendor.vue';
import { currentEventRef } from '../../../states/eventState';
import { computed, defineComponent, getCurrentInstance, onMounted, ref } from '@vue/composition-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import { useVendorEventPropertyState } from '../../../states/vendorEventPropertyState';
import { useVendorInvitationState } from '../../../states/vendorInvitationState';

import { formatDate } from '@innevent/webapp-utils';
import type { ResetButton, VendorEventProperty, VendorInvitation } from '@innevent/webapp-types';

export default defineComponent({
	components: { 
		FontAwesomeIcon, 
		LoadButton, 
		DeleteButton, 
		Multiselect, 
		ModalCreateVendor, 
		ModalLinkVendor 
	},
	setup() {
		const instance = getCurrentInstance();
		const { isLoadingInitialRef: isLoadingVendorInvitationsRef, vendorInvitationsRef, 
			loadVendorInvitations, createVendorInvitation, deleteVendorInvitation } = useVendorInvitationState();
		const { isLoadingInitialRef: isLoadingVendorEventPropsRef, vendorEventPropertyRef, 
			loadVendorEventProperties, deleteVendorEventProperty } = useVendorEventPropertyState();
		const saveButtonInvitationRef = ref<InstanceType<typeof LoadButton>>();
		const vendorInvitation: Partial<VendorInvitation> = {
			mail: '',
			status: 'CREATED',
			message: '',
			EventId: currentEventRef.value?.eventId
		};
		const tableFieldsVendors = [
			{ key: 'name', label: 'Firmenname', sortable: true },
			{ key: 'address', label: 'Adresse' },
			{ key: 'buttons', label: '' }
		];
		const tableFieldsInvitations = [
			{ key: 'mail', label: 'E-Mail', sortable: true },
			{ key: 'status', label: 'Status', sortable: true },
			{ key: 'message', label: 'Nachricht' },
			{ key: 'createdAt', label: 'Erstellt am', sortable: true },
			{ key: 'Vendor', label: 'Verkäufer', formatter: vendor => vendor ? vendor.name : '' },
			{ key: 'buttons', label: '' }
		];
		const filterOptions = [
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		];
		const filterSelection = [
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		];

		onMounted(async ()  => {
			try {
				await loadVendorInvitations(currentEventRef.value!.eventId!);
				await reloadVendorEventProperties();
			} catch (error: any) {
				notifyError({ instance, error });
			}			
		});

		const filteredVendorInvitations = computed(() => {
			return vendorInvitationsRef.value.filter(invitation => filterSelection.some(status => status.key == invitation.status));
		});

		function onKeyupEnter() {
			saveButtonInvitationRef.value?.clickButton();
		}

		async function reloadVendorEventProperties() {
			try {
				await loadVendorEventProperties(currentEventRef.value!.eventId!);
			} catch (error) {
				notifyError({ instance, error });
			}
		}

		async function btnDeleteVendorEventProperty(btn: ResetButton, vendorEventProperty: VendorEventProperty) {
			try {
				await deleteVendorEventProperty({
					key: {
						id: vendorEventProperty.id
					}
				});
				notifySuccess({ instance, message: 'Erfolgreich entfernt!' });
			} catch (error) {
				notifyError({ instance, error });
			}
			btn.reset();

		}
		async function btnCreateVendorInvitation(btn: ResetButton, hideModal) {
			try {
				await createVendorInvitation({
					data: vendorInvitation as VendorInvitation
				});
				hideModal();
				notifySuccess({ instance, message: 'Einladung versendet!' });
			} catch (error) {
				notifyError({ instance, error });
			}
			btn.reset();
		}
		async function btnDeleteVendorInvitation(btn: ResetButton, vendorInvitation: VendorInvitation) {
			try {
				await deleteVendorInvitation({
					key: {
						id: vendorInvitation.id
					}
				});
				notifySuccess({ instance, message: 'Einladung entfernt!' });
			} catch (error) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		function onRowClick(item: VendorEventProperty) {
			window.open(process.env.URL_VENDORPORTAL + `/vendor/${item.VendorId}`)?.focus();
		}


		return {
			currentEventRef,
			onKeyupEnter,
			filteredVendorInvitations,
			isLoadingVendorInvitationsRef,
			isLoadingVendorEventPropsRef,
			vendorInvitationsRef,
			vendorEventPropertyRef,
			tableFieldsVendors,
			tableFieldsInvitations,
			formatDate,
			btnDeleteVendorEventProperty,
			btnCreateVendorInvitation,
			btnDeleteVendorInvitation,
			saveButtonInvitationRef,
			filterOptions,
			vendorInvitation,
			reloadVendorEventProperties,
			onRowClick
		};
	}
});
</script>
<style scoped>

</style>
