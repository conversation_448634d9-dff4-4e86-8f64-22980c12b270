<template>
  <div>
    <b-row>
      <b-col><h2>Verkaufsbereiche</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          size="sm"
          text="Verkaufszone hinzufügen"
          icon="plus-square"
          @click="createSalesArea"
        />
      </b-col>
    </b-row>
    <LoadingSpinner v-if="loading" />
    <template
      v-if="
        vendorEmployeeList && availableArticleGroups && availableDepositGroups
      "
    >
      <SalesArea
        v-for="sa in salesAreas"
        :key="sa.id"
        :selected-vendor="selectedVendor"
        :selected-event="selectedEvent"
        :sales-area="sa"
        :vendor-employee-list="vendorEmployeeList"
        :available-article-groups="availableArticleGroups"
        :available-deposit-groups="availableDepositGroups"
        @deleted="reloadAvailableSalesAreas()"
      />
    </template>
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadingSpinner from '../LoadingSpinner.vue';
import LoadButton from '../buttons/LoadButton.vue';
import SalesArea from './elements/SalesArea.vue';
const uuid = require('uuid/v4');

export default {
	components: { LoadingSpinner, LoadButton, SalesArea },
	props: ['selectedVendor', 'selectedEvent'],
	data() {
		return {
			salesAreas: null,
			vendorEmployeeList: null,
			availableArticleGroups: null,
			availableDepositGroups: null,
			loading: true
		};
	},
	computed: {},
	watch: {
		selectedEvent() {
			this.loading = true;
			this.reloadAvailableSalesAreas();
			this.reloadAvailableArticleGroups();
			this.reloadPermVendorUser();
			this.reloadAvailableDepositGroups();
		}
	},
	created() {
		this.reloadAvailableSalesAreas();
		this.reloadAvailableArticleGroups();
		this.reloadPermVendorUser();
		this.reloadAvailableDepositGroups();
	},
	methods: {
		reloadAvailableSalesAreas() {
			let params = new URLSearchParams();
			params.append(
				'include',
				'["PermSalesAreaUser", "ArticleGroup", "DepositGroup"]'
			);
			params.append('filterVendor', this.selectedVendor.id);
			params.append('filterEvent', this.selectedEvent.id);
			API.get('innorder', '/SalesArea?' + params)
				.then((response) => {
					this.salesAreas = response.rows;
					this.loading = false;
				})
				.catch((error) => {
					console.log(error);
					this.$bvToast.toast(
						'Beim Laden der SalesAreas ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadPermVendorUser() {
			let params = new URLSearchParams();
			params.append('include', '["PermVendorUser"]');
			params.append('filterVendor', this.selectedVendor.id);
			API.get('innorder', '/VendorEmployee?' + params)
				.then((response) => {
					this.vendorEmployeeList = response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der VendorPermissions ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadAvailableArticleGroups() {
			let params = new URLSearchParams();
			params.append('filterEvent', this.selectedEvent.id);
			params.append('filterVendor', this.selectedVendor.id);
			API.get('innorder', '/ArticleGroup?' + params)
				.then((response) => {
					this.availableArticleGroups = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der VendorPermissions ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadAvailableDepositGroups() {
			let params = new URLSearchParams();
			params.append('filterEvent', this.selectedEvent.id);
			params.append('filterVendor', this.selectedVendor.id);
			API.get('innorder', '/DepositGroup?' + params)
				.then((response) => {
					this.availableDepositGroups = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der VendorPermissions ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		createSalesArea(btn) {
			let item = {
				id: uuid(),
				VendorId: this.selectedVendor.id,
				EventId: this.selectedEvent.id,
				name: 'Neue Verkaufszone'
			};
			API.post('innorder', '/SalesArea/', { body: item })
				.then((response) => {
					this.$bvToast.toast(
						'Die Verkaufszone "' + item.name + '" wurde erfolgreich erstellt.',
						{
							title: 'Erstellen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					this.reloadAvailableSalesAreas();
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Erstellen von "' +
              item.name +
              '" ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		}
	}
};
</script>
