<template>
  <div>
    <b-row>
      <b-col>
        <h2>Entwertungsgruppen</h2>
      </b-col>
      <b-col>
        <div class="d-flex">
          <b-button
            class="ml-auto"
            variant="primary"
            size="sm"
            @click="btnCreateRedemtionGroup()"
          >
            <FontAwesomeIcon icon="plus-square" />
            Entwertungsgruppe erstellen
          </b-button>
        </div>
      </b-col>
    </b-row>
    <b-table
      id="redemptionGroupTable"
      ref="redemptionGroupTableRef"
      hover
      :items="redemptionGroupMap.redemptionGroups"
      :fields="tableFields"
      :busy="isLoadingRef"
      class="mt-3 table-clickable"
      head-variant="light"
      @row-clicked="tableRowClick"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            size="sm"
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="btnDeleteRedemtionGroup($event, data)"
          />
        </div>
      </template>
      <template #cell(method)="{value}">
        {{ value == 'REDEEM' ? 'Entwerten' : 'Entwerten und Verknüpfen' }}
      </template>
      <template #cell(validTickets)="{value}">
        <b-badge
          v-for="(ticketId, key) in Object.keys(value)"
          :key="key"
          variant="secondary"
          class="mr-1"
        >
          {{ getTicketName(ticketId) }}
        </b-badge>
      </template>
    </b-table>
    <RedemptionGroupSidebar 
      ref="redemptionGroupSidebarRef"
    />
  </div>
</template>
<script lang="ts">
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import RedemptionGroupSidebar from './InnticketRedemptionGroupSidebar.vue';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import type { BvTableCellData, ResetButton } from '@innevent/webapp-types';
import { formatDate } from '@innevent/webapp-utils';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import type { BTable, BvTableFieldArray } from 'bootstrap-vue';
import type { RedemptionGroup } from '@innevent/types';
import { useRedemptionGroupMap } from '../../../../states/redemptionGroup';
import { useTicketMapState } from '../../../../states/ticketMap';


export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		RedemptionGroupSidebar
	},
	setup() {
		const instance = getCurrentInstance();
		const redemptionGroupTableRef = ref<BTable>();
		const redemptionGroupSidebarRef = ref<InstanceType<typeof RedemptionGroupSidebar>>();
		const { redemptionGroupMapRef: redemptionGroupMap, isLoadingRef } = useRedemptionGroupMap();
		const { ticketMapRef } = useTicketMapState();

		
		const tableFields: BvTableFieldArray =  [
			{ key: 'redemptionGroupName', label: 'Name', sortable: true },
			{ key: 'description', label: 'Beschreibung' },
			{ key: 'method', label: 'Methode' },
			{ key: 'validTickets', label: 'Tickets' },
			{ key: 'buttons', label: '' }
		];

		function getTicketName(ticketId: string) {
			return ticketMapRef.value.tickets.find(ticket => 
				ticket.ticketId == ticketId)?.ticketName ?? 'Ticket nicht gefunden';
		}

		function onRedemptionGroupChange() {
			redemptionGroupTableRef.value?.refresh();
		}

		async function btnDeleteRedemtionGroup(btn: ResetButton, cellData: BvTableCellData<RedemptionGroup>) {
			try {
				await api.deleteRedemptionGroup({
					key: {
						eventId: cellData.item.eventId,
						redemptionGroupId: cellData.item.redemptionGroupId
					}
				});
				redemptionGroupMap.value.redemptionGroups = redemptionGroupMap.value.redemptionGroups.filter(item => 
					item.redemptionGroupId !== cellData.item.redemptionGroupId
				);
				onRedemptionGroupChange();
				notifySuccess({ instance });
			} catch (error) {
				notifyError({ instance });
			}
			btn.reset();
		}

		function tableRowClick(redemptionGroup: RedemptionGroup) {
			const redemptionGroupObj: RedemptionGroup = JSON.parse(JSON.stringify(redemptionGroup));
			redemptionGroupSidebarRef.value?.openForEdit(redemptionGroupObj);
		}

		function btnCreateRedemtionGroup() {
			redemptionGroupSidebarRef.value?.openForCreate();
		}
		

		return {
			tableFields,
			redemptionGroupSidebarRef,
			redemptionGroupTableRef,
			btnDeleteRedemtionGroup,
			tableRowClick,
			btnCreateRedemtionGroup,
			formatDate,
			redemptionGroupMap, 
			getTicketName,
			isLoadingRef
		};
	}
});
</script>

<style>
</style>
