<template>
  <b-card
    title="Online Top-Down"
    tag="article"
  >
    <!-- <b-card-text>
      If you still have token credit on your festival wristband, you can have this paid out to your bank account. Please fill out the following data on the form.
    </b-card-text>
    <b-card-text>
      Falls du noch Token-Guthaben auf deinem Festival Armband hast kannst du dieses auf dein Bankkonto auszahlen lassen, bitte fülle dafür die folgenden Daten des Formulares aus.
    </b-card-text> -->
    <!-- <b-card-text>
      Die Online-Auszahlung für das Electrifinity 2025 hat noch nicht begonnen.
    </b-card-text> -->
    <b-card-text>
      The Online payout for Electrifinity 2025 is closed. <NAME_EMAIL> for further information.
    </b-card-text>
    <b-card-text>
      Die Online-Auszahlung für das Electrifinity 2025 ist beendet. Bitte wende <NAME_EMAIL> für weitere Informationen.
    </b-card-text>
    <!-- <b-button
      to="/token-payout"
      variant="primary"
      block
    >
      Online Top-Down
    </b-button> -->
  </b-card>
</template>
<script>

// Frameworks

// Vue Components
// import Navigation from './Eventportal/Navigation.vue'

export default {
	components: { },
	props: [],
	data() { return {
		selectedEventId: null
	};},
	computed: {
		currentEvent() {
			if (this.$route.params.event) return this.events.find(e => e.id == this.$route.params.event);
			else return null;
		}
	},
	watch: {

	},
	created() {

	},
	methods: {

	}
};

</script>
<style scoped>

</style>
