<template>
  <b-sidebar
    v-model="sidebarOpenRef"
    sidebar-class="top-fixed-header"
    body-class="p-3"
    z-index="4"
    title="Ticketimport"
    width="1500px"
    backdrop
    right
  >
    <div>
      <b-container class="mb-4">
        <b-row>
          <b-col sm="8">
            <b-card bg-variant="light">
              <b-row class="mt-2">
                <b-col sm="3">
                  <label>Name</label>
                </b-col>
                <b-col sm="9">
                  <b-form-input
                    v-model="ticketImportRef.name"
                    type="text"
                    @keyup.enter="keyUpEnterSave()"
                  />
                </b-col>
              </b-row>
              <b-row class="mt-2">
                <b-col sm="3">
                  <label>Beschreibung</label>
                </b-col>
                <b-col sm="9">
                  <b-form-input
                    v-model="ticketImportRef.description"
                    type="text"
                    @keyup.enter="keyUpEnterSave()"
                  />
                </b-col>
              </b-row>
              <b-row class="mt-2">
                <b-col sm="3">
                  <label>Enthält die erste Zeile Header Informationen</label>
                </b-col>
                <b-col sm="9">
                  <b-form-radio-group
                    v-model="ticketImportRef.isFirstLineHeader"
                    :options="firstLineHeaderOptions"
                    button-variant="outline-primary"
                    buttons
                  />
                </b-col>
              </b-row>
              <LoadButton
                ref="createTicketImportButton"
                class="float-right mt-2"
                variant="primary"
                text="Ticketimport erstellen / speichern"
                @click="btnClickCreateTicketImport"
              />
            </b-card>
            <b-card
              class="mt-5"
              title="Schritte"
              bg-variant="light"
            >
              <b-tabs
                v-model="currentTabRef"
                content-class="mt-3"
              >
                <b-tab
                  title="1) Upload"
                  active
                >
                  Bitte lade zunächst eine Datei mit folgenden Feldern hoch.
                </b-tab>
                <b-tab
                  title="2) Feld Mapping"
                  :disabled="getColumnsArray.length < 1"
                >
                  <b-container>
                    <b-row
                      v-for="(field, fieldKey) of fieldMappingRef"
                      :key="fieldKey"
                      class="mt-2"
                    >
                      <b-col sm="3">
                        <label
                          :for="`field-${fieldKey}`"
                        >{{ field.name }}:</label>
                      </b-col>
                      <b-col sm="9">
                        <Multiselect
                          :id="`field-${fieldKey}`"
                          v-model="field.columnValue"
                          label="firstRowValue"
                          placeholder="Nichts ausgewählt"
                          :options="getColumnsArray"
                          :option-height="104"
                          :show-labels="false"
                          :allowEmpty="true"
                          :maxHeight="1000"
                        >
                          <template #singleLabel="props">
                            <span>{{ props.option.firstRowValue }}</span>
                          </template>
                          <template #option="props">
                            <h6>{{ props.option.firstRowValue }}</h6>
                            <span>{{ props.option.exampleRowValues }}</span>
                          </template>
                        </Multiselect>
                      </b-col>
                    </b-row>
                    <LoadButton
                      ref="saveFieldMappingRef"
                      class="float-right mt-2"
                      variant="primary"
                      text="Feldmapping speichern"
                      :disabled="!isFieldMappingSavePossible || secondInspectRunningRef"
                      @click="btnSaveFieldMapping"
                    />
                    <b-progress
                      v-if="secondInspectRunningRef"
                      :value="secondInspectProgressRef"
                      class="mb-3"
                      animated
                    />
                  </b-container>
                </b-tab>
                <b-tab
                  title="3) Ticket Mapping"
                  :disabled="!ticketMappingActiveRef"
                >
                  <InnticketTicketMapper
                    v-if="ticketImportRef.ticketImportId"
                    ref="ticketMapperRef"
                    v-model="ticketImportRef"
                  />
                </b-tab>
                <b-tab
                  title="4) Status Mapping"
                  :disabled="!statusMappingActiveRef"
                >
                  <InnticketStatusMapper
                    v-if="ticketImportRef.ticketImportId"
                    ref="statusMapperRef"
                    v-model="ticketImportRef"
                  />
                </b-tab>
                <b-tab
                  title="5) Ausführung"
                  :disabled="!ticketMappingActiveRef"
                >
                  <b-row>
                    <LoadButton
                      class="m-3 ml-auto"
                      variant="primary"
                      text="Import ausführen"
                      size="sm"
                      :disabled="!isImportPossible || !lastSuccessfullInspectedFile"
                      @click="btnExecuteImport($event)"
                    />
                  </b-row>
                  <b-table
                    hover
                    sortBy="executionNumber"
                    :sortDesc="true"
                    :items="ticketExecutionTableItems"
                    :fields="ticketExecutionTableFields"
                  >
                    <template #cell(executionDate)="{value}">
                      {{ formatDate(value) }}
                    </template>
                    <template #cell(eventEmployee)="{value}">
                      {{ value.firstName + ' ' + value.lastName }}
                    </template>
                    <template #cell(importFileId)="{value}">
                      {{ value }}
                    </template>
                    <template #cell(status)="{value}">
                      <b-badge
                        v-if="value === 'IMPORT_RUNNING' || value === 'IMPORT_CREATED'"
                        variant="success"
                      >
                        In Arbeit
                        <b-spinner
                          small
                        />
                      </b-badge>
                      <b-badge
                        v-if="value === 'IMPORT_ERROR'"
                        variant="danger"
                      >
                        Error
                      </b-badge>
                      <b-badge
                        v-if="value === 'IMPORT_DONE'"
                        variant="success"
                      >
                        Erfolgreich
                      </b-badge>
                    </template>
                    <template #row-details="{item}">
                      <b-alert
                        v-if="item.stats"
                        variant="success"
                        show
                      >
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Verarbeitete Tickets:</b>
                          </b-col>
                          <b-col>{{ item.stats.allTickets }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Neue Tickets:</b>
                          </b-col>
                          <b-col>{{ item.stats.createdTickets }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Bereits vorhandene Tickets:</b>
                          </b-col>
                          <b-col>{{ item.stats.existingTickets }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Aktualisierte Tickets:</b>
                          </b-col>
                          <b-col>{{ item.stats.updatedTickets }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Ungültige Tickets:</b>
                          </b-col>
                          <b-col>{{ item.stats.allInvalidTickets }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Ungültige Mappings:</b>
                          </b-col>
                          <b-col>{{ item.stats.invalidMappings }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Fehler in der Verarbeitung:</b>
                          </b-col>
                          <b-col>{{ item.stats.invalidProcessing }}</b-col>
                        </b-row>
                        <b-row class="mb-2">
                          <b-col
                            sm="3"
                            class="text-sm-right"
                          >
                            <b>Fehlerhafte Tickets:</b>
                          </b-col>
                          <b-col><pre>{{ item.invalidTickets }}</pre></b-col>
                        </b-row>
                      </b-alert>
                      <b-alert
                        v-if="item.importError"
                        variant="danger"
                        show
                      >
                        {{ item.importError }}
                      </b-alert>
                    </template>
                  </b-table>
                </b-tab>
              </b-tabs>
            </b-card>
          </b-col>
          <b-col sm="4">
            <b-badge
              v-if="
                !ticketImportRef.uploadedFiles ||
                  Object.keys(ticketImportRef.uploadedFiles).length === 0
              "
              class="mx-auto"
            >
              Keine Dateien vorhanden
            </b-badge>
            <b-list-group v-else>
              <b-list-group-item
                v-for="(file, fileIndex) in uploadedFiles"
                :key="file.fileNumber"
                class="d-flex align-items-center"
              >
                <b-container>
                  <b-row class="mb-2">
                    <b-badge class="mr-2">
                      {{ file.fileNumber }}
                    </b-badge>
                    <b-badge class="mr-2">
                      {{ formatDate(file.createdOn) }}
                    </b-badge>
                    <b-badge
                      v-if="file.badgeVariant === 'primary'"
                      :variant="file.badgeVariant"
                    >
                      {{ file.badgeText }}
                      <b-spinner
                        small
                      />
                    </b-badge>
                    <b-badge
                      v-else
                      :variant="file.badgeVariant"
                    >
                      {{ file.badgeText }}
                    </b-badge>
                  </b-row>
                  <b-row>
                    <FontAwesomeIcon
                      icon="file"
                      class="mr-2"
                    />
                    <span>{{ file.fileName }}</span>
                  </b-row>
                  <b-row>
                    {{ file.inspectError }}
                  </b-row>
                  <b-row>
                    <DeleteButton
                      v-if="file.fileNumber !== 1"
                      block
                      size="sm"
                      icon="trash"
                      text="Entfernen"
                      variant="danger"
                      @click="btnDeleteFile($event, file)"
                    />
                  </b-row>
                  <b-row v-if="(fileIndex + 1) === uploadedFiles.length && isImportPossible">
                    <LoadButton
                      block
                      class="float-right mt-3"
                      variant="primary"
                      text="Import ausführen"
                      :disabled="file.status !=='INSPECTION_DONE'"
                      size="sm"
                      @click="btnExecuteImport($event, file)"
                    />
                  </b-row>
                </b-container>
              </b-list-group-item>
            </b-list-group>

            <b-container class="mt-4">
              <b-row>
                <b-form-file
                  v-model="newFileUpload"
                  :disabled="!ticketImportRef.ticketImportId"
                  placeholder="Choose a file or drop it here..."
                  drop-placeholder="Drop file here..."
                />
              </b-row>
              <b-row class="mt-1">
                <LoadButton
                  ref="saveButton"
                  variant="primary"
                  text="Hochladen"
                  block="true"
                  :disabled="!Boolean(newFileUpload)"
                  @click="btnUploadFile"
                />
              </b-row>
            </b-container>
          </b-col>
        </b-row>
      </b-container>
    </div>
  </b-sidebar>
</template>
<script lang="ts">
/* eslint-disable @typescript-eslint/naming-convention */
import type { ComputedRef } from '@vue/composition-api';
import { defineComponent, ref, getCurrentInstance, computed, onMounted, nextTick } from '@vue/composition-api';
import LoadButton from '@innevent/webapp-components/button/LoadButton.vue';
import DeleteButton from '@innevent/webapp-components/button/DeleteButton.vue';
import type { ModalAction, ResetButton, Ticket } from '@innevent/webapp-types';
import type { FieldMappingItem, TicketImport, TicketImportColumn, TicketImportExecution, 
	TicketImportFile, TicketImportMappingAttribute }  from '@innevent/types';
import { refreshTicketMap } from '../../../../states/ticketMap';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import * as api from '@innevent/webapp-api';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import Vue from 'vue';
import { formatDate } from '@innevent/webapp-utils';
import Multiselect from 'vue-multiselect';
import { currentEventRef } from '../../../../states/eventState';
import InnticketTicketMapper from './InnticketTicketMapper.vue';
import InnticketStatusMapper from './InnticketStatusMapper.vue';
import { refreshTicketImportMap, createTicketImport } from '../../../../states/ticketImport';
import { innTicketImport as log } from '../../../../loglevel';

type FieldMapRef = {
	[fieldKey: string]: FieldMap;
}
 type FieldMap = {
    name: string;
	key: string;
	mappingAttribute: TicketImportMappingAttribute;
	isRequired: boolean;
	columnValue: TicketImportColumn | null;
}

type TicketImportFileWithBadgeInfo = TicketImportFile & {
	badgeVariant: string;
	badgeText: string;
}

export default defineComponent({
	components: {
		LoadButton,
		DeleteButton,
		FontAwesomeIcon,
		Multiselect,
		InnticketTicketMapper,
		InnticketStatusMapper
	},
	setup() {
		const actionRef = ref<ModalAction>('create');
		const sidebarOpenRef = ref<boolean>(false);
		const instance = getCurrentInstance();
		const currentTicket = ref<Ticket>();
		const createTicketImportButton = ref<InstanceType<typeof LoadButton>>();
		const currentTabRef = ref<number>(0);
		const saveButton = ref<InstanceType<typeof LoadButton>>();
		const ticketMapperRef = ref<InstanceType<typeof InnticketTicketMapper>>();
		const statusMapperRef = ref<InstanceType<typeof InnticketStatusMapper>>();
		let checkForTicketValueMappingIntervalId;
		let checkForTicketValueMappingCounter: number = 0;
		const secondInspectRunningRef = ref(false);
		const secondInspectProgressRef = ref(10);
		const firstLineHeaderOptions = [{
			text: 'Ja',
			value: true
		}, {
			text: 'Nein',
			value: false
		}];


		const fieldMappingRef = ref<FieldMapRef>({
			ticketCode: {
				name: 'Ticket Code*',
				key: 'ticketCode',
				mappingAttribute: 'ticket.code',
				isRequired: true,
				columnValue: null
			},
			generatedTicketName: {
				name: 'Ticketname*',
				key: 'generatedTicketName',
				mappingAttribute: 'ticket.generatedTicketName',
				isRequired: true,
				columnValue: null
			},
			personalizationFirstName: {
				name: 'Personalisierung Vorname',
				key: 'personalizationFirstName',
				mappingAttribute: 'ticket.personalization.firstName',
				isRequired: false,
				columnValue: null
			},
			personalizationLastName: {
				name: 'Personalisierung Nachname',
				key: 'personalizationLastName',
				mappingAttribute: 'ticket.personalization.lastName',
				isRequired: false,
				columnValue: null
			},
			price: {
				name: 'Brutto Preis',
				key: 'price',
				mappingAttribute: 'ticket.price.priceGross',
				isRequired: false,
				columnValue: null
			},
			status: {
				name: 'Status',
				key: 'status',
				mappingAttribute: 'ticket.status',
				isRequired: false,
				columnValue: null
			}
		});

		let ticketImportRef = ref<Partial<TicketImport>>({});
		let newFileUpload = ref<File | undefined>(undefined);

		async function keyUpEnterSave() {
			createTicketImportButton.value?.clickButton();
		}


		const getColumnsArray = computed(() => {
			if (!ticketImportRef.value.columns) return [];
			return Object.values(ticketImportRef.value.columns).map(column => {
				column.exampleRowValues = column.exampleRowValues.filter(value => value);
				return column;
			});
		});

		async function btnSaveFieldMapping(btn: ResetButton) {
			try {
				if (!ticketImportRef.value) {
					return;
				}
				const { eventId, ticketImportId } = ticketImportRef.value;
				const newFieldMapping = Object.values(fieldMappingRef.value).reduce((mappingItemArray, mappingItem) => {
					if (mappingItem.columnValue) {
						mappingItemArray.push({
							innTicketField: mappingItem.mappingAttribute,
							columnNumber: mappingItem.columnValue.columnNumber
						});
					}
					return mappingItemArray;
				}, [] as FieldMappingItem[]);
				await api.updateTicketImport({
					key: {
						eventId: eventId!,
						ticketImportId: ticketImportId!
					},
					data: {
						fieldMapping: newFieldMapping
					}
				});
				ticketImportRef.value.fieldMapping = newFieldMapping;
				notifySuccess({ instance, title: 'Feld-Mapping angepasst' });

				const isTicketFieldMappingSet = newFieldMapping.some(fieldMapping => fieldMapping.innTicketField === 'ticket.generatedTicketName');
				if (isTicketFieldMappingSet) {
					secondInspectRunningRef.value = true;
					checkForTicketValueMappingCounter = 0;
					secondInspectProgressRef.value = 0;
					clearInterval(checkForTicketValueMappingIntervalId);
					checkForTicketValueMappingIntervalId = setInterval(checkForTicketValueMapping, 2000);
				}
			} catch (error: any) {
				notifyError({ instance, message: error });
			}
			btn.reset();
		}

		async function btnClickCreateTicketImport(btn: ResetButton): Promise<void> {
			try {
				const ticketImportExists = ticketImportRef.value.ticketImportId !== undefined;
				if (ticketImportExists) {
					ticketImportRef.value = await api.updateTicketImport({
						key: {
							eventId: currentEventRef.value!.eventId!,
							ticketImportId: ticketImportRef.value.ticketImportId!
						},
						data: {
							name: ticketImportRef.value.name!,
							description: ticketImportRef.value.description ?? '',
							isFirstLineHeader: ticketImportRef.value.isFirstLineHeader
						}
					});
				} else {
					ticketImportRef.value = await createTicketImport({
						key: {
							eventId: currentEventRef.value!.eventId!
						},
						data: {
							name: ticketImportRef.value.name!,
							description: ticketImportRef.value.description ?? ''
						}
					});
				}
				notifySuccess({ instance, title: 'Import erfolgreich gespeichert' });

			} catch (error: any) {
				notifyError({ instance, title: 'Speichern fehlerhaft', message: error });
			}
			btn.reset();
		}

		async function btnUploadFile(btn: ResetButton) {
			if (!ticketImportRef.value.ticketImportId) {
				notifyError({ instance,  title: 'TicketImport', message: 'Ticketimport muss zuerst erstellt werden' });
				return;
			}
			
			try {
				if (!newFileUpload.value) {
					notifyError({ instance,  title: 'TicketImport', message: 'Keine Datei ausgewählt' });
					return;
				}
				const ticketImportFile: TicketImportFile = await api.createUpload({
					key: {
						eventId: currentEventRef.value!.eventId!,
						ticketImportId: ticketImportRef.value.ticketImportId
					},
					data: {
						filename: newFileUpload.value.name
					}
				});
				await api.uploadTicketImportFile({
					data: {
						uploadUrl: ticketImportFile.uploadUrl as string,
						file: newFileUpload.value
					}
				});

				Vue.set(ticketImportRef.value.uploadedFiles!, ticketImportFile.fileNumber, ticketImportFile);

				newFileUpload.value = undefined;
				notifySuccess({ instance, title: 'Upload erfolgreich' });
				btn.reset();

				checkForUploadStatus(ticketImportFile);
			} catch (error) {
				notifyError({ instance,  title: 'TicketImport', error });
				btn.reset();
			}
		}

		async function checkForUploadStatus(ticketImportFile: TicketImportFile) {
			const ticketImportUpdated = await api.getTicketImport({ key: {
				eventId: currentEventRef.value!.eventId!, 
				ticketImportId: ticketImportRef.value!.ticketImportId!
			} });
			ticketImportRef.value = ticketImportUpdated;
			ticketMapperRef.value?.refreshTicketValueMapping(ticketImportRef.value as TicketImport);
			
			const lastTicketImport = ticketImportUpdated.uploadedFiles[ticketImportFile.fileNumber];
			
			if (!['INSPECTION_DONE', 'INSPECTION_ERROR', 'INSPECTION_SKIPPED'].includes(lastTicketImport.status)) {
				setTimeout(() => checkForUploadStatus(ticketImportFile), 1000);
			} else {
				nextTick(() => {
					currentTabRef.value = 1;
				});
			}
		}

		function openForCreate() {
			actionRef.value = 'create';
			sidebarOpenRef.value = true;
			ticketImportRef.value = {};

			//Reset Field-Mapping
			Object.values(fieldMappingRef.value).forEach((mappingField) => mappingField.columnValue = null);
		}

		function openForEdit(ticketImportObj: TicketImport) {
			actionRef.value = 'edit';
			sidebarOpenRef.value = true;
			ticketImportRef.value = ticketImportObj;
			Object.values(fieldMappingRef.value).forEach((mappingField) => mappingField.columnValue = null);

			//Field-Mapping
			ticketImportRef.value.fieldMapping?.forEach((mappingItem) => {
				const mappingField = Object.values(fieldMappingRef.value).find(FieldMappingItem => 
					FieldMappingItem.mappingAttribute === mappingItem.innTicketField);
				if (mappingField) {
					mappingField.columnValue = ticketImportRef.value.columns?.[mappingItem.columnNumber] ?? null;
				}
			});
		}

		const ticketMappingActiveRef = computed(() => {
			if (!ticketImportRef.value.columns) return false;
			return ticketImportRef.value.fieldMapping?.some(mapping => mapping.innTicketField === 'ticket.generatedTicketName');
		});

		const statusMappingActiveRef = computed(() => {
			if (!ticketImportRef.value.columns) return false;
			return ticketImportRef.value.fieldMapping?.some(mapping => mapping.innTicketField === 'ticket.status');
		});

		onMounted(async () => {
			await refreshTicketMap();
		});

		const ticketExecutionTableItems: ComputedRef<TicketImportExecution[]> = computed(() => {
			if (!ticketImportRef.value.executions) return [];
			return Object.values(ticketImportRef.value.executions).map(execution => {
				return {
					...execution,
					_showDetails: true
				};
			});
		});

		async function btnExecuteImport(btn: ResetButton, file?: TicketImportFile) {
			try {
				if (!ticketImportRef.value) return;
				file ??= lastSuccessfullInspectedFile.value;
				if (!file) {
					notifyError({ instance,  title: 'TicketImport', message: 'Keine Erfolgreich inspizierte Datei für den Import vorhanden' });
					return;
				}
				const execution = await api.runImport({
					key: {
						eventId: currentEventRef.value!.eventId!,
						ticketImportId: ticketImportRef.value.ticketImportId!
					},
					data: {
						importFileId: file.fileNumber.toString()
					}
				});
				currentTabRef.value = 4;
				await refreshTicketImportMap();
				const { executionNumber } = execution;
				Vue.set(ticketImportRef.value.executions!, executionNumber, execution);
				notifySuccess({ instance, title: 'Import erfolgreich gestartet' });
				await checkForExecutionStatus(executionNumber);
			} catch (error) {
				console.log(error);
				notifyError({ instance,  title: 'TicketImport', message: 'Fehler beim Starten des Ticketimports' });
			}
			btn.reset();
		}

		const ticketExecutionTableFields = [{
			label: 'Nr',
			key: 'executionNumber',
			sortable: true
		}, {
			label: 'Datum',
			key: 'executionDate',
			sortable: true
		}, {
			label: 'Mitarbeiter',
			key: 'eventEmployee'
		}, {
			label: 'Datei',
			key: 'importFileId'
		}, {
			label: 'Status',
			key: 'status'
		}];

		async function checkForExecutionStatus(executionNumber: number) {
			log.debug('checkForExecutionStatus', executionNumber);
			const ticketImportUpdated = await api.getTicketImport({ key: {
				eventId: currentEventRef.value!.eventId!, 
				ticketImportId: ticketImportRef.value!.ticketImportId!
			} });
			ticketImportRef.value = ticketImportUpdated;
			
			const lastExecution = ticketImportUpdated.executions[executionNumber];
			if (!['IMPORT_DONE', 'IMPORT_ERROR'].includes(lastExecution.status)) {
				setTimeout(() => checkForExecutionStatus(executionNumber), 5000);
			}
		}

		async function checkForTicketValueMapping() {
			log.info('run checkForTicketValueMapping()');
			checkForTicketValueMappingCounter++;
			const ticketImportUpdated = await api.getTicketImport({ key: {
				eventId: currentEventRef.value!.eventId!, 
				ticketImportId: ticketImportRef.value!.ticketImportId!
			} });

			const hasTicketValueMappingChanged = JSON.stringify(ticketImportUpdated.valueMapping.generatedTicketName) !== 
				JSON.stringify(ticketImportRef.value.valueMapping?.generatedTicketName);
			log.debug('hasTicketValueMappingChanged', hasTicketValueMappingChanged);

			const hasStatusValueMappingChanged = JSON.stringify(ticketImportUpdated.valueMapping.status) !== 
				JSON.stringify(ticketImportRef.value.valueMapping?.status);
			log.debug('hasStatusValueMappingChanged', hasStatusValueMappingChanged);

			ticketImportRef.value = ticketImportUpdated;
			secondInspectProgressRef.value += 10;
			
			// TODO, checkForTicketValueMapping also runs if no hasTicketValueMappingChanged
			if (hasTicketValueMappingChanged || hasStatusValueMappingChanged || checkForTicketValueMappingCounter > 10) {
				clearInterval(checkForTicketValueMappingIntervalId);
				secondInspectRunningRef.value = false;
				currentTabRef.value = 2;
				ticketMapperRef.value?.refreshTicketValueMapping(ticketImportUpdated);
				statusMapperRef.value?.refreshStatusValueMapping(ticketImportUpdated);
			}
		}

		const uploadedFiles: ComputedRef<TicketImportFileWithBadgeInfo[]> = computed(() => {
			return Object.values(ticketImportRef.value.uploadedFiles ?? {}).map(file => {
				const returnFile: TicketImportFileWithBadgeInfo = {
					... file,
					badgeVariant: 'primary',
					badgeText: 'Wird inspiziert...'
				};
				if (file.status === 'INSPECTION_DONE') {
					returnFile.badgeVariant = 'success';
					returnFile.badgeText = 'Erfolgreich inspiziert';
				}
				else if (file.status === 'INSPECTION_ERROR') {
					returnFile.badgeVariant = 'danger';
					returnFile.badgeText = 'Fehler';
				}
				return returnFile;
			});
		});

		async function btnDeleteFile(btn: ResetButton, file: TicketImportFile) {
			try {
				if (!ticketImportRef.value) return;
				await api.removeTicketImportFile({
					key: {
						eventId: currentEventRef.value!.eventId!,
						ticketImportId: ticketImportRef.value.ticketImportId!,
						fileNumber: file.fileNumber
					}
				});

				Vue.delete(ticketImportRef.value.uploadedFiles!, file.fileNumber);
				notifySuccess({ instance, title: 'Datei erfolgreich gelöscht' });
			} catch (error) {
				console.log(error);
				notifyError({ instance,  title: 'TicketImport', message: 'Fehler beim Löschen der Datei' });
			}
			btn.reset();
		}

		const isImportPossible = computed(() => {
			if (!ticketImportRef.value.fieldMapping || !ticketImportRef.value.valueMapping || 
				!ticketImportRef.value.uploadedFiles || !ticketImportRef.value.columns) {
				return false;
			}
			const { fieldMapping, valueMapping, uploadedFiles } = ticketImportRef.value;
			if (!fieldMapping.some(mapping => mapping.innTicketField === 'ticket.code')) {
				return false;
			}
			if (!fieldMapping.some(mapping => mapping.innTicketField === 'ticket.generatedTicketName')) {
				return false;
			}
			if (valueMapping.generatedTicketName.length < 1) {
				return false;
			}
			if (Object.keys(uploadedFiles).length < 1) {
				return false;
			}
			return true;
		});

		const isFieldMappingSavePossible = computed(() => {
			return fieldMappingRef.value.generatedTicketName.columnValue !== null && fieldMappingRef.value.ticketCode.columnValue !== null;
		});

		const lastSuccessfullInspectedFile = computed(() => uploadedFiles.value.reduce((resultFile, file) => {
			if (file.status === 'INSPECTION_DONE' && file.fileNumber > (resultFile?.fileNumber ?? 0)) return file;
		}, undefined as TicketImportFileWithBadgeInfo | undefined));

		return {
			actionRef,
			sidebarOpenRef,
			saveButton,
			keyUpEnterSave,
			openForCreate,
			openForEdit,
			currentTicket,
			ticketImportRef,
			btnClickCreateTicketImport,
			newFileUpload,
			btnUploadFile,
			formatDate,
			createTicketImportButton,
			fieldMappingRef,
			getColumnsArray,
			btnSaveFieldMapping,
			ticketMappingActiveRef,
			ticketExecutionTableItems,
			btnExecuteImport,
			currentTabRef,
			ticketExecutionTableFields,
			ticketMapperRef,
			statusMapperRef,
			secondInspectRunningRef,
			secondInspectProgressRef,
			uploadedFiles,
			btnDeleteFile,
			isImportPossible,
			isFieldMappingSavePossible,
			firstLineHeaderOptions,
			lastSuccessfullInspectedFile,
			statusMappingActiveRef
		};
	}
});
</script>