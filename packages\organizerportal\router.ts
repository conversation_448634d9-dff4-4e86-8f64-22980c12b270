import Vue from 'vue';

import VueRouter from 'vue-router';
Vue.use(VueRouter);

import OrganizerPortalHome from './src/vue/OrganizerPortalHome.vue';
import Eventportal from './src/vue/Eventportal.vue';
import Organizerportal from './src/vue/Eventportal.vue';
import PageNotFound from './src/vue/PageNotFound.vue';

import GeneralEventdataPage from './src/vue/Eventportal/general/GeneralEventdataPage.vue';
import GeneralEmployeePage from './src/vue/Eventportal/general/GeneralEmployeePage.vue';
import EventEmployeeInvitationPage from './src/vue/Eventportal/general/EventEmployeeInvitationPage.vue';
import DeviceOverviewPage from './src/vue/Eventportal/general/GeneralConnectionLogPage.vue';
import CashboxPage from './src/vue/Eventportal/general/CashboxPage.vue';
import EcTerminalPage from './src/vue/Eventportal/general/EcTerminalPage.vue';

import InnorderDashboardPage from './src/vue/Eventportal/innorder/InnorderDashboardPage.vue';
import InnorderDepositGroupsPage from './src/vue/Eventportal/innorder/InnorderDepositGroupsPage.vue';
import InnorderTokenstationPage from './src/vue/Eventportal/innorder/InnorderTokenstationPage.vue';
import InnorderStatsPage from './src/vue/Eventportal/innorder/InnorderStatsPage.vue';
import InnorderVendorPage from './src/vue/Eventportal/innorder/InnorderVendorPage.vue';
import InnorderSettingsPage from './src/vue/Eventportal/innorder/InnorderSettingsPage.vue';


import InnticketDashboardPage from './src/vue/Eventportal/innticket/InnticketDashboardPage.vue';
import InnticketRedemptionGroupPage from './src/vue/Eventportal/innticket/InnticketRedemptionGroupPage.vue';
// import InnticketEmployeePage from './src/vue/Eventportal/innticket/InnticketEmployeePage.vue'
import InnticketImportPage from './src/vue/Eventportal/innticket/InnticketImportPage.vue';
import InnticketOrderPage from './src/vue/Eventportal/innticket/InnticketOrdersPage.vue';
import InnticketTicketsPage from './src/vue/Eventportal/innticket/InnticketTicketsPage.vue';
import InnticketSettingsPage from './src/vue/Eventportal/innticket/InnticketSettingsPage.vue';

import InnAccessAccessAreasPage from './src/vue/Eventportal/innaccess/InnAccessAccessAreasPage.vue';

import { useEventState } from './src/states';
const { currentEventIdRef } = useEventState();
import { router as log } from './src/loglevel';

// Vue Router
export const router = new VueRouter({
	mode: 'history',
	routes: [
		{ name: 'portalHome', path: '/', component: OrganizerPortalHome, alias: '/vendor' },
		{ name: 'eventEmployeeInvitation', path: '/eventemployee-invitation/:eventEmployeeInvitationId', component: EventEmployeeInvitationPage },
		{ name: 'oDashboard', path: '/organizer/:organizer/', alias: '/organizer/:organizer/dashboard', component: Organizerportal },
		{ name: 'eDashboard', path: '/event/:event/', alias: '/event/:event/dashboard', component: Eventportal, children: [
			{ name: 'eBasedata', path: 'basedata', component: GeneralEventdataPage },
			{ name: 'eEmployee', path: 'employees', component: GeneralEmployeePage },
			{ name: 'eConnectionLogs', path: 'devices', component: DeviceOverviewPage },
			{ name: 'eCashbox', path: 'cashboxes', component: CashboxPage },
			{ name: 'eECTerminal', path: 'ecTerminals', component: EcTerminalPage },
			{ name: 'eOrderDashboard', path: 'innorder/dashboard', component: InnorderDashboardPage },
			{ name: 'eTokenstation', path: 'innorder/tokenstations', component: InnorderTokenstationPage },
			{ name: 'eVendor', path: 'innorder/vendors', component: InnorderVendorPage },
			{ name: 'eOrderSettings', path: 'innorder/settings', component: InnorderSettingsPage },
			{ name: 'eDepositgroup', path: 'innorder/depositgroups', component: InnorderDepositGroupsPage },
			{ name: 'eEvaluation', path: 'innorder/evaluation', component: InnorderStatsPage },
			{ name: 'eTicketDashboard', path: 'innticket/dashboard', component: InnticketDashboardPage },
			{ name: 'eTicket', path: 'innticket/tickets', component: InnticketTicketsPage },
			{ name: 'eTicketImport', path: 'innticket/ticketimport', component: InnticketImportPage },
			{ name: 'eOrders', path: 'innticket/orders', component: InnticketOrderPage },
			{ name: 'eRedemptionGroup', path: 'innticket/redemptiongroups', component: InnticketRedemptionGroupPage },
			{ name: 'eTicketSettings', path: 'innticket/settings', component: InnticketSettingsPage },
			{ name: 'eAccessArea', path: 'innaccess/accessareas', component: InnAccessAccessAreasPage }

		] },
		// { name: 'vArticles', path: '/vendor/:vendor/articles', components: { nav: VendorNav, default: MainArticles }},
		// { name: 'vEmployees', path: '/vendor/:vendor/employees', components: { nav: VendorNav, default: MainStaff }},
		// { name: 'vEvents', path: '/vendor/:vendor/events', components: { nav: VendorNav, default: MainEvents }},
		// { name: 'eArticles', path: '/vendor/:vendor/event/:event/articles', components: { nav: VendorEventNav, default: EventArticlePage }},
		// { name: 'eSalesAreas', path: '/vendor/:vendor/event/:event/salesareas', components: { nav: VendorEventNav, default: SalesAreaPage }},
		// { name: 'eOrders', path: '/vendor/:vendor/event/:event/orders', components: { nav: VendorEventNav, default: OrderPage }},
		{ path: '*', component: PageNotFound }
	]
});

router.beforeEach((toRoute, fromRoute, next) => {
	log.debug('beforeEach Hook, toRoute:', toRoute);
	if (toRoute.path.substr(0, 7) === '/event/' && toRoute.params.event) {
		currentEventIdRef.value = toRoute.params.event;
	} else {
		currentEventIdRef.value = undefined;
	}
	next();
});