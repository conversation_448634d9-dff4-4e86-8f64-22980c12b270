<template>
  <div>
    <b-row>
      <b-col><h2>EC Terminals</h2></b-col>
      <b-col class="d-flex">
        <LoadButton
          class="ml-auto"
          style="height: fit-content"
          size="sm"
          text="EC Terminal erstellen"
          icon="plus-square"
          @click="createTerminal"
        />
      </b-col>
    </b-row>
    <b-table
      hover
      :busy="!terminals"
      :items="terminals"
      :fields="terminalsFields"
      class="mt-3 table-clickable"
      striped
      :show-empty="true"
      empty-text="Keine EC-Terminals vorhanden"
      head-variant="light"
      @row-clicked="openSidebar"
    >
      <template #table-busy>
        <div class="text-center text-primary my-2">
          <b-spinner class="align-middle" />
          <strong>Loading...</strong>
        </div>
      </template>
      <template #cell(currentCredit)="data">
        {{ formatCurrency(data.value) }}
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <DeleteButton
            :data="data"
            size="sm"
            class="ml-auto"
            @click="deleteTerminal"
          />
        </div>
      </template>
    </b-table>
    <ECTerminalSidebar
      ref="terminalSidebar"
      v-model="sidebarVisible"
      :terminal="selectedTerminal"
      :employees="vendorEmployees"
      @reloadData="reloadTerminals"
    />
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadButton from '../buttons/LoadButton.vue';
import DeleteButton from '../buttons/DeleteButton.vue';
import ECTerminalSidebar from './elements/ecterminal/ECTerminalSidebar.vue';

const uuid = require('uuid/v4');

export default {
	components: { LoadButton, DeleteButton, ECTerminalSidebar },
	props: ['selectedEvent', 'selectedVendor'],
	data() {
		return {
			terminals: [],
			terminalsFields: [
				{
					key: 'name',
					label: 'Name',
					sortable: true
				},
				{
					key: 'description',
					label: 'Beschreibung'
				},
				{
					key: 'currentCredit',
					label: 'Credit',
					sortable: true
				},
				{
					key: 'buttons',
					label: ''
				}
			],
			selectedTerminal: null,
			sidebarVisible: false,
			eventEmployees: [],
			vendorEmployees: []
		};
	},
	computed: {},
	watch: {
		sidebarVisible(sidebarVisible) {
			if (!sidebarVisible) this.selectedTerminal = null;
		}
	},
	created() {
		this.reloadTerminals();
		this.reloadVendorEmployees();
		//this.reloadEventEmployees()
	},
	methods: {
		formatCurrency(val) {
			return new Intl.NumberFormat('de-DE', {
				style: 'currency',
				currency: 'EUR'
			}).format(val);
		},
		openSidebar(terminal) {
			this.selectedTerminal = terminal;
			this.sidebarVisible = false;
			this.sidebarVisible = true;
		},
		async deleteTerminal(btn) {
			let item = btn.data.item;
			await this.$refs.terminalSidebar.deleteTerminal(btn);
			this.terminals = this.terminals.filter(
				(terminal) => terminal.id != item.id
			);
		},
		createTerminal(btn) {
			let item = {
				id: uuid(),
				name: 'Neues EC Terminal',
				VendorId: this.selectedVendor.id,
				credit: '0.00',
				VendorEmployees: []
			};
			API.post('core', '/ECTerminal', { body: item })
				.then((response) => {
					this.$bvToast.toast(
						'Das EC Terminal "' + item.name + '" wurde erfolgreich erstellt.',
						{
							title: 'Erstellen Erfolgreich',
							autoHideDelay: 3000,
							variant: 'success'
						}
					);
					this.terminals.push(item);
					btn.reset();
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Erstellen von "' +
              item.name +
              '" ist ein Fehler aufgetreten. Error: ' +
              JSON.stringify(error),
						{
							title: 'Erstellen Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
					btn.reset();
				});
		},
		reloadTerminals() {
			this.terminals = null;
			API.get('core', '/ECTerminal?filterVendor=' + this.selectedVendor.id)
				.then((response) => {
					this.terminals = response;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der EC Terminals ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadEventEmployees() {
			API.get(
				'core',
				'/EventEmployee?include=["PermEventUser"]&filterEvent=' +
          this.selectedEvent.id
			)
				.then((response) => {
					this.eventEmployees = response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der EventEmployees ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		},
		reloadVendorEmployees() {
			API.get(
				'innorder',
				'/VendorEmployee?include=["PermVendorUser"]&filterVendor=' +
          this.selectedVendor.id
			)
				.then((response) => {
					this.vendorEmployees = response.rows;
				})
				.catch((error) => {
					this.$bvToast.toast(
						'Beim Laden der VendorEmployees ist ein Fehler aufgetreten.',
						{
							title: 'Laden Fehlgeschlagen',
							autoHideDelay: 10000,
							variant: 'danger'
						}
					);
				});
		}
	}
};
</script>
<style scoped>
</style>
