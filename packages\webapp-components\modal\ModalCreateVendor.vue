<template>
  <b-modal
    id="modalCreateVendor"
    ref="modalCreateVendor"
    title="Verkäufer erstellen"
    size="lg"
  >
    <b-tabs
      v-model="activeTab"
      content-class="mt-3"
      justified
    >
      <b-tab
        title="Allgemein"
        active
      >
        <b-form @submit="nextTab">
          <b-form-group label="Geschäftsform der Organisation">
            <b-form-select
              v-model="organization.organizationType"
              :options="organizationTypes"
              :state="validOrganizationType"
            />
          </b-form-group>
          <b-form-group
            v-if="organization.organizationType != 'PERSON'"
            label="Name der Organisation"
          >
            <b-form-input
              v-model="organization.organizationName"
              placeholder="Gib den vollständigen Namen der Organisation ein"
              :state="validOrganizationName"
              @keyup.enter="nextTab"
            />
          </b-form-group>
          <b-form-group
            v-if="organization.organizationType == 'PERSON'"
            label="Vorname"
          >
            <b-form-input
              v-model="organization.firstName"
              :state="validFirstName"
            />
          </b-form-group>
          <b-form-group
            v-if="organization.organizationType == 'PERSON'"
            label="Nachname"
          >
            <b-form-input
              v-model="organization.lastName"
              :state="validLastName"
              @keyup.enter="nextTab"
            />
          </b-form-group>
        </b-form>
      </b-tab>
      <b-tab
        :disabled="!generalIsValid"
        title="Adressdaten"
      >
        <div class="form-row">
          <div class="form-label-group col-9">
            <b-form-input
              ref="inputfieldOrganizationStreet"
              v-model="organization.street"
              placeholder="Straße"
              :state="validStreet"
            />
            <label>Straße</label>
          </div>
          <div class="form-label-group col-3">
            <b-form-input
              v-model="organization.housenumber"
              placeholder="Hausnummer"
              :state="validHousenumber"
            />
            <label>Hausnummer</label>
          </div>
        </div>
        <div class="form-row">
          <div class="form-label-group col-3">
            <b-form-input
              v-model="organization.postalcode"
              placeholder="Postleitzahl"
              :state="validPostalcode"
            />
            <label for="input4">Postleitzahl</label>
          </div>
          <div class="form-label-group col-9">
            <b-form-input
              v-model="organization.city"
              placeholder="Stadt"
              :state="validCity"
            />
            <label for="input5">Stadt</label>
          </div>
        </div>
        <div class="form-row">
          <div class="form-label-group col-6">
            <b-form-input
              v-model="organization.country"
              placeholder="Land"
              :state="validCountry"
            />
            <label for="input6">Land</label>
          </div>
          <div class="form-label-group col-6">
            <b-form-input
              v-model="organization.state"
              placeholder="Bundesland"
              :state="validState"
              @keyup.enter="btnCreateVendorClick()"
            />
            <label for="input7">Bundesland</label>
          </div>
        </div>
      </b-tab>
      <!-- <b-tab
        :disabled="!generalIsValid || !addressIsValid"
        title="Module"
      >
        <p>Bitte gewünschte Module auswählen</p>
      </b-tab> -->
    </b-tabs>
    <template #modal-footer="{ ok, cancel }">
      <b-button
        class="mr-auto"
        @click="cancel()"
      >
        Abbrechen
      </b-button>
      <b-button
        v-if="activeTab < 1"
        ref="btnCreateVendorContinue"
        variant="primary"
        :disabled="!nextButtonActive"
        @click="nextTab"
      >
        Weiter
      </b-button>
      <LoadButton
        v-else
        ref="btnCreateVendor"
        variant="primary"
        :disabled="!nextButtonActive"
        text="Verkäufer erstellen"
        @click="onClickCreateVendor"
      />
    </template>
  </b-modal>
</template>
<script>

// Frameworks
import { API } from 'aws-amplify';
import validator from 'validator';

// Vue Components
import LoadButton from '../button/LoadButton.vue';

export default {
	components: { LoadButton },
	props: { 
		linkvendorevent:{ default:false },
		eventid:{ default:null } 
	},
	data() { return {
		activeTab: 0,
		organization: {},
		organizationTypes: [
			{ value: 'PERSON', text: 'Einzelperson' },
			{ value: 'GBR', text: 'GbR' },
			{ value: 'GMBH', text: 'GmbH' },
			{ value: 'UG', text: 'UG' },
			{ value: 'AG', text: 'AG' },
			{ value: 'CLUB', text: 'Verein' },
			{ value: 'OTHER', text: 'Andere' }
		]
	};},
	computed: {
		nextButtonActive() {
			if (this.activeTab == 0) {
				if (this.generalIsValid) return true;
			} else if (this.activeTab == 1) {
				if (this.addressIsValid) return true;
			} else if (this.activeTab == 2) {
				return true;
			}
			else return false;
		},
		validStreet() {
			if (!this.organization.street) return null;
			if (this.organization.street.length < 2) return false;
			if (this.organization.street.length > 20) return false;
			else return true;
		},
		validHousenumber() {
			if (!this.organization.housenumber) return null;
			if (this.organization.housenumber.length > 10) return false;
			else return true;
		},
		validPostalcode() {
			if (!this.organization.postalcode) return null;
			if (this.organization.postalcode.length < 2) return false;
			if (this.organization.postalcode.length > 10) return false;
			if (!validator.isPostalCode(this.organization.postalcode, 'any')) return false;
			else return true;
		},
		validCity() {
			if (!this.organization.city) return null;
			if (this.organization.city.length < 2) return false;
			if (this.organization.city.length > 60) return false;
			else return true;
		},
		validCountry() {
			if (!this.organization.country) return null;
			if (this.organization.country.length < 2) return false;
			if (this.organization.country.length > 60) return false;
			else return true;
		},
		validState() {
			if (!this.organization.state) return null;
			if (this.organization.state.length < 2) return false;
			if (this.organization.state.length > 60) return false;
			else return true;
		},
		validOrganizationType() {
			if (!this.organization.organizationType) return null;
			else return true;
		},
		validOrganizationName() {
			if (!this.organization.organizationName) return null;
			if (this.organization.organizationName.length < 4) return false;
			if (this.organization.organizationName.length > 60) return false;
			else return true;
		},
		validFirstName() {
			if (!this.organization.firstName) return null;
			if (this.organization.firstName.length < 2) return false;
			if (this.organization.firstName.length > 20) return false;
			else return true;
		},
		validLastName() {
			if (!this.organization.lastName) return null;
			if (this.organization.lastName.length < 2) return false;
			if (this.organization.lastName.length > 20) return false;
			else return true;
		},
		generalIsValid() {
			if (!this.validOrganizationType) return false;
			if (this.organization.organizationType == 'PERSON') {
				if (!this.validFirstName || !this.validLastName) return false;
			} else if (!this.validOrganizationName) return false;
			return true;
		},
		addressIsValid() {
			if (!this.validStreet || !this.validHousenumber || !this.validPostalcode || !this.validCity || !this.validCountry || !this.validState) return false;
			return true;
		}
	},
	created() {

	},
	mounted() {
		
	},
	methods: {
		onClickCreateVendor(btn) {
			if (this.linkvendorevent) this.createVendorLinkEvent(btn);
			else this.createOrganization(btn);
		},
		
		btnCreateVendorClick() {
			this.$refs.btnCreateVendor.$el.click();
		},
		createOrganization(btn) {
			API.post('innorder', '/Vendor', { body: this.organization }).then(response  => {
				this.$bvToast.toast('Der Verkäufer wurde erfolgreich erstellt.', {
					title: 'Erstellen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
				this.organization.id = response;
				this.$emit('organizationCreated', this.organization);
				this.organization =  {};
				this.$bvModal.hide('modalCreateVendor');
			}).catch(error => {
				this.$bvToast.toast('Beim Erstellen ist ein Fehler aufgetreten.', {
					title: 'Erstellen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		createVendorLinkEvent(btn) {
			API.post('innorder', '/Vendor/Event/' + this.eventid, { body: this.organization }).then(response  => {
				this.$bvToast.toast('Der Verkäufer wurde erfolgreich erstellt.', {
					title: 'Erstellen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				btn.reset();
				this.organization.id = response;
				this.$emit('organizationCreated', this.organization);
				this.organization =  {};
				this.$bvModal.hide('modalCreateVendor');
			}).catch(error => {
				this.$bvToast.toast('Beim Erstellen ist ein Fehler aufgetreten.', {
					title: 'Erstellen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		nextTab(evt) {
			evt.preventDefault();
			if (this.activeTab == 0) {
				if (this.generalIsValid) {
					this.activeTab++;
					this.$nextTick().then(() => this.$refs.inputfieldOrganizationStreet.focus());
				}
			} 
			
		}
	}
};

</script>
