<template>
  <div>
    <h2>Artikelverwaltung</h2>
    <LoadingSpinner v-if="loading" />
    <ArticleGroup
      v-for="ag in articleGroups"
      :key="ag.id"
      :ag="ag"
      :article-groups="articleGroups"
      :ag-bus="agBus"
      @delArticleGroup="delArticleGroup"
    />
    <div class="d-flex">
      <LoadButton
        class="ml-auto"
        size="sm"
        text="Artikelgruppe hinzufügen"
        icon="plus-square"
        @click="addArticleGroup"
      />
    </div>
  </div>
</template>
<script>
import { API } from 'aws-amplify';
import LoadingSpinner from '../LoadingSpinner.vue';
import ArticleGroup from '../ArticleGroup.vue';
import LoadButton from '../buttons/LoadButton.vue';
import Vue from 'vue';
const uuid = require('uuid/v4');

export default {
	components: { LoadingSpinner, ArticleGroup, LoadButton },
	props: ['selectedVendor'],
	data() { return {
		articleGroups: [],
		loading: true,
		agBus: new Vue()
	};},
	created() {
		API.get('innorder', '/ArticleGroup?filterVendor=' + this.selectedVendor.id + '&filterGlobal=true').then(response => {
			this.articleGroups = response;
			this.loading = false;
		}).catch(error => {
			this.$bvToast.toast('Beim Laden der Artikelgruppen ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
				title: 'Laden Fehlgeschlagen',
				autoHideDelay: 10000,
				variant: 'danger'
			});
		});
	},
	methods:{
		addArticleGroup(btn) {
			let item = {
				id:uuid(),
				VendorId: this.selectedVendor.id,
				active: true,
				color: null,
				description: '',
				EventId: null,
				name: 'Neue Artikelgruppe'
			};
			API.post('innorder', '/ArticleGroup/', { body: item }).then(response  => {
				this.$bvToast.toast('Die Artikelgruppe "' + item.name + '" wurde erfolgreich erstellt.', {
					title: 'Erstellen Erfolgreich',
					autoHideDelay: 3000,
					variant: 'success'
				});
				this.articleGroups.push(item);
				btn.reset();
			}).catch(error => {
				this.$bvToast.toast('Beim Erstellen von "' + item.name + '" ist ein Fehler aufgetreten. Error: ' + JSON.stringify(error), {
					title: 'Erstellen Fehlgeschlagen',
					autoHideDelay: 10000,
					variant: 'danger'
				});
				btn.reset();
			});
		},
		delArticleGroup(articleGroup) {
			this.$bvToast.toast('Die ArtikelGruppe "' + articleGroup.name + '" wurde erfolgreich gelöscht.', {
				title: 'Löschen Erfolgreich',
				autoHideDelay: 3000,
				variant: 'success'
			});
			// Remove from Local Array
			for (let i = 0; i < this.articleGroups.length; i++) {
				if (this.articleGroups[i].id == articleGroup.id) {
					this.$delete(this.articleGroups, i);
				}
			}
		}
	}
};
</script>
<style scoped>

</style>
