<template>
  <div>
    <CashboxTable />
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { useCashboxState } from '../../../states';
import { currentEventRef } from '../../../states/eventState';
import CashboxTable from './elements/CashboxTable.vue';


export default defineComponent({
	components: { CashboxTable },
	setup() {
		const { loadCashboxes } = useCashboxState();
		
		onMounted(async ()  => {
			await loadCashboxes({
				key: { eventId: currentEventRef.value!.eventId! }
			}); 
		});

		return {};
	}
});
</script>