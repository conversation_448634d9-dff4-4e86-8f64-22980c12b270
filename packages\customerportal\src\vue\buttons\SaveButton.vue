<template>
  <b-button
    squared
    :size="size"
    variant="success"
    @click.stop="click"
    :disabled="disabled"
    :block="block"
  ><b-spinner small v-if="state=='loading'"></b-spinner>
  <FontAwesomeIcon v-if="state!='loading'" icon='save'></FontAwesomeIcon> {{btnText}}</b-button>
</template>
<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export default {
  data(){ return{
    state: 'initial',
    btnText: 'Speichern'
  }},
  props: {
    data: Object,
    block: { default: false },
    size: { type: String, default: '' },
    text: { type: String, default: 'Speichern' }
  },
  created(){
    this.btnText = this.text
  },
  components: { FontAwesomeIcon },
  computed:{
    disabled(){
      if(this.state == 'loading'){
        return true
      }else{
        return false
      }
    }
  },
  methods: {
    click(){
      if(this.state == 'initial'){
        this.btnText = "Loading..."
        this.state = "loading"
        this.$emit('click', this)
      }
    },
    reset(){
      this.state = "initial",
      this.btnText = this.text
    }
  }
}
</script>
