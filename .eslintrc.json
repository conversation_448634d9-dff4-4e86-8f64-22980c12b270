{
    "env": {
        "browser": true,
        "es2021": true,
        "node": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:vue/vue3-essential",
        "plugin:vue/vue3-strongly-recommended",
        "plugin:vue/vue3-recommended"
        // 'plugin:vue/recommended' // Use this if you are using Vue.js 2.x.
    ],
    "parser": "vue-eslint-parser", // Default <PERSON>rser
    "parserOptions": {
        "ecmaVersion": 12,
        "sourceType": "module",
        "parser": "@typescript-eslint/parser" // Parser for Script Tags
    },
    "plugins": [
        "vue",
        "@typescript-eslint",
        "unused-imports"
    ],
    "ignorePatterns": "*.js",
    "overrides": [
        {
            "files": [
                "*.ts"
            ],
            "parser": "@typescript-eslint/parser"
        }
    ],
    "rules": {
        "max-len": ["error", {
            "code": 150
        }],
        "indent": [
            "error",
            "tab"
        ],
        "key-spacing": ["warn", {
            "beforeColon": false,
            "afterColon": true,
            "mode": "strict"
        }],
        "@typescript-eslint/type-annotation-spacing": "warn",
        "@typescript-eslint/no-non-null-assertion": "off",
        "linebreak-style": "off",
        "quotes": [
            "error",
            "single"
        ],
        "semi": [
            "error",
            "always"
        ],
        "prefer-rest-params": "off",
        "@typescript-eslint/no-inferrable-types": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "no-var": "error",
        "id-length": [
            "warn",
            {
                "min": 3
            }
        ],
        "no-multiple-empty-lines": [
            "warn",
            {
                "max": 2,
                "maxEOF": 2,
                "maxBOF": 2
            }
        ],
        "space-before-blocks": "warn",
        "@typescript-eslint/naming-convention": [
            "warn",
            {
                "selector": "default",
                "format": [
                    "strictCamelCase"
                ],
                "leadingUnderscore": "forbid",
                "trailingUnderscore": "forbid"
            },
            {
                "selector": "typeLike",
                "format": [
                    "StrictPascalCase"
                ],
                "leadingUnderscore": "forbid",
                "trailingUnderscore": "forbid"
            }
        ],
        "space-before-function-paren": [
            "error",
            {
                "anonymous": "never",
                "named": "never",
                "asyncArrow": "always"
            }
        ],
        "@typescript-eslint/consistent-type-imports": [
            "warn",
            {
                "prefer": "type-imports",
                "disallowTypeAnnotations": true
            }
        ],
        "@typescript-eslint/member-delimiter-style": [
            "error",
            {
                "multiline": {
                    "delimiter": "semi",
                    "requireLast": true
                },
                "singleline": {
                    "delimiter": "semi",
                    "requireLast": true
                }
            }
        ],
        "comma-dangle": [
            "error",
            {
                "arrays": "never",
                "objects": "never",
                "imports": "never",
                "exports": "never",
                "functions": "never"
            }
        ],
        "keyword-spacing": "off",
        "@typescript-eslint/keyword-spacing": [
            "warn"
        ],
        "@typescript-eslint/ban-ts-comment": "off",
        "space-in-parens": [
            "warn",
            "never"
        ],
        "space-infix-ops": "warn",
        "@typescript-eslint/space-infix-ops":"warn",
        "comma-spacing": "warn",
        "vue/component-name-in-template-casing": [
            "error",
            "PascalCase",
            {
                "registeredComponentsOnly": true,
                "ignores": []
            }
        ],
        "vue/camelcase": "warn",
        "vue/attribute-hyphenation": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "warn",
        "unused-imports/no-unused-vars": [
            "warn",
            {
                "vars": "all",
                "varsIgnorePattern": "^_",
                "args": "after-used",
                "argsIgnorePattern": "^_"
            }
        ],
        "object-curly-spacing": "off",
        "@typescript-eslint/object-curly-spacing": [
            "warn",
            "always"
        ],
        "require-await": "off",
        "@typescript-eslint/no-explicit-any": "off"
        // "@typescript-eslint/require-await": "error" 
        // "@typescript-eslint/await-thenable": "error" 
    }
}