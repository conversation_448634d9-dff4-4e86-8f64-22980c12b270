<template>
  <b-list-group-item class="flex-column align-items-start">
    <div class="d-flex justify-content-between mb-3">
      <h5 class="mb-1">
        {{ textHeadline }}
      </h5>
      <h6>{{ formatedDate }}</h6>
    </div>
    <div class="d-flex flex-wrap  justify-content-end">
      <div class="flex-grow-1">
        <p>Ort: {{ textLocation }}</p>
        <p v-if="textPaymentMethod">
          Bezahlmethode: {{ textPaymentMethod }}
        </p>
        <template v-if="topUp && topUp.newIdentifier">
          <p v-if="topUp.depositCreditChange">
            Pfandbuchung: {{ formatCurrency(topUp.depositCreditChange) }}
          </p>
        </template>
        <template v-if="topDown">
          <p v-if="topDown.identifierReturn">
            Karte/Chip zurückgegeben
          </p>
          <p v-if="topDown.depositCreditChange">
            Pfandbuchung: {{ formatCurrency(topDown.depositCreditChange) }}
          </p>
          <p v-if="topDown.payoutCreditRound">
            Auszahlung: {{ formatCurrency(topDown.payoutCreditRound) }}
          </p>
        </template>
        <b-table
          v-if="transaction.type == 'ORDER' || transaction.type == 'CASHLESSORDER'"
          small
          borderless
          outlined
          striped
          :items="transaction.Order.OrderPositions"
          :fields="orderPositionFields"
        >
          <template #cell(name)="data">
            <span>{{ data.value }}</span><br>
            <small
              v-if="data.item.type == 'ARTICLE'"
              class="text-primary"
            >Artikel Kauf</small>
            <small
              v-else-if="data.item.type == 'DEPOSIT'"
              class="text-primary"
            >Pfand Ausgabe</small>
            <small
              v-else-if="data.item.type == 'DEPOSITRETURN'"
              class="text-primary"
            >Pfand Rückgabe</small>
          </template>
          <template #cell(costGross)="data">
            <span>{{ formatCurrency(data.value) }}</span><br>
            <small class="text-primary">inkl. {{ data.item.vat }}% Mwst</small>
          </template>
          <template #cell(costGrossSum)="data">
            <span>{{ formatCurrency(data.value) }}</span><br>
            <small class="text-primary">inkl. {{ data.item.vat }}% Mwst</small>
          </template>
        </b-table>
      </div>
      <b-table-simple
        class="w-auto ml-5 align-self-end"
        small
      >
        <b-tr>
          <b-th>Guthaben vorher</b-th>
          <b-td>{{ formatCurrency(transaction.creditBefore) }}</b-td>
        </b-tr>
        <b-tr>
          <b-th>Guthaben Änderung</b-th>
          <b-td>{{ formatCurrency(transaction.creditChange) }}</b-td>
        </b-tr>
        <b-tr>
          <b-th>Guthaben nachher</b-th>
          <b-td>{{ formatCurrency(transaction.creditAfter) }}</b-td>
        </b-tr>
      </b-table-simple>
    </div>
  </b-list-group-item>
</template>
<script>

// Frameworks
let validator = require('validator');

// Vue Components
// import Navigation from './Eventportal/Navigation.vue'

export default {
	components: { },
	props: ['transaction'],
	data() { return {
		orderPositionFields: [
			{
				key: 'name',
				label: 'Name'
			}, {
				key: 'costGross',
				label: 'Preis'
			}, {
				key: 'amount',
				label: 'Anz.'
			}, {
				key: 'costGrossSum',
				label: 'Summe'
			}
		]
	};},
	computed: {
		textPaymentMethod() {
			switch (this.transaction.paymentMethod) {
			case 'CASH': return 'Bargeld';
			case 'CARD': return 'Bankkarte';
			case 'INNTICKET': return 'Ticket eingelöst';
			case 'CORRECTION': return 'Korrektur';
			case 'INNCASHLESS': return 'Tokenkarte/Wristband';
			case 'PAYPAL': return 'Paypal';
			default: return '';
			}
		},
		textHeadline() {
			if (['ORDER', 'CASHLESSORDER'].includes(this.transaction.type)) return 'Bestellung';
			else if (this.transaction.type == 'TOPUP') return 'Aufladung';
			else if (this.transaction.type == 'TOPDOWN') return 'Abbuchung';
			else return 'Andere Transaction';
		},
		textLocation() {
			try {
				if (['ORDER', 'CASHLESSORDER'].includes(this.transaction.type)) return this.transaction.Order.SalesArea.name;
				else return this.transaction.InnOrderTransactionCashless.Tokenstation.name;
			} catch (err) {
				return 'unbekannt';
			}
		},
		topUp() {
			try {
				return this.transaction.InnOrderTransactionCashless.TopUp;
			} catch (err) {
				return null;
			}
		},
		topDown() {
			try {
				return this.transaction.InnOrderTransactionCashless.TopDown;
			} catch (err) {
				return null;
			}
		},
		formatedDate() {
			return new Date(this.transaction.builtAt).toLocaleString();
		}
	},
	watch: {

	},
	created() {

	},
	methods: {
		formatCurrency(val) {
			return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(val);
		}
	}
};

</script>
<style scoped>

</style>
