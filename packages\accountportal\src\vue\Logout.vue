<template>
  <b-row class="justify-content-center mt-5">
    <div class="innLoginForm">
      <b-alert v-if="successfull" show variant="success">Erfolgreich abgemeldet</b-alert>
      <b-button v-if="successfull" variant="outline-primary" to="/login" block><PERSON><PERSON><PERSON></b-button>
    </div>
  </b-row>
</template>
<script>


import { Auth } from 'aws-amplify'
import URL from 'url-parse'

export default {
  data() { return{
    cognitoUser: null,
    successfull: false,
    url: null
  }},
  async created(){
    this.url = new URL(window.location.href, true)
    await Auth.currentAuthenticatedUser().then(user => {
      this.cognitoUser = user
      console.log("User logged in")
      this.signOut()
    }).catch(err => {
      console.log("User signed out")
      this.success()
    })
  },
  methods: {
    async signOut(){
      try {
        await Auth.signOut()
        this.success()
      } catch (error) {
        if(error.code == 'NotAuthorizedException') this.success()
        else console.log(error)
      }
    },
    async success(){
      this.successfull = true
      if(this.url.query.redirect_uri){
        var urlRedirect = buildUrl(this.url.query.redirect_uri, {})
        window.location.replace(urlRedirect)
      }
    }
  }
}
</script>
<style scoped>

</style>
