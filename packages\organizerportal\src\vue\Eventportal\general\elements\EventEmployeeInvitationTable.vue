<template>
  <div>
    <b-row>
      <b-col><h2>Mitarbeitereinladungen</h2></b-col>
      <b-col class="d-flex">
        <b-button
          class="ml-auto"
          variant="primary"
          size="sm"
          @click="openInvitationModal"
        >
          <FontAwesomeIcon icon="plus-square" />
          Mi<PERSON>beiter einladen
        </b-button>
      </b-col>
    </b-row>
    <label>Filter nach Status</label>
    <Multiselect
      v-model="filterSelection"
      :options="filterOptions"
      :multiple="true"
      placeholder="Auswählen"
      label="name"
      track-by="name"
      :allow-empty="false"
      :preselect-first="true"
    />
    <b-table
      id="tableEventEmployeesInvitationRef"
      ref="tableEventEmployeesInvitationRef"
      hover
      :items="filterInvitationsByStatus"
      :fields="tableFields"
      :busy="isLoadingRef"
      :per-page="paginationEventEmployeeInvitation.perPage"
      :current-page="paginationEventEmployeeInvitation.currentPage"
      class="mt-3 table-clickable"
      head-variant="light"
    >
      <template #table-busy>
        <TableBusyLoader />
      </template>
      <template #cell(createdOn)="data">
        {{ formatDate(data.value) }}
      </template>
      <template #cell(status)="{value}">
        <b-badge
          :variant="invitationStatus[value].variant"
        >
          {{ invitationStatus[value].name }}
        </b-badge>
      </template>
      <template #cell(buttons)="data">
        <div class="d-flex justify-content-end">
          <LoadButton
            v-if="data.item.status == 'CREATED'"
            variant="primary"
            class="mr-2"
            text="Erneut versenden"
            @click="resendInvitation($event, data.item)"
          />
          <DeleteButton
            size="sm"
            class=""
            icon="trash"
            text="Entfernen"
            variant="danger"
            @click="deleteInvitation($event, data.item)"
          />
        </div>
      </template>
    </b-table>
    <b-pagination
      v-model="paginationEventEmployeeInvitation.currentPage"
      :per-page="paginationEventEmployeeInvitation.perPage"
      :total-rows="paginationEventEmployeeInvitation.totalRows"
      align="center"
    />
    <EventEmployeeInvitationModal 
      ref="eventEmployeeInvitationModalRef"
    />
  </div>
</template>
<script lang="ts">
import LoadButton from '../../../buttons/LoadButton.vue';
import DeleteButton from '../../../buttons/DeleteButton.vue';
import { defineComponent, ref, getCurrentInstance, computed } from '@vue/composition-api';
import { useEventEmployeeInvitationState, 
	deleteEventEmployeeInvitation } from '../../../../states/eventEmployeeInvitationState';
import type { BTable } from 'bootstrap-vue';
import type { EventEmployeeInvitation } from '@innevent/types';
import TableBusyLoader from '@innevent/webapp-components/TableBusyLoader.vue';
import { formatDate } from '@innevent/webapp-utils';
import { notifyError, notifySuccess } from '@innevent/webapp-utils';
import Multiselect from 'vue-multiselect';
import EventEmployeeInvitationModal from './EventEmployeeInvitationModal.vue';
import { eventEmployeeInvitation as api } from '@innevent/webapp-api'; 
import type { ResetButton } from '@innevent/webapp-types';

export default defineComponent({
	components: {
		DeleteButton,
		TableBusyLoader,
		Multiselect,
		EventEmployeeInvitationModal,
		LoadButton
	},
	setup() {
		const instance = getCurrentInstance();
		const { eventEmployeeInvitationsRef, isLoadingRef } = useEventEmployeeInvitationState();
		const tableEventEmployeesInvitationRef = ref<BTable>();
		const eventEmployeeInvitationModalRef = ref<InstanceType<typeof EventEmployeeInvitationModal>>();

		const paginationEventEmployeeInvitation = ref({
			totalRows: eventEmployeeInvitationsRef.value.length,
			perPage: 10,
			currentPage: 1
		});
		const filterOptions = [
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		];
		const filterSelection = ref([
			{ name: 'Akzeptiert', key: 'ACCEPTED' },
			{ name: 'Versendet', key: 'CREATED' },
			{ name: 'Abgelehnt', key: 'DECLINED' }
		]);

		const invitationStatus = {
			CREATED: {
				name: 'Versendet',
				variant: 'secondary'
			}, 
			ACCEPTED: {
				name: 'Akzeptiert',
				variant: 'success'
			}, 
			DECLINED: {
				name: 'Abgelehnt',
				variant: 'danger'
			}
		};

		const tableFields = [
			{ key: 'email', label: 'Email' },
			{ key: 'status', label: 'Status', sortable: true },
			{ key: 'message', label: 'Nachricht' },
			{ key: 'createdOn', label: 'Erstellt am', sortable: true },
			{ key: 'buttons', label: '' }
		];

		async function deleteInvitation(btn: ResetButton, dataItem: EventEmployeeInvitation) {
			try {
				await deleteEventEmployeeInvitation(dataItem.eventEmployeeInvitationId!);
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		const filterInvitationsByStatus = computed(() =>{
			const filterSelectionList = filterSelection.value.map((selection) => selection.key);
			return eventEmployeeInvitationsRef.value.filter((invitation) => filterSelectionList.includes(invitation.status!));
		});

		function openInvitationModal() {
			eventEmployeeInvitationModalRef.value?.openModal();
		}

		async function resendInvitation(btn, dataItem: EventEmployeeInvitation) {
			try {
				await api.resendInvitation({
					key: {
						eventId: dataItem.eventId,
						eventEmployeeInvitationId: dataItem.eventEmployeeInvitationId!
					}
				});
				notifySuccess({ instance });
			} catch (error: any) {
				notifyError({ instance, error });
			}
			btn.reset();
		}

		return {
			tableEventEmployeesInvitationRef,
			eventEmployeeInvitationsRef,
			tableFields,
			isLoadingRef,
			deleteEventEmployeeInvitation,
			paginationEventEmployeeInvitation,
			invitationStatus,
			formatDate,
			deleteInvitation,
			filterInvitationsByStatus,
			filterSelection,
			filterOptions,
			openInvitationModal,
			eventEmployeeInvitationModalRef,
			resendInvitation
		};
	}
});
</script>