<template>
  <div>
    <b-table
      striped
      hover
      :busy="busy"
      :items="ticketValueMappingRef"
      :fields="tableFields"
    >
      <template #cell(innTicketIdentifier)="{item}">
        <Multiselect
          v-model="item.selectedTicket"
          placeholder="Ticket auswählen"
          :show-labels="false"
          label="ticketName"
          :options="ticketMapRef.tickets"
          style="min-width: 350px"
          trackBy="ticketId"
          :allowEmpty="true"
          @input="onInputTicketSelect(item)"
        />
        <Multiselect
          v-model="item.selectedSalesPeriod"
          placeholder="Verkaufsphase auswählen"
          :show-labels="false"
          :custom-label="getSalesPeriodLabel"
          :options="getTicketSaleProperties(item.selectedTicket)"
          style="min-width: 350px"
          trackBy="salesPeriodId"
          :allowEmpty="true"
        >
          <template #option="{option}">
            <h5>{{ option.salesPeriod.salesPeriodName }}</h5>
            <div>{{ `Von: ${formatDate(option.salesPeriod.timeFrom)}` }}</div>
            <div>{{ `Bis: ${formatDate(option.salesPeriod.timeTo)}` }}</div>
            <div>{{ `Preis: ${formatPrice(option.price)}` }}</div>
            <div>{{ `Kontingent: ${option.contingent} Stück` }}</div>
          </template>
        </Multiselect>
      </template>
    </b-table>
    <LoadButton
      class="float-right mt-2"
      variant="primary"
      text="Ticketmapping speichern"
      :loading="isLoadingRef"
      @click="saveTicketMapping"
    />
  </div>
</template>
<script lang="ts">
/* eslint-disable @typescript-eslint/naming-convention */
import type { PropType } from '@vue/composition-api';
import { onMounted } from '@vue/composition-api';
import { ref } from '@vue/composition-api';
import { defineComponent, getCurrentInstance } from '@vue/composition-api';
import Multiselect from 'vue-multiselect';
import type { Ticket, TicketImport, TicketSaleProperty, ValueMappingItem } from '@innevent/types';
import { notifyError, formatDate, formatPrice, notifySuccess } from '@innevent/webapp-utils';
import { useTicketMapState } from '../../../../states/ticketMap';
import type { SalesPeriod } from '@innevent/webapp-types';
import LoadButton from '@innevent/webapp-components/button/LoadButtonV2.vue';
import Vue from 'vue';
import { innTicketImport as log } from '../../../../loglevel';
import * as api from '@innevent/webapp-api';


type TicketSalePropertyWithSalesPeriod = TicketSaleProperty & {
	salesPeriod: Partial<SalesPeriod> & Pick<SalesPeriod, 'salesPeriodName'>;
}

type ValueMappingItemWithObjects = Pick<ValueMappingItem, 'importValue' | 'innTicketIdentifier'> & {
	selectedTicket: Ticket | undefined;
	selectedSalesPeriod: TicketSaleProperty | undefined;
}

export default defineComponent({
	components: {
		Multiselect,
		LoadButton
	},
	props: {
		value: {
			type: Object as PropType<TicketImport>,
			required: true
		},
		busy: {
			type: Boolean,
			required: false
		}
	},
	emits: ['input'],
	setup(props, { emit }) {
		const instance = getCurrentInstance();
		const { ticketMapRef } = useTicketMapState();
		const isLoadingRef = ref(false);

		let ticketImportInput: TicketImport = JSON.parse(JSON.stringify(props.value));

		const tableFields = [{
			label: 'Ticket Name (Import)',
			key: 'importValue',
			sortable: true
		}, {
			label: 'Zuweisung InnTicket',
			key: 'innTicketIdentifier'
		}];

		const ticketValueMappingRef = ref<ValueMappingItemWithObjects[]>([]);

		function refreshTicketValueMapping(ticketImport?: TicketImport) {
			if (ticketImport) {
				ticketImportInput = JSON.parse(JSON.stringify(ticketImport));
			}
			if (ticketImportInput.fieldMapping.length < 1 || Object.keys(ticketImportInput.columns).length < 1) {
				return;
			}

			log.info('run refreshTicketValueMapping()');
			if (!ticketImportInput.fieldMapping) {
				log.debug('no ticketImportInput.fieldMapping found');
				ticketValueMappingRef.value = [];
				return;
			}
			// Search for generatedTicketName columns fieldMappings
			const fieldMappingsTicket = Object.values(ticketImportInput.fieldMapping).filter(fieldMappingItem => 
				fieldMappingItem.innTicketField === 'ticket.generatedTicketName');
			if (fieldMappingsTicket.length > 1) {
				log.debug('fieldMappingsTicket.length > 1');
				notifyError({ instance, message: 'generatedTicketName is mapped multiple!' });
				ticketValueMappingRef.value = [];
				return;
			}
			else if (fieldMappingsTicket.length === 0) {
				log.debug('fieldMappingsTicket.length === 0');
				notifyError({ instance, message: 'generatedTicketName is not mapped!' }); 
				ticketValueMappingRef.value = [];
				return;
			}

			log.debug('map valueMappingItems');
			ticketValueMappingRef.value = ticketImportInput.valueMapping.generatedTicketName.map(valueMappingItem => {
				const allTickets = ticketMapRef.value.tickets as Ticket[];
				const selectedTicket: Ticket | undefined = allTickets.find(ticket => ticket.ticketId === valueMappingItem.ticketId);
				const selectedSalesPeriod: TicketSaleProperty | undefined = getTicketSaleProperties(selectedTicket).find(saleProperty => 
					saleProperty.salesPeriodId === valueMappingItem.salesPeriodId);
				return {
					selectedTicket,
					selectedSalesPeriod,
					importValue: valueMappingItem.importValue,
					innTicketIdentifier: valueMappingItem.innTicketIdentifier
				};
			});
		}

		onMounted(async () => {
			await refreshTicketValueMapping();
		});

		function getTicketSaleProperties(ticket?: Ticket): TicketSalePropertyWithSalesPeriod[] {
			if (!ticket) return [];
			return Object.values(ticket.ticketSaleProperties).map(saleProperty => {
				const salesPeriod: SalesPeriod | undefined = ticketMapRef.value.salesPeriods.find(salesPeriod =>
					salesPeriod.salesPeriodId === saleProperty.salesPeriodId);
				const reuslt = {
					...saleProperty,
					salesPeriod: salesPeriod ?? {
						salesPeriodName: 'Unknown SalesPeriod'
					}
				};
				return reuslt;
			});
		}

		function getSalesPeriodLabel(saleProperty: TicketSalePropertyWithSalesPeriod): string {
			return saleProperty.salesPeriod.salesPeriodName;
		}

		async function saveTicketMapping() {
			try {
				isLoadingRef.value = true;
				const ticketImportResponse: TicketImport = props.value;
				const newValueMapping = ticketValueMappingRef.value.map(mappingItem => {
					return {
						importValue: mappingItem.importValue,
						ticketId: mappingItem.selectedTicket?.ticketId,
						salesPeriodId: mappingItem.selectedSalesPeriod?.salesPeriodId
					};
				});
				await api.updateTicketImportValueMapping({
					key: {
						ticketImportId: ticketImportResponse.ticketImportId,
						eventId: ticketImportResponse.eventId,
						mappingAttribute: 'generatedTicketName'
					},
					data: newValueMapping
				});
				ticketImportResponse.valueMapping.generatedTicketName = newValueMapping;
				emit('input', ticketImportResponse);
				notifySuccess({ instance, title: 'Ticket-Mapping gespeichert' });
			} catch (error) {
				notifyError({ instance, message: 'Fehler beim Speichern' });
			}
			isLoadingRef.value = false;
		}

		function onInputTicketSelect(item) {
			Vue.set(item, 'selectedSalesPeriod', undefined);
		}

		return {
			tableFields,
			ticketValueMappingRef,
			ticketMapRef,
			getTicketSaleProperties,
			getSalesPeriodLabel,
			saveTicketMapping,
			onInputTicketSelect,
			formatDate,
			formatPrice,
			refreshTicketValueMapping,
			isLoadingRef
		};
	}
});
</script>