import type { Event } from '@innevent/types';
import { apiInnEvent } from './instances';

type ModelType = Event;
type PrimaryKey = 'eventId';

export async function getPermittetEvents(): Promise<ModelType[]> {
	const response = await (await apiInnEvent()).get('/Event');
	return response.data;
}

export type EventCreateOptions = {
	data: Pick<ModelType, 'description' | 'name' | 'organizerId' | 'settings' | 'timeFrom' | 'timeTo'>;
}
export async function createEvent(options: EventCreateOptions): Promise<Required<ModelType>> {
	const response = await (await apiInnEvent()).post('/Event', options.data, {
		params: {
			returnValue: 'ALL_NEW'
		}
	});
	return response.data;
}

export type EventDeleteOptions = {
	key: Pick<ModelType, 'eventId'>;
}
export async function deleteEvent(options: EventDeleteOptions): Promise<void> {
	await (await apiInnEvent()).delete(`/Event/${options.key.eventId}`, {
		params: {
			eventId: options.key.eventId
		}
	});
}

export type EventUpdateOptions = {
	key: Pick<ModelType, 'eventId'>;
	data: Partial<Pick<ModelType, 'name' | 'description' | 'timeFrom' | 'timeTo' | 'settings'>>;
}
export async function updateEvent(options: EventUpdateOptions): Promise<ModelType> {
	const response = await (await apiInnEvent()).patch(`/Event/${options.key.eventId}`, options.data, {
		params: {
			eventId: options.key.eventId,
			returnValue: 'ALL_NEW'
		}
	});
	return response.data;
}